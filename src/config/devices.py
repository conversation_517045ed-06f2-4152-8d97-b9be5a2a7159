caps_weee_ios = {
    "platformName": "iOS",
    "appium:platformVersion": "15.0.2",
    "appium:deviceName": "iPhone 12",
    "appium:app": "com.differentsocial.Weee",
    "appium:udid": "00008101-001138D60190001E",
    "appium:automationName": "XCUITest",
    "appium:autoAcceptAlerts": True,
    "appium:includeSafariInWebviews": True,
    "appium:newCommandTimeout": 3600,
    "appium:connectHardwareKeyboard": True,
    "startIWDP": True,

}

caps_weee_android = {
    "platformName": "Android",
    "appium:platformVersion": "12",
    "appium:deviceName": "8BCX17X5T",
    "appium:appPackage": "com.sayweee.weee",
    # "appium:app": r"D:\software\Weee!_20.14.apk",
    # "appium:appActivity": ".IconPlaceholder2",
    "appium:appActivity": ".module.launch.SplashActivity",
    "appium:automationName": "UiAutomator2",
    "appium:unicodeKeyboard": True,
    "appium:restKeyboard": True,
    "appium:noReset": True
}

caps_ios_onboarding = {
 "platformName": "iOS",
 "appium:platformVersion": "15.0.2",
 "appium:deviceName": "iPhone 12",
 "appium:app": '/Users/<USER>/weee.ipa',
 "appium:udid": "00008101-001138D60190001E",
 "appium:automationName": "XCUITest",
 "appium:autoAcceptAlerts": True,
 "appium:includeSafariInWebviews": True,
 "appium:newCommandTimeout": 3600,
 "appium:connectHardwareKeyboard": True,
 "startIWDP": True
 }

caps_android_onboarding = {
    "platformName": "Android",
    "appium:platformVersion": "12",
    "appium:deviceName": "8BCX17X5T",
    # "appium:appPackage": "com.sayweee.weee",
    # "appium:app": r"D:\software\Weee!_20.14.apk",
    "appium:app": r"D:\software\Weee!_20.16.apk",
    # "appium:appActivity": ".IconPlaceholder2",
    "appium:appActivity": ".module.launch.SplashActivity",
    "appium:automationName": "UiAutomator2",
    "appium:unicodeKeyboard": "true",
    "appium:restKeyboard": "true",
}

caps_preferences_ios = {
    "platformName": 'iOS',
    "appium:platformVersion": "15.0.2",
    "appium:deviceName": "iPhone 12",
    "appium:app": "com.apple.Preferences",
    "appium:udid": "00008101-001138D60190001E",
    "appium:automationName": "XCUITest",
    "appium:autoAcceptAlerts": True,
    "appium:includeSafariInWebviews": True,
    "appium:newCommandTimeout": 3600,
    "appium:connectHardwareKeyboard": True,
    "startIWDP": True
}
