{"uuid": "b7bd1f2e-ef0c-4091-9011-c260f5d525c3", "children": ["943a0a38-cc55-46f8-b51d-8da70a3895ff"], "befores": [{"name": "get_driver", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/pluggy/_callers.py\", line 77, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/_pytest/fixtures.py\", line 1123, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/_pytest/fixtures.py\", line 895, in call_fixture_func\n    fixture_result = next(generator)\n                     ^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/src/EC/conftest.py\", line 132, in get_driver\n    driver = DeviceManager(os.getenv(\"platform\", \"Android\")).initialize_driver(is_installed=True)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/src/lib/device_manger.py\", line 33, in initialize_driver\n    self.driver = webdriver.Remote(\n                  ^^^^^^^^^\n  File \"_pydevd_bundle/pydevd_pep_669_tracing_cython.pyx\", line 554, in _pydevd_bundle.pydevd_pep_669_tracing_cython.py_line_callback\n  File \"_pydevd_bundle/pydevd_pep_669_tracing_cython.pyx\", line 547, in _pydevd_bundle.pydevd_pep_669_tracing_cython.py_line_callback\n  File \"/Applications/PyCharm CE.app/Contents/plugins/python-ce/helpers/pydev/pydevd.py\", line 1220, in do_wait_suspend\n    self._do_wait_suspend(thread, frame, event, arg, suspend_type, from_this_thread)\n  File \"/Applications/PyCharm CE.app/Contents/plugins/python-ce/helpers/pydev/pydevd.py\", line 1235, in _do_wait_suspend\n    time.sleep(0.01)\n"}, "start": 1748414578664, "stop": 1748414590647}], "start": 1748414578664, "stop": 1748414591053}