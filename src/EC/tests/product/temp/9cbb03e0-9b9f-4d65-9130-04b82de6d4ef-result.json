{"name": "点击See All跳转测试", "status": "passed", "description": "\n        测试步骤：\n        1. 进入产品详情页\n        2. 点击See All\n        3. 验证是否跳转到Explore页面并展示全部视频\n        ", "parameters": [{"name": "setup", "value": "('test_003_see_all_navigation',)"}], "start": 1749202379880, "stop": 1749202425906, "uuid": "82048fde-97ef-499b-8f76-c03a27574014", "historyId": "6debfaa1ce2119d87b51aa5545367250", "testCaseId": "935bf7b52fd648fb8d939976c54fad41", "fullName": "src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures#test_112812_see_all_navigation", "labels": [{"name": "story", "value": "iOS端PDP视频功能测试"}, {"name": "tag", "value": "pdp_video"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.product"}, {"name": "suite", "value": "test_112812_pdp_video_features"}, {"name": "subSuite", "value": "TestPDPVideoFeatures"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "64999-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.product.test_112812_pdp_video_features"}]}