{"uuid": "23783c04-7050-42e2-9dcb-01485e502f7c", "children": ["4246bbc4-1f3f-4ab7-94f0-39daf9a528ab"], "befores": [{"name": "get_driver", "status": "broken", "statusDetails": {"message": "selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: The application at 'D:\\software\\Weee!_20.10.apk' does not exist or is not accessible\nStacktrace:\nUnknownError: An unknown server-side error occurred while processing the command. Original error: The application at 'D:\\software\\Weee!_20.10.apk' does not exist or is not accessible\n    at getResponseForW3CError (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/errors.js:1118:9)\n    at asyncHandler (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/protocol.js:491:57)\n", "trace": "  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/pluggy/_callers.py\", line 77, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/_pytest/fixtures.py\", line 1123, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/_pytest/fixtures.py\", line 895, in call_fixture_func\n    fixture_result = next(generator)\n                     ^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android/src/EC/conftest.py\", line 91, in get_driver\n    driver = DeviceManager(os.getenv(\"platform\", \"Android\")).initialize_driver()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android/src/lib/device_manger.py\", line 26, in initialize_driver\n    self.driver = webdriver.Remote(\n                  ^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/appium/webdriver/webdriver.py\", line 229, in __init__\n    super().__init__(\n  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py\", line 209, in __init__\n    self.start_session(capabilities)\n  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/appium/webdriver/webdriver.py\", line 321, in start_session\n    response = self.execute(RemoteCommand.NEW_SESSION, w3c_caps)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py\", line 348, in execute\n    self.error_handler.check_response(response)\n  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/selenium/webdriver/remote/errorhandler.py\", line 229, in check_response\n    raise exception_class(message, screen, stacktrace)\n"}, "start": 1747906785731, "stop": 1747906805616}], "start": 1747906785731, "stop": 1747906805747}