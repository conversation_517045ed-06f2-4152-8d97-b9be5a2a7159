{"name": "PDP页面Reviews栏基础展示测试", "status": "passed", "description": "\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证Reviews栏是否展示\n        3. 验证总review数是否正确显示\n        4. 验证Customers say栏是否展示\n        5. 验证AI-generated文本样式\n        ", "parameters": [{"name": "setup", "value": "('test_reviews_section_display',)"}], "start": 1749177936360, "stop": 1749177987880, "uuid": "140a8cc7-9587-415e-9195-d44c40254bb2", "historyId": "f687fa249e9502191962abf9beab7816", "testCaseId": "ebd254721491e4204b01ca8c9db8c493", "fullName": "src.EC.tests.product.test_112777_pdp_reviews_selling_points.TestPDPReviewsSellingPoints#test_112777_reviews_section_display", "labels": [{"name": "story", "value": "PDP页面Reviews栏和卖点关键词功能测试"}, {"name": "tag", "value": "pdp_reviews"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.product"}, {"name": "suite", "value": "test_112777_pdp_reviews_selling_points"}, {"name": "subSuite", "value": "TestPDPReviewsSellingPoints"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "55768-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.product.test_112777_pdp_reviews_selling_points"}]}