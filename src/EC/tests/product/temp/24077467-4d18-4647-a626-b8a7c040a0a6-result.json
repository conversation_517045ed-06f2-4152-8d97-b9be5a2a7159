{"name": "PDP页面Reviews栏基础展示测试", "status": "failed", "statusDetails": {"message": "AssertionError: Reviews section is not displayed on PDP\nassert False\n +  where False = <bound method ProductDetailPage.check_reviews_section_exists of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x105ba6a20>>()\n +    where <bound method ProductDetailPage.check_reviews_section_exists of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x105ba6a20>> = <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x105ba6a20>.check_reviews_section_exists", "trace": "self = <src.EC.tests.product.test_112777_pdp_reviews_selling_points.TestPDPReviewsSellingPoints object at 0x10410c980>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"9350ae97-9152-4557-a0f8-16c04c85de19\")>\nsetup = None\n\n    @allure.title(\"PDP页面Reviews栏基础展示测试\")\n    @pytest.mark.parametrize('setup', [('test_reviews_section_display', )], indirect=True)\n    def test_112777_reviews_section_display(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证Reviews栏是否展示\n        3. 验证总review数是否正确显示\n        4. 验证Customers say栏是否展示\n        5. 验证AI-generated文本样式\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n    \n        # 导航到产品详情页\n        navigation_success = self.navigate_to_product_detail(d, platform)\n        assert navigation_success, \"Failed to navigate to product detail page\"\n    \n        # 初始化产品详情页对象\n        pdp_page = ProductDetailPage(d, platform)\n    \n        # 1. 验证Reviews栏是否存在\n>       assert pdp_page.check_reviews_section_exists(), \"Reviews section is not displayed on PDP\"\nE       AssertionError: Reviews section is not displayed on PDP\nE       assert False\nE        +  where False = <bound method ProductDetailPage.check_reviews_section_exists of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x105ba6a20>>()\nE        +    where <bound method ProductDetailPage.check_reviews_section_exists of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x105ba6a20>> = <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x105ba6a20>.check_reviews_section_exists\n\ntest_112777_pdp_reviews_selling_points.py:95: AssertionError"}, "description": "\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证Reviews栏是否展示\n        3. 验证总review数是否正确显示\n        4. 验证Customers say栏是否展示\n        5. 验证AI-generated文本样式\n        ", "parameters": [{"name": "setup", "value": "('test_reviews_section_display',)"}], "start": 1749114247907, "stop": 1749114291741, "uuid": "bd8dc6e5-a199-4c7c-8106-d716df751418", "historyId": "f687fa249e9502191962abf9beab7816", "testCaseId": "ebd254721491e4204b01ca8c9db8c493", "fullName": "src.EC.tests.product.test_112777_pdp_reviews_selling_points.TestPDPReviewsSellingPoints#test_112777_reviews_section_display", "labels": [{"name": "story", "value": "PDP页面Reviews栏和卖点关键词功能测试"}, {"name": "tag", "value": "pdp_reviews"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.product"}, {"name": "suite", "value": "test_112777_pdp_reviews_selling_points"}, {"name": "subSuite", "value": "TestPDPReviewsSellingPoints"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "43542-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.product.test_112777_pdp_reviews_selling_points"}]}