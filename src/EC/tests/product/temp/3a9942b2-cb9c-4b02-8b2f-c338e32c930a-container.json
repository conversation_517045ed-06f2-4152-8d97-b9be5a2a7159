{"uuid": "502cbdca-066e-4265-9402-a6a6fdac87d3", "children": ["d1a0a69b-dc19-4218-87fc-5273a2f3e81c"], "befores": [{"name": "get_driver", "status": "broken", "statusDetails": {"message": "selenium.common.exceptions.NoSuchElementException: Message: An element could not be located on the page using the given search parameters.; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\nNoSuchElementError: An element could not be located on the page using the given search parameters.\n    at XCUITestDriver.doNativeFind (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:143:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at XCUITestDriver.findNativeElementOrElements (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:97:12)\n    at XCUITestDriver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:24:14)\n    at XCUITestDriver.findElOrElsWithProcessing (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)\n    at XCUITestDriver.findElement (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:75:12)\n", "trace": "  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/pluggy/_callers.py\", line 77, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/_pytest/fixtures.py\", line 1123, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/_pytest/fixtures.py\", line 895, in call_fixture_func\n    fixture_result = next(generator)\n                     ^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android/src/EC/conftest.py\", line 96, in get_driver\n    login(driver, \"english\", \"<EMAIL>\", \"********\", strategy)\n  File \"/Users/<USER>/qa-ui-android/src/EC/conftest.py\", line 106, in login\n    _driver.find_element(*_strategy.get('shop_now')).click()\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/appium/webdriver/webdriver.py\", line 384, in find_element\n    return self.execute(RemoteCommand.FIND_ELEMENT, {'using': by, 'value': value})['value']\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py\", line 348, in execute\n    self.error_handler.check_response(response)\n  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/appium/webdriver/errorhandler.py\", line 125, in check_response\n    raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))\n"}, "start": 1747908207853, "stop": 1747908221280}], "start": 1747908207853, "stop": 1747908221425}