{"name": "iOS端PDP视频UI展示测试", "status": "broken", "statusDetails": {"message": "selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: The application at 'D:\\software\\Weee!_20.10.apk' does not exist or is not accessible\nStacktrace:\nUnknownError: An unknown server-side error occurred while processing the command. Original error: The application at 'D:\\software\\Weee!_20.10.apk' does not exist or is not accessible\n    at getResponseForW3CError (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/errors.js:1118:9)\n    at asyncHandler (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/protocol.js:491:57)", "trace": "@pytest.fixture(scope=\"session\")\n    def get_driver():\n>       driver = <PERSON><PERSON><PERSON>anager(os.getenv(\"platform\", \"Android\")).initialize_driver()\n\n../../conftest.py:91: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../../lib/device_manger.py:26: in initialize_driver\n    self.driver = webdriver.Remote(\n../../../../qa-ui-android/lib/python3.12/site-packages/appium/webdriver/webdriver.py:229: in __init__\n    super().__init__(\n../../../../qa-ui-android/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:209: in __init__\n    self.start_session(capabilities)\n../../../../qa-ui-android/lib/python3.12/site-packages/appium/webdriver/webdriver.py:321: in start_session\n    response = self.execute(RemoteCommand.NEW_SESSION, w3c_caps)\n../../../../qa-ui-android/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute\n    self.error_handler.check_response(response)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <selenium.webdriver.remote.errorhandler.ErrorHandler object at 0x105e6f5c0>\nresponse = {'status': 500, 'value': '{\"value\":{\"error\":\"unknown error\",\"message\":\"An unknown server-side error occurred while pro...asyncHandler (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/protocol.js:491:57)\"}}'}\n\n    def check_response(self, response: Dict[str, Any]) -> None:\n        \"\"\"Checks that a JSON response from the WebDriver does not have an\n        error.\n    \n        :Args:\n         - response - The JSON response from the WebDriver server as a dictionary\n           object.\n    \n        :Raises: If the response contains an error message.\n        \"\"\"\n        status = response.get(\"status\", None)\n        if not status or status == ErrorCode.SUCCESS:\n            return\n        value = None\n        message = response.get(\"message\", \"\")\n        screen: str = response.get(\"screen\", \"\")\n        stacktrace = None\n        if isinstance(status, int):\n            value_json = response.get(\"value\", None)\n            if value_json and isinstance(value_json, str):\n                import json\n    \n                try:\n                    value = json.loads(value_json)\n                    if len(value) == 1:\n                        value = value[\"value\"]\n                    status = value.get(\"error\", None)\n                    if not status:\n                        status = value.get(\"status\", ErrorCode.UNKNOWN_ERROR)\n                        message = value.get(\"value\") or value.get(\"message\")\n                        if not isinstance(message, str):\n                            value = message\n                            message = message.get(\"message\")\n                    else:\n                        message = value.get(\"message\", None)\n                except ValueError:\n                    pass\n    \n        exception_class: Type[WebDriverException]\n        e = ErrorCode()\n        error_codes = [item for item in dir(e) if not item.startswith(\"__\")]\n        for error_code in error_codes:\n            error_info = getattr(ErrorCode, error_code)\n            if isinstance(error_info, list) and status in error_info:\n                exception_class = getattr(ExceptionMapping, error_code, WebDriverException)\n                break\n        else:\n            exception_class = WebDriverException\n    \n        if not value:\n            value = response[\"value\"]\n        if isinstance(value, str):\n            raise exception_class(value)\n        if message == \"\" and \"message\" in value:\n            message = value[\"message\"]\n    \n        screen = None  # type: ignore[assignment]\n        if \"screen\" in value:\n            screen = value[\"screen\"]\n    \n        stacktrace = None\n        st_value = value.get(\"stackTrace\") or value.get(\"stacktrace\")\n        if st_value:\n            if isinstance(st_value, str):\n                stacktrace = st_value.split(\"\\n\")\n            else:\n                stacktrace = []\n                try:\n                    for frame in st_value:\n                        line = frame.get(\"lineNumber\", \"\")\n                        file = frame.get(\"fileName\", \"<anonymous>\")\n                        if line:\n                            file = f\"{file}:{line}\"\n                        meth = frame.get(\"methodName\", \"<anonymous>\")\n                        if \"className\" in frame:\n                            meth = f\"{frame['className']}.{meth}\"\n                        msg = \"    at %s (%s)\"\n                        msg = msg % (meth, file)\n                        stacktrace.append(msg)\n                except TypeError:\n                    pass\n        if exception_class == UnexpectedAlertPresentException:\n            alert_text = None\n            if \"data\" in value:\n                alert_text = value[\"data\"].get(\"text\")\n            elif \"alert\" in value:\n                alert_text = value[\"alert\"].get(\"text\")\n            raise exception_class(message, screen, stacktrace, alert_text)  # type: ignore[call-arg]  # mypy is not smart enough here\n>       raise exception_class(message, screen, stacktrace)\nE       selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: The application at 'D:\\software\\Weee!_20.10.apk' does not exist or is not accessible\nE       Stacktrace:\nE       UnknownError: An unknown server-side error occurred while processing the command. Original error: The application at 'D:\\software\\Weee!_20.10.apk' does not exist or is not accessible\nE           at getResponseForW3CError (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/errors.js:1118:9)\nE           at asyncHandler (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/protocol.js:491:57)\n\n../../../../qa-ui-android/lib/python3.12/site-packages/selenium/webdriver/remote/errorhandler.py:229: WebDriverException"}, "description": "\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证视频栏展示及视频个数显示\n        ", "parameters": [{"name": "setup", "value": "('test_112812_pdp_video_ui_display',)"}], "start": 1747906785716, "stop": 1747906785716, "uuid": "4246bbc4-1f3f-4ab7-94f0-39daf9a528ab", "historyId": "60923f2b1aba9c657800c71283729ef9", "testCaseId": "7d6cd075fbe0ebaf7d2a64add28a49fb", "fullName": "src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures#test_112812_pdp_video_ui_display", "labels": [{"name": "story", "value": "iOS端PDP视频功能测试"}, {"name": "tag", "value": "pdp_video"}, {"name": "parentSuite", "value": "src.EC.tests.product"}, {"name": "suite", "value": "test_112812_pdp_video_features"}, {"name": "subSuite", "value": "TestPDPVideoFeatures"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "58634-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.product.test_112812_pdp_video_features"}]}