{"uuid": "f2b46649-7a9a-4e6d-bdfe-3dd290384693", "children": ["ea8eece4-97c8-453b-aff8-ec6f2b17e10f"], "befores": [{"name": "get_driver", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/pluggy/_callers.py\", line 77, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/_pytest/fixtures.py\", line 1123, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android/qa-ui-android/lib/python3.12/site-packages/_pytest/fixtures.py\", line 895, in call_fixture_func\n    fixture_result = next(generator)\n                     ^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android/src/EC/conftest.py\", line 92, in get_driver\n    driver = DeviceManager(os.getenv(\"platform\", \"Android\")).initialize_driver()\n             ^^^^^^^^^^^^^\n  File \"_pydevd_bundle/pydevd_pep_669_tracing_cython.pyx\", line 554, in _pydevd_bundle.pydevd_pep_669_tracing_cython.py_line_callback\n  File \"_pydevd_bundle/pydevd_pep_669_tracing_cython.pyx\", line 547, in _pydevd_bundle.pydevd_pep_669_tracing_cython.py_line_callback\n  File \"/Applications/PyCharm CE.app/Contents/plugins/python-ce/helpers/pydev/pydevd.py\", line 1220, in do_wait_suspend\n    self._do_wait_suspend(thread, frame, event, arg, suspend_type, from_this_thread)\n  File \"/Applications/PyCharm CE.app/Contents/plugins/python-ce/helpers/pydev/pydevd.py\", line 1235, in _do_wait_suspend\n    time.sleep(0.01)\n"}, "start": 1747906979841, "stop": 1747907645807}], "start": 1747906979841, "stop": 1747907646222}