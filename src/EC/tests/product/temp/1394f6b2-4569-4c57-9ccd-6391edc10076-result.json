{"name": "iOS端PDP视频UI展示测试", "status": "passed", "description": "\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证视频栏展示及视频个数显示\n        ", "parameters": [{"name": "setup", "value": "('test_112812_pdp_video_ui_display',)"}], "start": 1747984378421, "stop": 1747984500066, "uuid": "8834fd73-b59d-4c8f-bc7e-b5cf1ed067bf", "historyId": "60923f2b1aba9c657800c71283729ef9", "testCaseId": "7d6cd075fbe0ebaf7d2a64add28a49fb", "fullName": "src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures#test_112812_pdp_video_ui_display", "labels": [{"name": "story", "value": "iOS端PDP视频功能测试"}, {"name": "tag", "value": "pdp_video"}, {"name": "parentSuite", "value": "src.EC.tests.product"}, {"name": "suite", "value": "test_112812_pdp_video_features"}, {"name": "subSuite", "value": "TestPDPVideoFeatures"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "16479-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.product.test_112812_pdp_video_features"}]}