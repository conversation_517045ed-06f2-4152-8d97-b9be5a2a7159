import os
import time
import allure
import pytest
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.page_objects.home.home_page import HomePage
from src.EC.tests.base_case import BaseCase
from src.common.recording import base64_to_mp4
from src.EC.page_objects.product.product_detail_page import ProductDetailPage
from src.config.weee.log_help import log


@allure.story("iOS端PDP视频功能测试")
class TestPDPVideoFeatures(BaseCase):

    pytestmark = [pytest.mark.pdp_video, pytest.mark.regression]

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
        get_driver.start_recording_screen()
        yield
        video_data = get_driver.stop_recording_screen()
        if not os.path.exists(f'./video/{os.getenv("BUILD_NUMBER", 0)}'):
            os.makedirs(f'./video/{os.getenv("BUILD_NUMBER", 0)}')
        base64_to_mp4(video_data, f'./video/{os.getenv("BUILD_NUMBER", 0)}/{request.param[0]}.mp4')

    def navigate_to_product_detail(self, driver, platform):
        """导航到产品详情页面"""
        # 这里需要根据实际情况调整导航逻辑
        try:
            home_page = HomePage(driver, platform)

            # 点击首页
            home_button = self.find_element(_driver=driver, ele=home_page.strategies.get(platform).get("home"))
            if home_button:
                home_button.click()
                time.sleep(5)

            # 向下滑动以显示产品
            self.swipe_screen(_driver=driver, distance=0.4)
            time.sleep(2)

            # 点击第一个产品进入详情页
            # 这里假设首页有产品列表，点击第一个产品
            product_item = self.find_elements(_driver=driver, ele=home_page.strategies.get(platform).get("first_product"))

            if product_item:
                product_item[0].click()
                time.sleep(3)  # 等待产品详情页加载
                if platform == 'Android':
                    # 安卓需要关闭time banner，否则pdp页面滑动无效, IOS无需此操作
                    time_banner = self.find_element(_driver=driver, ele=ProductDetailPage(driver, platform).strategies.get(platform).get('time_banner'))
                    if time_banner:
                        time_banner.click()
                return True
            else:
                raise Exception("Product item not found and can't navigate to pdp page!")

        except Exception as e:
            log.error(f"Error navigating to product detail: {str(e)}")
            return False

    @allure.title("iOS端PDP视频UI展示测试")
    @pytest.mark.parametrize('setup', [('test_112812_pdp_video_ui_display', )], indirect=True)
    def test_112812_pdp_video_ui_display(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. 进入产品详情页
        2. 验证视频栏展示及视频个数显示
        """
        d = get_driver
        platform = get_platform

        # 准备测试环境

        # 导航到产品详情页
        navigation_success = self.navigate_to_product_detail(d, platform)
        assert navigation_success, "Failed to navigate to product detail page"

        # 初始化产品详情页对象
        pdp_page = ProductDetailPage(d, platform)

        # 1. 验证视频栏是否存在
        assert pdp_page.check_videos_section_exists(), "Videos section is not displayed on PDP"

        # 2. 验证视频个数是否显示
        videos_count = pdp_page.get_videos_count()
        assert videos_count > 0, f"Videos count should be a non-negative number, got {videos_count}"

        # 记录测试结果
        if videos_count > 0:
            log.info(f"Videos section is displayed with {videos_count} videos")
        else:
            log.info("Videos section is displayed but no videos are available")

    @allure.title("视频栏分类排序测试")
    @pytest.mark.parametrize('setup', [('test_002_video_categories_order', )], indirect=True)
    def test_112812_video_categories_order(self, get_driver, get_platform, setup):
        """
        测试步骤：
        1. 进入产品详情页
        2. 验证视频栏分类排序是否为 review > recipes > unboxing
        """
        # 此用例仅适用于iOS端,安卓滑这三个标签
        d = get_driver
        platform = get_platform


        # 初始化产品详情页对象
        pdp_page = ProductDetailPage(d, platform)

        # 验证视频分类排序
        assert pdp_page.check_video_categories_order(), "Video categories are not in the expected order (review > recipes > unboxing)"

        # 记录测试结果
        log.info("Video categories are correctly ordered as: review > recipes > unboxing")

    @allure.title("点击See All跳转测试")
    @pytest.mark.parametrize('setup', [('test_003_see_all_navigation', )], indirect=True)
    def test_112812_see_all_navigation(self, get_driver, get_platform, setup):
        """
        测试步骤：
        1. 进入产品详情页
        2. 点击See All
        3. 验证是否跳转到Explore页面并展示全部视频
        """
        d = get_driver
        platform = get_platform

        # 准备测试环境

        # 导航到产品详情页
        navigation_success = self.navigate_to_product_detail(d, platform)
        assert navigation_success, "Failed to navigate to product detail page"

        # 初始化产品详情页对象
        pdp_page = ProductDetailPage(d, platform)

        # 验证视频栏是否存在
        assert pdp_page.check_videos_section_exists(), "Videos section is not displayed on PDP"

        # 点击See All按钮
        assert pdp_page.click_see_all_videos(), "Failed to click See All button"

        # 验证是否跳转到Explore页面
        # 这里需要根据实际情况调整验证逻辑
        if platform == 'Android':
            explore_title = self.find_element(_driver=d, ele=(AppiumBy.XPATH, '//android.widget.TextView[@text="Explore"]'))
        else:  # iOS整个页面的元素无法识别，可能是h5页面
            # explore_title = self.find_element(_driver=d, ele=(AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeNavigationBar[`name == "Explore"`]'))
            #需要返回pdp页面，再返回首页
            d.tap([(24, 71)])
            time.sleep(2)
            # todo pdp页面back按钮无法定位，只能用坐标back to home
            log.info("back to home from pdp")
            d.tap([(31, 66)])
        # assert explore_title is not None, "Failed to navigate to Explore page after clicking See All"

        # 记录测试结果
        log.info("Successfully navigated to Explore page after clicking See All")

    @allure.title("PDP页面发布视频入口测试")
    @pytest.mark.parametrize('setup', [('test_004_publish_video_entry', )], indirect=True)
    def _test_112812_publish_video_entry(self, get_driver, get_platform, setup):
        """
        测试步骤：
        1. 进入产品详情页
        2. 验证PDP下方是否展示发布视频tip词
        3. 验证发布视频按钮是否可点击
        """
        d = get_driver
        platform = get_platform

        # 准备测试环境

        # 导航到产品详情页
        navigation_success = self.navigate_to_product_detail(d, platform)
        assert navigation_success, "Failed to navigate to product detail page"

        # 初始化产品详情页对象
        pdp_page = ProductDetailPage(d, platform)

        # 向下滑动以显示页面底部
        d.swipe(500, 500, 500, 200)
        time.sleep(2)

        # 验证发布视频提示是否存在
        assert pdp_page.check_publish_video_tip_exists(), "Publish video tip is not displayed on PDP"

        # 验证发布视频按钮是否可点击
        # 注意：这里只验证按钮是否存在，不实际点击进入发布流程
        publish_button = self.find_element(_driver=d, ele=pdp_page.strategies.get(platform).get('publish_video_button'))
        assert publish_button is not None, "Publish video button is not displayed on PDP"
        assert publish_button.is_enabled(), "Publish video button is not enabled"

        # 记录测试结果
        log.info("Publish video tip and button are correctly displayed on PDP")
