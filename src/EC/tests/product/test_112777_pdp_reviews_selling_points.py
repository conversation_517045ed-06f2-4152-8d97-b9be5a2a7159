import os
import time
import allure
import pytest
import re
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.page_objects.home.home_page import HomePage
from src.EC.tests.base_case import BaseCase
from src.common.recording import base64_to_mp4
from src.EC.page_objects.product.product_detail_page import ProductDetailPage
from src.EC.page_objects.explore.explore_page import ExplorePage
from src.config.weee.log_help import log



@allure.story("PDP页面Reviews栏和卖点关键词功能测试")
class TestPDPReviewsSellingPoints(BaseCase):

    pytestmark = [pytest.mark.pdp_reviews, pytest.mark.selling_points]

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
        get_driver.start_recording_screen()
        yield
        video_data = get_driver.stop_recording_screen()
        if not os.path.exists(f'./video/{os.getenv("BUILD_NUMBER", 0)}'):
            os.makedirs(f'./video/{os.getenv("BUILD_NUMBER", 0)}')
        base64_to_mp4(video_data, f'./video/{os.getenv("BUILD_NUMBER", 0)}/{request.param[0]}.mp4')

    def navigate_to_product_detail(self, driver, platform):
        """导航到产品详情页面"""
        try:
            home_page = HomePage(driver, platform)

            # 点击首页
            home_button = self.find_element(_driver=driver, ele=home_page.strategies.get(platform).get("home"))
            if home_button:
                home_button.click()
                time.sleep(5)

            # 向下滑动以显示产品
            self.swipe_screen(_driver=driver, distance=0.4)
            time.sleep(2)

            # 点击第一个产品进入详情页
            product_item = self.find_elements(_driver=driver, ele=home_page.strategies.get(platform).get("first_product"))

            if product_item:
                product_item[0].click()
                time.sleep(3)  # 等待产品详情页加载
                if platform == 'Android':
                    # 安卓需要关闭time banner，否则pdp页面滑动无效, IOS无需此操作
                    time_banner = self.find_element(_driver=driver, ele=ProductDetailPage(driver, platform).strategies.get(platform).get('time_banner'))
                    if time_banner:
                        time_banner.click()
                return True
            else:
                raise Exception("Product item not found and can't navigate to pdp page!")

        except Exception as e:
            log.error(f"Error navigating to product detail: {str(e)}")
            return False

    def extract_product_id_from_url(self, driver):
        """从当前页面URL提取产品ID"""
        try:
            # 这里需要根据实际情况获取产品ID
            # 可能需要通过页面元素或其他方式获取
            # 暂时返回测试用的产品ID
            return 12345  # 替换为实际的产品ID获取逻辑
        except Exception as e:
            log.error(f"Failed to extract product ID: {str(e)}")
            return None

    @allure.title("PDP页面Reviews栏基础展示测试")
    @pytest.mark.parametrize('setup', [('test_reviews_section_display', )], indirect=True)
    def test_112777_reviews_section_display(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. 进入产品详情页
        2. 验证Reviews栏是否展示
        3. 验证总review数是否正确显示
        4. 验证Customers say栏是否展示
        5. 验证AI-generated文本样式
        """
        d = get_driver
        platform = get_platform

        # 导航到产品详情页
        navigation_success = self.navigate_to_product_detail(d, platform)
        assert navigation_success, "Failed to navigate to product detail page"

        # 初始化产品详情页对象
        pdp_page = ProductDetailPage(d, platform)

        # 1. 验证Reviews栏是否存在
        assert pdp_page.check_reviews_section_exists(), "Reviews section is not displayed on PDP"
        log.info("✓ Reviews section is displayed")

        # 2. 验证总review数显示
        reviews_count = pdp_page.get_total_reviews_count()
        assert reviews_count >= 0, f"Reviews count should be non-negative, got {reviews_count}"
        log.info(f"✓ Total reviews count: {reviews_count}")

        # 以下内容为IOS独有，安卓没有
        if platform == 'iOS':
            # 3. 验证Customers say栏是否存在
            customers_say_exists = pdp_page.check_customers_say_section_exists()
            if reviews_count > 50:
                assert customers_say_exists, "Customers say section should be displayed when reviews exist"
                log.info("✓ Customers say section is displayed")
            else:
                log.info("ℹ No reviews found, customers say section may not be displayed")

            # 4. 验证AI-generated文本样式
            if customers_say_exists:
                # 目前无法做太复杂的文本分析，先跳过此步骤
                pass
                # ai_text_style_correct = pdp_page.check_ai_generated_text_style()
                # assert ai_text_style_correct, "AI-generated text style should be lighter than normal text"
                # log.info("✓ AI-generated text style is correct")

            # 5. 跳转关键词review list界面
            selling_points_container = self.find_element(_driver=d, ele=pdp_page.strategies.get(platform).get("selling_points_container"))
            if selling_points_container:
                selling_points_container.click()
                time.sleep(3)
                assert self.find_element(_driver=d, ele=pdp_page.strategies.get(platform).get("review_list_table")).is_displayed()
                back_to_pdp = self.find_element(_driver=d, ele=pdp_page.strategies.get(platform).get("review_list_back"))
                d.tap([(back_to_pdp.location.get("x"), back_to_pdp.location.get("y"))])
                # back.click()


