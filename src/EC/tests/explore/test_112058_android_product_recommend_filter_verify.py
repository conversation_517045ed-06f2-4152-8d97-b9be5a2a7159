import os
import time

import allure
import pytest

from src.EC.page_objects.explore.explore_page import ExplorePage
from src.EC.tests.base_case import BaseCase
from src.api.zipcode import switch_zipcode
from src.common.recording import base64_to_mp4
from src.config.weee.log_help import log


@allure.story("H5-商品推荐规则filter 查询验证")
class TestAndroidProductRecommendFilterVerify(BaseCase):

    pytestmark = [pytest.mark.explore, pytest.mark.filter, pytest.mark.smoke]
    @allure.title("Android-deals商品推荐规则filter 查询验证")
    def test_112058_deals_android_product_recommend_filter_verify(self, get_driver, android_header, get_platform):
        d = get_driver
        explore_page = ExplorePage(d, get_platform)
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("home")).click()
        time.sleep(15)
        _text = self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("zipcode")).text
        print("_text===>", _text)
        # 如果zipcode不是98011， 则切换到98011
        if _text != '98011':
            switch_zipcode(android_header, "98011")

        # 切换到explore页面
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("explore")).click()

        # deals
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("deals")).click()
        # 点击sort featured
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        sorts = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("sorts"))
        explore_page.search_product_by_sort(sorts[0])
        self._filter_assertion(d, explore_page, get_platform)

        # 点击sort best selling
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        # sorts
        sorts = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("sorts"))
        explore_page.search_product_by_sort(sorts[1])
        self._filter_assertion(d, explore_page, get_platform)

        # 点击sort price low to high
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        sorts = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("sorts"))
        explore_page.search_product_by_sort(sorts[2])
        self._filter_assertion(d, explore_page, get_platform)

        # 点击sort price high to low
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        sorts = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("sorts"))
        explore_page.search_product_by_sort(sorts[3])
        self._filter_assertion(d, explore_page, get_platform)

        # filter local delivery
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        filters = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_inner"))
        explore_page.search_product_by_sort(filters[0])
        self._filter_assertion(d, explore_page, get_platform)

        # filter pantry
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        filters = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_inner"))
        filters[0].click()
        explore_page.search_product_by_sort(filters[1])
        self._filter_assertion(d, explore_page, get_platform)

        # filter global
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        filters = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_inner"))
        filters[1].click()
        explore_page.search_product_by_sort(filters[2])
        self._filter_assertion(d, explore_page, get_platform)

        # product_type与filter的locator表达式一样，所以用的还是filter_inner
        # product_type deals
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        filters = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_inner"))
        filters[2].click()
        explore_page.search_product_by_sort(filters[3])
        self._filter_assertion(d, explore_page, get_platform)

        # product_type new arrivals
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        filters = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_inner"))
        filters[3].click()
        explore_page.search_product_by_sort(filters[4])
        assert self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_main")).is_displayed()

    @allure.title("Android-new arrivals商品推荐规则filter 查询验证")
    def test_112058_new_arrivals_android_product_recommend_filter_verify(self, get_driver, android_header, get_platform):
        d = get_driver
        explore_page = ExplorePage(d, get_platform)
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("home")).click()
        time.sleep(15)
        _text = self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("zipcode")).text
        print("_text===>", _text)
        # 如果zipcode不是98011， 则切换到98011
        if _text != '98011':
            switch_zipcode(android_header, "98011")
            d.reload()
        # 切换到explore页面
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("explore")).click()

        # new arrivals
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("new_arrivals")).click()
        # 点击sort featured
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        sorts = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("sorts"))
        explore_page.search_product_by_sort(sorts[0])
        self._filter_assertion(d, explore_page, get_platform)

        # 点击sort best selling
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("new_arrivals")).click()
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        # sorts
        sorts = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("sorts"))
        explore_page.search_product_by_sort(sorts[1])
        self._filter_assertion(d, explore_page, get_platform)

        # 点击sort price low to high
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("new_arrivals")).click()
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        sorts = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("sorts"))
        explore_page.search_product_by_sort(sorts[2])
        self._filter_assertion(d, explore_page, get_platform)

        # 点击sort price high to low
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("new_arrivals")).click()
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        sorts = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("sorts"))
        explore_page.search_product_by_sort(sorts[3])
        self._filter_assertion(d, explore_page, get_platform)

        # filter local delivery
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("new_arrivals")).click()
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        filters = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_inner"))
        explore_page.search_product_by_sort(filters[0])
        self._filter_assertion(d, explore_page, get_platform)

        # filter pantry
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("new_arrivals")).click()
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        filters = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_inner"))
        explore_page.search_product_by_sort(filters[1])
        self._filter_assertion(d, explore_page, get_platform)

        # filter global
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("new_arrivals")).click()
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        filters = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_inner"))
        explore_page.search_product_by_sort(filters[2])
        self._filter_assertion(d, explore_page, get_platform)

        # product_type与filter的locator表达式一样，所以用的还是filter_inner
        # product_type deals
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("new_arrivals")).click()
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        filters = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_inner"))
        explore_page.search_product_by_sort(filters[3])
        self._filter_assertion(d, explore_page, get_platform)

        # product_type new arrivals
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("new_arrivals")).click()
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        filters = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_inner"))
        explore_page.search_product_by_sort(filters[4])
        assert self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_main")).is_displayed()

    @allure.title("Android-Best sellers商品推荐规则filter 查询验证")
    def test_112058_best_sellers_android_product_recommend_filter_verify(self, get_driver, android_header, get_platform):
        d = get_driver
        explore_page = ExplorePage(d, get_platform)
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("home")).click()
        time.sleep(15)
        _text = self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("zipcode")).text
        print("_text===>", _text)
        # 如果zipcode不是98011， 则切换到98011
        if _text != '98011':
            switch_zipcode(android_header, "98011")
            d.reload()
        # 切换到explore页面
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("explore")).click()

        # Best sellers
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("Bestsellers")).click()
        # 点击sort featured
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        sorts = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("sorts"))
        explore_page.search_product_by_sort(sorts[0])
        self._filter_assertion(d, explore_page, get_platform)

        # 点击sort best selling
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("Bestsellers")).click()
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        # sorts
        sorts = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("sorts"))
        explore_page.search_product_by_sort(sorts[1])
        self._filter_assertion(d, explore_page, get_platform)

        # 点击sort price low to high
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("Bestsellers")).click()
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        sorts = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("sorts"))
        explore_page.search_product_by_sort(sorts[2])
        self._filter_assertion(d, explore_page, get_platform)

        # 点击sort price high to low
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("Bestsellers")).click()
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        sorts = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("sorts"))
        explore_page.search_product_by_sort(sorts[3])
        self._filter_assertion(d, explore_page, get_platform)

        # filter local delivery
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("Bestsellers")).click()
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        filters = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_inner"))
        explore_page.search_product_by_sort(filters[0])
        self._filter_assertion(d, explore_page, get_platform)

        # filter pantry
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("Bestsellers")).click()
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        filters = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_inner"))
        filters[0].click()
        explore_page.search_product_by_sort(filters[1])
        self._filter_assertion(d, explore_page, get_platform)

        # filter global
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("Bestsellers")).click()
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        filters = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_inner"))
        filters[1].click()
        explore_page.search_product_by_sort(filters[2])
        self._filter_assertion(d, explore_page, get_platform)

        # product_type与filter的locator表达式一样，所以用的还是filter_inner
        # product_type deals
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("Bestsellers")).click()
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        filters = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_inner"))
        filters[2].click()
        explore_page.search_product_by_sort(filters[3])
        self._filter_assertion(d, explore_page, get_platform)

        # product_type new arrivals
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("Bestsellers")).click()
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter")).click()
        filters = self.find_elements(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_inner"))
        filters[3].click()
        explore_page.search_product_by_sort(filters[4])
        assert self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("filter_main")).is_displayed()

    def _filter_assertion(self, _driver, _page: ExplorePage, _platform):
        assert self.find_element(_driver=_driver, ele=_page.strategies.get(_platform).get("filter_main")).is_displayed()
        assert self.find_elements(_driver=_driver, ele=_page.strategies.get(_platform).get("filter_main_add_cart"))







