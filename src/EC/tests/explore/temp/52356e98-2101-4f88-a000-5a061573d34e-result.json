{"name": "Android-deals商品推荐规则filter 查询验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'WebDriver' object has no attribute 'reload'", "trace": "self = <test_112058_android_product_recommend_filter_verify.TestAndroidProductRecommendFilterVerify object at 0x105d8b2c0>\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"c04c0b17-dd10-4616-8c1b-39a2f34013e7\")>\nandroid_header = {'Content-Type': 'application/json;charset=UTF-8', 'app-version': 'null', 'authorization': '<PERSON>er eyJraWQiOiJkZjBlZDI...KMJB8M7q-wuMb86U9kEexqcmfvSiCSa4Fp4o4G77K3bT7cN8_vZ3Y7qkppbEDrnLxeLCd23OZ2kN0RycmNSK5M', 'b-cookie': '1265617263', ...}\nget_platform = 'iOS'\n\n    @allure.title(\"Android-deals商品推荐规则filter 查询验证\")\n    def test_112058_deals_android_product_recommend_filter_verify(self, get_driver, android_header, get_platform):\n        d = get_driver\n        explore_page = ExplorePage(d, get_platform)\n        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get(\"home\")).click()\n        time.sleep(15)\n        _text = self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get(\"zipcode\")).text\n        print(\"_text===>\", _text)\n        # 如果zipcode不是98011， 则切换到98011\n        if _text != '98011':\n            switch_zipcode(android_header, \"98011\")\n>           d.reload()\nE           AttributeError: 'WebDriver' object has no attribute 'reload'\n\ntest_112058_android_product_recommend_filter_verify.py:29: AttributeError"}, "start": 1748239596434, "stop": 1748239613994, "uuid": "272978f4-6d02-4230-84fb-6ddb1aec1dea", "historyId": "0659c5f0d328dce7632f5e205a32e4ba", "testCaseId": "0659c5f0d328dce7632f5e205a32e4ba", "fullName": "src.EC.tests.explore.test_112058_android_product_recommend_filter_verify.TestAndroidProductRecommendFilterVerify#test_112058_deals_android_product_recommend_filter_verify", "labels": [{"name": "story", "value": "H5-商品推荐规则filter 查询验证"}, {"name": "tag", "value": "explore"}, {"name": "tag", "value": "filter"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "src.EC.tests.explore"}, {"name": "suite", "value": "test_112058_android_product_recommend_filter_verify"}, {"name": "subSuite", "value": "TestAndroidProductRecommendFilterVerify"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "13254-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.explore.test_112058_android_product_recommend_filter_verify"}]}