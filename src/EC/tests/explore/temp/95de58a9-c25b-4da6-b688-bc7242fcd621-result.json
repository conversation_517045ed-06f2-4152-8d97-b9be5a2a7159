{"name": "Android-deals商品推荐规则filter 查询验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'text'", "trace": "self = <test_112058_android_product_recommend_filter_verify.TestAndroidProductRecommendFilterVerify object at 0x11184cda0>\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"10724ddb-8952-4fdc-bf96-63f74ea9a90f\")>\nandroid_header = {'Content-Type': 'application/json;charset=UTF-8', 'app-version': 'null', 'authorization': 'Bearer eyJraWQiOiJkZjBlZDI...2sDpc1J8jUEarjK7LMz2xWQ9GxCgM836N4ujPnLEYq6TJ12vBSfo6S1rGS1pbufeqrgPZ6YU2MGE4WPlOj6VoE', 'b-cookie': '1265614983', ...}\nget_platform = 'iOS'\n\n    @allure.title(\"Android-deals商品推荐规则filter 查询验证\")\n    def test_112058_deals_android_product_recommend_filter_verify(self, get_driver, android_header, get_platform):\n        d = get_driver\n        explore_page = ExplorePage(d, get_platform)\n        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get(\"home\")).click()\n        time.sleep(15)\n>       _text = self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get(\"zipcode\")).text\nE       AttributeError: 'NoneType' object has no attribute 'text'\n\ntest_112058_android_product_recommend_filter_verify.py:24: AttributeError"}, "start": 1748239324107, "stop": 1748239350834, "uuid": "c9cfefbd-ec5c-43e2-836f-dbc4e255a778", "historyId": "0659c5f0d328dce7632f5e205a32e4ba", "testCaseId": "0659c5f0d328dce7632f5e205a32e4ba", "fullName": "src.EC.tests.explore.test_112058_android_product_recommend_filter_verify.TestAndroidProductRecommendFilterVerify#test_112058_deals_android_product_recommend_filter_verify", "labels": [{"name": "story", "value": "H5-商品推荐规则filter 查询验证"}, {"name": "tag", "value": "explore"}, {"name": "tag", "value": "filter"}, {"name": "tag", "value": "smoke"}, {"name": "parentSuite", "value": "src.EC.tests.explore"}, {"name": "suite", "value": "test_112058_android_product_recommend_filter_verify"}, {"name": "subSuite", "value": "TestAndroidProductRecommendFilterVerify"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "11531-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.explore.test_112058_android_product_recommend_filter_verify"}]}