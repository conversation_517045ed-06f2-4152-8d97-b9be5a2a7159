import os
import time
import allure
import pytest
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.tests.base_case import BaseCase
from src.common.recording import base64_to_mp4
from src.EC.page_objects.social.social_search_page import SocialSearchPage

from src.config.weee.log_help import log



@allure.story("Social搜索账号功能验证")
class TestSocialSearchAccountsVerify(BaseCase):

    pytestmark = [pytest.mark.regression]

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
        get_driver.start_recording_screen()
        yield
        video_data = get_driver.stop_recording_screen()
        if not os.path.exists(f'./video/{os.getenv("BUILD_NUMBER", 0)}'):
            os.makedirs(f'./video/{os.getenv("BUILD_NUMBER", 0)}')
        base64_to_mp4(video_data, f'./video/{os.getenv("BUILD_NUMBER", 0)}/{request.param[0]}.mp4')



    @allure.title("搜索对应用户名并切换到Accounts栏测试")
    @pytest.mark.parametrize('setup', [('test_search_user_and_switch_accounts',)], indirect=True)
    def test_112741_search_user_and_switch_accounts(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. 进入搜索页面
        2. 搜索指定用户名
        3. 验证默认在Posts栏
        4. 切换到Accounts栏
        5. 验证出现对应的用户
        """
        d = get_driver
        platform = get_platform

        # 初始化社交搜索页面对象
        search_page = SocialSearchPage(d, platform)

        # 导航到搜索页面，点击inspiration按钮
        inspiration = self.find_element(d, search_page.strategies.get(platform).get("inspiration"))
        assert inspiration, "Failed to find inspiration button!"
        inspiration.click()
        time.sleep(3)

        # 搜索用户（使用测试用户名）
        test_username = "autotest"
        search_success = search_page.search_user(test_username)
        assert search_success, f"Failed to search for user: {test_username}"

        # 验证默认在Posts栏
        photos_tab_active = search_page.check_posts_tab_active()
        assert photos_tab_active, "Photos tab should be active by default"
        log.info("✓ Photos tab is active by default")

        # 切换到Accounts栏
        switch_success = search_page.switch_to_accounts_tab()
        assert switch_success, "Failed to switch to Accounts tab"
        log.info("✓ Successfully switched to Accounts tab")

        # 验证出现对应的用户
        user_list = search_page.get_user_list()
        assert len(user_list) > 0, "No users found in Accounts tab"

        # 验证搜索的用户在结果中
        user_found = any(test_username.lower() in user.text.lower() for user in user_list)
        assert user_found, f"User '{test_username}' not found in search results"
        log.info(f"✓ User '{test_username}' found in search results")

        target_user = user_list[0].text.lower()

        # 点击用户头像
        avatar_click_success = search_page.click_user_avatar(target_user)
        assert avatar_click_success, f"Failed to click avatar for user: {target_user}"

        # 验证跳转到Profile页面
        profile_page_loaded = search_page.check_user_profile_page(target_user)
        assert profile_page_loaded, f"Failed to navigate to profile page for user: {target_user}"
        log.info(f"✓ Successfully navigated to profile page for user: {target_user}")

        # 点击关注/取消关注按钮
        follow_click_success = search_page.click_follow_button(target_user)
        assert follow_click_success, f"Failed to click follow button for user: {target_user}"

        # 搜索不存在的用户
        # nonexistent_user = "nonexistentuser123"
        # search_result = search_page.search_user(nonexistent_user)
        # assert not search_result, f"Search result should be empty for nonexistent user: {nonexistent_user}"
        # time.sleep(3)  # 等待搜索结果加载





