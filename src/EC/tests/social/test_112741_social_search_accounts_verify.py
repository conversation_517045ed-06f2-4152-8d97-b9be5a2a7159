import os
import time
import allure
import pytest
import re
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.page_objects.home.home_page import HomePage
from src.EC.tests.base_case import BaseCase
from src.common.recording import base64_to_mp4
from src.EC.page_objects.product.product_detail_page import ProductDetailPage
from src.EC.page_objects.explore.explore_page import ExplorePage
from src.config.weee.log_help import log



@allure.story("Social搜索账号功能验证")
class TestSocialSearchAccountsVerify(BaseCase):

    pytestmark = [pytest.mark.regression]

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
        get_driver.start_recording_screen()
        yield
        video_data = get_driver.stop_recording_screen()
        if not os.path.exists(f'./video/{os.getenv("BUILD_NUMBER", 0)}'):
            os.makedirs(f'./video/{os.getenv("BUILD_NUMBER", 0)}')
        base64_to_mp4(video_data, f'./video/{os.getenv("BUILD_NUMBER", 0)}/{request.param[0]}.mp4')



