{"uuid": "d3d851eb-7ca0-4d88-9947-79b2fdea4994", "children": ["9e76280f-2964-44c2-b79a-698ae2a86959"], "befores": [{"name": "get_driver", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/pluggy/_callers.py\", line 77, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/_pytest/fixtures.py\", line 1123, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/_pytest/fixtures.py\", line 895, in call_fixture_func\n    fixture_result = next(generator)\n                     ^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/src/EC/conftest.py\", line 139, in get_driver\n    log.info(\"already logged in\")\n    ^^^\n  File \"_pydevd_bundle/pydevd_pep_669_tracing_cython.pyx\", line 554, in _pydevd_bundle.pydevd_pep_669_tracing_cython.py_line_callback\n  File \"_pydevd_bundle/pydevd_pep_669_tracing_cython.pyx\", line 547, in _pydevd_bundle.pydevd_pep_669_tracing_cython.py_line_callback\n  File \"/Applications/PyCharm CE.app/Contents/plugins/python-ce/helpers/pydev/pydevd.py\", line 1220, in do_wait_suspend\n    self._do_wait_suspend(thread, frame, event, arg, suspend_type, from_this_thread)\n  File \"/Applications/PyCharm CE.app/Contents/plugins/python-ce/helpers/pydev/pydevd.py\", line 1235, in _do_wait_suspend\n    time.sleep(0.01)\n"}, "start": 1748587927295, "stop": 1748587954731}], "start": 1748587927294, "stop": 1748587955097}