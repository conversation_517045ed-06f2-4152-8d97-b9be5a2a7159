{"name": "搜索对应用户名并切换到Accounts栏测试", "status": "failed", "statusDetails": {"message": "AssertionError: Failed to click avatar for user: autotest\nassert False", "trace": "self = <test_112741_social_search_accounts_verify.TestSocialSearchAccountsVerify object at 0x119a27cb0>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"95bb67c1-5df0-4052-968d-7aa00be542a0\")>\nsetup = None\n\n    @allure.title(\"搜索对应用户名并切换到Accounts栏测试\")\n    @pytest.mark.parametrize('setup', [('test_search_user_and_switch_accounts',)], indirect=True)\n    def test_search_user_and_switch_accounts(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入搜索页面\n        2. 搜索指定用户名\n        3. 验证默认在Posts栏\n        4. 切换到Accounts栏\n        5. 验证出现对应的用户\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n    \n        # 初始化社交搜索页面对象\n        search_page = SocialSearchPage(d, platform)\n    \n        # 导航到搜索页面，点击inspiration按钮\n        inspiration = self.find_element(d, search_page.strategies.get(platform).get(\"inspiration\"))\n        assert inspiration, \"Failed to find inspiration button!\"\n        inspiration.click()\n        time.sleep(3)\n    \n        # 搜索用户（使用测试用户名）\n        test_username = \"autotest\"\n        search_success = search_page.search_user(test_username)\n        assert search_success, f\"Failed to search for user: {test_username}\"\n    \n        # 验证默认在Posts栏\n        photos_tab_active = search_page.check_posts_tab_active()\n        assert photos_tab_active, \"Photos tab should be active by default\"\n        log.info(\"✓ Photos tab is active by default\")\n    \n        # 切换到Accounts栏\n        switch_success = search_page.switch_to_accounts_tab()\n        assert switch_success, \"Failed to switch to Accounts tab\"\n        log.info(\"✓ Successfully switched to Accounts tab\")\n    \n        # 验证出现对应的用户\n        user_list = search_page.get_user_list()\n        assert len(user_list) > 0, \"No users found in Accounts tab\"\n    \n        # 验证搜索的用户在结果中\n        user_found = any(test_username.lower() in user.text.lower() for user in user_list)\n        assert user_found, f\"User '{test_username}' not found in search results\"\n        log.info(f\"✓ User '{test_username}' found in search results\")\n    \n        target_user = user_list[0].text.lower()\n    \n        # 点击用户头像\n        avatar_click_success = search_page.click_user_avatar(target_user)\n>       assert avatar_click_success, f\"Failed to click avatar for user: {target_user}\"\nE       AssertionError: Failed to click avatar for user: autotest\nE       assert False\n\ntest_112741_social_search_accounts_verify.py:82: AssertionError"}, "description": "\n        测试步骤：\n        1. 进入搜索页面\n        2. 搜索指定用户名\n        3. 验证默认在Posts栏\n        4. 切换到Accounts栏\n        5. 验证出现对应的用户\n        ", "parameters": [{"name": "setup", "value": "('test_search_user_and_switch_accounts',)"}], "start": *************, "stop": *************, "uuid": "8822317e-4e4f-48d9-9754-a7348ac8c094", "historyId": "130c67e3a58f1c6e4bc67cd413068e54", "testCaseId": "9bef6ce639e24b9ebddf2ab4e370795e", "fullName": "src.EC.tests.social.test_112741_social_search_accounts_verify.TestSocialSearchAccountsVerify#test_search_user_and_switch_accounts", "labels": [{"name": "story", "value": "Social搜索账号功能验证"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.social"}, {"name": "suite", "value": "test_112741_social_search_accounts_verify"}, {"name": "subSuite", "value": "TestSocialSearchAccountsVerify"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "93956-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.social.test_112741_social_search_accounts_verify"}]}