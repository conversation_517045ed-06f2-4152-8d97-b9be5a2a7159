{"name": "搜索对应用户名并切换到Accounts栏测试", "status": "passed", "description": "\n        测试步骤：\n        1. 进入搜索页面\n        2. 搜索指定用户名\n        3. 验证默认在Posts栏\n        4. 切换到Accounts栏\n        5. 验证出现对应的用户\n        ", "parameters": [{"name": "setup", "value": "('test_search_user_and_switch_accounts',)"}], "start": *************, "stop": *************, "uuid": "e7d4cde9-21d5-47f9-8c56-1b789fc6285e", "historyId": "130c67e3a58f1c6e4bc67cd413068e54", "testCaseId": "9bef6ce639e24b9ebddf2ab4e370795e", "fullName": "src.EC.tests.social.test_112741_social_search_accounts_verify.TestSocialSearchAccountsVerify#test_search_user_and_switch_accounts", "labels": [{"name": "story", "value": "Social搜索账号功能验证"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.social"}, {"name": "suite", "value": "test_112741_social_search_accounts_verify"}, {"name": "subSuite", "value": "TestSocialSearchAccountsVerify"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "94737-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.social.test_112741_social_search_accounts_verify"}]}