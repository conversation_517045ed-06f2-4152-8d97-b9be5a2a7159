{"name": "首页CMS collection检查", "status": "broken", "statusDetails": {"message": "selenium.common.exceptions.StaleElementReferenceException: Message: The previously found element \"\"Add item to cart\" Button\" is not present in the current view anymore. Make sure the application UI has the expected state. Original error: No matches found for Elements matching predicate 'BLOCKPREDICATE(0x105b894d0)' from input {(\n    <PERSON><PERSON>, {{59.0, 655.7}, {36.0, 36.0}}, label: 'Remove item',\n    <PERSON><PERSON>, {{77.0, 655.7}, {55.0, 36.0}}, label: 'Current number of items in cart: 2',\n    <PERSON><PERSON>, {{114.0, 655.7}, {36.0, 36.0}}, label: 'Add item',\n    <PERSON><PERSON>, {{20.0, 755.3}, {134.0, 39.3}}, identifier: 'tv_product_name', label: 'Green Onion 1 bunch',\n    <PERSON><PERSON>, {{15.0, 799.7}, {61.0, 19.0}}, label: '1K+ sold',\n    <PERSON><PERSON>, {{17.0, 708.7}, {108.0, 21.0}}, label: '$0.49/ea for 2+', Disabled\n)}; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\nStacktrace:\nStaleElementReferenceError: The previously found element \"\"Add item to cart\" Button\" is not present in the current view anymore. Make sure the application UI has the expected state. Original error: No matches found for Elements matching predicate 'BLOCKPREDICATE(0x105b894d0)' from input {(\n    Button, {{59.0, 655.7}, {36.0, 36.0}}, label: 'Remove item',\n    Button, {{77.0, 655.7}, {55.0, 36.0}}, label: 'Current number of items in cart: 2',\n    Button, {{114.0, 655.7}, {36.0, 36.0}}, label: 'Add item',\n    Button, {{20.0, 755.3}, {134.0, 39.3}}, identifier: 'tv_product_name', label: 'Green Onion 1 bunch',\n    Button, {{15.0, 799.7}, {61.0, 19.0}}, label: '1K+ sold',\n    Button, {{17.0, 708.7}, {108.0, 21.0}}, label: '$0.49/ea for 2+', Disabled\n)}\n    at errorFromW3CJsonCode (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:1085:25)\n    at ProxyRequestError.getActualError (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:954:14)\n    at JWProxy.proxyReqRes (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:415:58)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)", "trace": "self = <test_102302_home_cms_collection_check.TestHomeCMSCollectionCheck object at 0x103df0dd0>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"efdfe512-68ce-4364-a163-79fbce133154\")>\nsetup = None\n\n    @allure.title(\"首页CMS collection检查\")\n    @pytest.mark.parametrize('setup', [('test_102302_home_cms_collection_check', )], indirect=True)\n    @pytest.mark.ios\n    def test_102302_home_cms_collection_check(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1.\n        \"\"\"\n        # todo 安卓首页的cms组件没有加标识符，无法识别\n        d = get_driver\n        platform = get_platform\n        hp = HomePage(d, platform)\n    \n        # 关掉首页广告\n        adv = self.find_element(_driver=d, ele=hp.strategy.get(\"popup_close\"))\n        if adv:\n            adv.click()\n    \n        # 需要向下滑动，否则editor's pick有可能显示不出来\n        self.swipe_screen(d, 0.4)\n    \n        # 1. 进入首页，获取editor's pick并校验\n        editors_pick_container = hp.get_home_cms_collection('editors_pick_container')\n        assert editors_pick_container, f\"Failed to find editors pick container, {editors_pick_container}\"\n        editors_pick_products = hp.get_home_products_in_cms(editors_pick_container)\n        assert editors_pick_products, f\"Failed to find editors pick products, {editors_pick_products}\"\n        for product in editors_pick_products:\n            add_to_cart_button = product.find_element(*hp.strategy.get('home_add_to_cart'))\n            add_to_cart_button.click()\n>           assert add_to_cart_button.is_enabled()\n\ntest_102302_home_cms_collection_check.py:59: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webelement.py:192: in is_enabled\n    return self._execute(Command.IS_ELEMENT_ENABLED)[\"value\"]\n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webelement.py:395: in _execute\n    return self._parent.execute(command, params)\n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute\n    self.error_handler.check_response(response)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x106715670>\nresponse = {'status': 404, 'value': '{\"value\":{\"error\":\"stale element reference\",\"message\":\"The previously found element \\\\\"\\\\\"Ad...iver/lib/jsonwp-proxy/proxy.js:415:58)\\\\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\"}}'}\n\n    def check_response(self, response: Dict[str, Any]) -> None:\n        \"\"\"\n        https://www.w3.org/TR/webdriver/#errors\n        \"\"\"\n        payload = response.get('value', '')\n        if isinstance(payload, dict):\n            payload_dict = payload\n        else:\n            try:\n                payload_dict = json.loads(payload)\n            except (json.JSONDecodeError, TypeError):\n                return\n            if not isinstance(payload_dict, dict):\n                return\n        value = payload_dict.get('value')\n        if not isinstance(value, dict):\n            return\n        error = value.get('error')\n        if not error:\n            return\n    \n        message = value.get('message', error)\n        stacktrace = value.get('stacktrace', '')\n        # In theory, we should also be checking HTTP status codes.\n        # Java client, for example, prints a warning if the actual `error`\n        # value does not match to the response's HTTP status code.\n        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(\n            error, sel_exceptions.WebDriverException\n        )\n        if exception_class is sel_exceptions.WebDriverException and message:\n            if message == 'No such context found.':\n                exception_class = appium_exceptions.NoSuchContextException\n            elif message == 'That command could not be executed in the current context.':\n                exception_class = appium_exceptions.InvalidSwitchToTargetException\n    \n        if exception_class is sel_exceptions.UnexpectedAlertPresentException:\n            raise sel_exceptions.UnexpectedAlertPresentException(\n                msg=message,\n                stacktrace=format_stacktrace(stacktrace),\n                alert_text=value.get('data'),\n            )\n>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))\nE       selenium.common.exceptions.StaleElementReferenceException: Message: The previously found element \"\"Add item to cart\" Button\" is not present in the current view anymore. Make sure the application UI has the expected state. Original error: No matches found for Elements matching predicate 'BLOCKPREDICATE(0x105b894d0)' from input {(\nE           Button, {{59.0, 655.7}, {36.0, 36.0}}, label: 'Remove item',\nE           Button, {{77.0, 655.7}, {55.0, 36.0}}, label: 'Current number of items in cart: 2',\nE           Button, {{114.0, 655.7}, {36.0, 36.0}}, label: 'Add item',\nE           Button, {{20.0, 755.3}, {134.0, 39.3}}, identifier: 'tv_product_name', label: 'Green Onion 1 bunch',\nE           Button, {{15.0, 799.7}, {61.0, 19.0}}, label: '1K+ sold',\nE           Button, {{17.0, 708.7}, {108.0, 21.0}}, label: '$0.49/ea for 2+', Disabled\nE       )}; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\nE       Stacktrace:\nE       StaleElementReferenceError: The previously found element \"\"Add item to cart\" Button\" is not present in the current view anymore. Make sure the application UI has the expected state. Original error: No matches found for Elements matching predicate 'BLOCKPREDICATE(0x105b894d0)' from input {(\nE           Button, {{59.0, 655.7}, {36.0, 36.0}}, label: 'Remove item',\nE           Button, {{77.0, 655.7}, {55.0, 36.0}}, label: 'Current number of items in cart: 2',\nE           Button, {{114.0, 655.7}, {36.0, 36.0}}, label: 'Add item',\nE           Button, {{20.0, 755.3}, {134.0, 39.3}}, identifier: 'tv_product_name', label: 'Green Onion 1 bunch',\nE           Button, {{15.0, 799.7}, {61.0, 19.0}}, label: '1K+ sold',\nE           Button, {{17.0, 708.7}, {108.0, 21.0}}, label: '$0.49/ea for 2+', Disabled\nE       )}\nE           at errorFromW3CJsonCode (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:1085:25)\nE           at ProxyRequestError.getActualError (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:954:14)\nE           at JWProxy.proxyReqRes (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:415:58)\nE           at processTicksAndRejections (node:internal/process/task_queues:95:5)\n\n../../../../qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: StaleElementReferenceException"}, "description": "\n        测试步骤：\n        1.\n        ", "parameters": [{"name": "setup", "value": "('test_102302_home_cms_collection_check',)"}], "start": 1751001805052, "stop": 1751001820334, "uuid": "ba42ff87-66fb-40f0-b3ce-95d85eabeaf3", "historyId": "10fb05f86a2e8fdf1981ae4387284d2b", "testCaseId": "a4dcf4e3fb24f2ca961c09a27bde6f92", "fullName": "src.EC.tests.home.test_102302_home_cms_collection_check.TestHomeCMSCollectionCheck#test_102302_home_cms_collection_check", "labels": [{"name": "story", "value": "[102302]首页CMS collection检查"}, {"name": "tag", "value": "ios"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.home"}, {"name": "suite", "value": "test_102302_home_cms_collection_check"}, {"name": "subSuite", "value": "TestHomeCMSCollectionCheck"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "1493-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.home.test_102302_home_cms_collection_check"}]}