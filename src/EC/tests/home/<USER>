import os
import allure
import pytest

from src.EC.page_objects.home.home_page import HomePage
from src.EC.page_objects.home.search import SearchPage
from src.EC.tests.base_case import BaseCase
from src.api.commonapi.commfunc import empty_cart
from src.common.recording import base64_to_mp4


@allure.story("[111131][Android]安卓anycart搜索UI验证")
class TestHomeCategoryUIUXCheck(BaseCase):

    pytestmark = [pytest.mark.regression]

    @pytest.fixture(scope='function')
    def setup(self, request, autotest_header, android_header, get_driver):
        empty_cart(android_header)
        empty_cart(autotest_header)
        get_driver.start_recording_screen()
        yield
        empty_cart(android_header)
        empty_cart(autotest_header)
        video_data = get_driver.stop_recording_screen()
        if not os.path.exists(f'./video/{os.getenv("BUILD_NUMBER", 0)}'):
            os.makedirs(f'./video/{os.getenv("BUILD_NUMBER", 0)}')
        base64_to_mp4(video_data, f'./video/{os.getenv("BUILD_NUMBER", 0)}/{request.param[0]}.mp4')

    @allure.title("首页category UI/UX检查")
    @pytest.mark.parametrize('setup', [('test_109472_home_category_ui_ux_check', )], indirect=True)
    @pytest.mark.android
    def test_109472_home_category_ui_ux_check(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. https://metersphere.sayweee.net/#/track/case/edit/af9ccdc8-ae98-651f-a3bb-e7faff293af2?projectId=4f014f7d-0089-41c3-ab11-6993d2c89860
        """
        # 这个用例还在做实验，IOS与安卓首页关于category的显示并不一样， ios的banner下方还显示一排category，安卓没有
        d = get_driver
        platform = get_platform
        hp = HomePage(d, platform)

        # 关掉首页广告
        adv = self.find_element(_driver=d, ele=hp.strategy.get("popup_close"))
        if adv:
            adv.click()

        # 1. 进入首页，查看分类显示
        hp.switch_store(hp.strategy.get("chinese_store"))
        assert hp.check_categories_on_home(), f"Failed to check home categories after switch store"

        hp.switch_store(hp.strategy.get("japanese_store"))
        assert hp.check_categories_on_home(), f"Failed to check home categories after switch store"

        hp.switch_store(hp.strategy.get("korean_store"))
        assert hp.check_categories_on_home(), f"Failed to check home categories after switch store"

        hp.switch_store(hp.strategy.get("vietnamese_store"))
        assert hp.check_categories_on_home(), f"Failed to check home categories after switch store"

        hp.switch_store(hp.strategy.get("filipino_store"))
        assert hp.check_categories_on_home(), f"Failed to check home categories after switch store"

        hp.switch_store(hp.strategy.get("thailand_store"))
        assert hp.check_categories_on_home(), f"Failed to check home categories after switch store"

        hp.switch_store(hp.strategy.get("explorer_store"))
        assert hp.check_categories_on_home(), f"Failed to check home categories after switch store"

        # 切回chinese store
        hp.switch_store(hp.strategy.get("chinese_store"))




