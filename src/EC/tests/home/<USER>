import os
import allure
import pytest

from src.EC.page_objects.home.home_page import HomePage
from src.EC.page_objects.home.search import SearchPage
from src.EC.tests.base_case import BaseCase
from src.api.commonapi.commfunc import empty_cart
from src.common.recording import base64_to_mp4


@allure.story("[111131][Android]安卓anycart搜索UI验证")
class TestSwitchStore(BaseCase):

    pytestmark = [pytest.mark.regression]

    @pytest.fixture(scope='function')
    def setup(self, request, autotest_header, android_header, get_driver):
        empty_cart(android_header)
        empty_cart(autotest_header)
        get_driver.start_recording_screen()
        yield
        empty_cart(android_header)
        empty_cart(autotest_header)
        video_data = get_driver.stop_recording_screen()
        if not os.path.exists(f'./video/{os.getenv("BUILD_NUMBER", 0)}'):
            os.makedirs(f'./video/{os.getenv("BUILD_NUMBER", 0)}')
        base64_to_mp4(video_data, f'./video/{os.getenv("BUILD_NUMBER", 0)}/{request.param[0]}.mp4')

    @allure.title("【102469】 首页store - 验证不同入口切换Store")
    @pytest.mark.parametrize('setup', [('test_102469_switch_different_store', )], indirect=True)
    @pytest.mark.android
    def test_102469_switch_different_store(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. https://metersphere.sayweee.net/#/track/case/edit/7b30039d-9a71-78ad-2a40-85ba8d85443a?projectId=4f014f7d-0089-41c3-ab11-6993d2c89860
        """
        d = get_driver
        platform = get_platform
        hp = HomePage(d, platform)

        # 关掉首页广告
        adv = self.find_element(_driver=d, ele=hp.strategy.get("popup_close"))
        if adv:
            adv.click()

        # 1. 首页切换store
        hp.switch_store(hp.strategy.get("chinese_store"))
        assert hp.check_home_product_after_switch_store(), f"Failed to check home product after switch store"

        hp.switch_store(hp.strategy.get("japanese_store"))
        assert hp.check_home_product_after_switch_store(), f"Failed to check home product after switch store"

        hp.switch_store(hp.strategy.get("korean_store"))
        assert hp.check_home_product_after_switch_store(), f"Failed to check home product after switch store"

        hp.switch_store(hp.strategy.get("vietnamese_store"))
        assert hp.check_home_product_after_switch_store(), f"Failed to check home product after switch store"

        hp.switch_store(hp.strategy.get("filipino_store"))
        assert hp.check_home_product_after_switch_store(), f"Failed to check home product after switch store"

        hp.switch_store(hp.strategy.get("thailand_store"))
        assert hp.check_home_product_after_switch_store(), f"Failed to check home product after switch store"

        hp.switch_store(hp.strategy.get("explorer_store"))
        assert hp.check_home_product_after_switch_store(), f"Failed to check home product after switch store"

        # 切回chinese store
        hp.switch_store(hp.strategy.get("chinese_store"))
        assert hp.check_home_product_after_switch_store(), f"Failed to check home product after switch store"




