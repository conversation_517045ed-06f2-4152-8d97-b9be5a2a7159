import os
import allure
import pytest

from src.EC.page_objects.home.home_page import HomePage
from src.EC.page_objects.home.search import SearchPage
from src.EC.tests.base_case import BaseCase
from src.api.commonapi.commfunc import empty_cart
from src.common.recording import base64_to_mp4


@allure.story("[102302]首页CMS collection检查")
class TestHomeCMSCollectionCheck(BaseCase):

    pytestmark = [pytest.mark.regression]

    @pytest.fixture(scope='function')
    def setup(self, request, autotest_header, android_header, get_driver):
        empty_cart(android_header)
        empty_cart(autotest_header)
        get_driver.start_recording_screen()
        yield
        empty_cart(android_header)
        empty_cart(autotest_header)
        video_data = get_driver.stop_recording_screen()
        if not os.path.exists(f'./video/{os.getenv("BUILD_NUMBER", 0)}'):
            os.makedirs(f'./video/{os.getenv("BUILD_NUMBER", 0)}')
        base64_to_mp4(video_data, f'./video/{os.getenv("BUILD_NUMBER", 0)}/{request.param[0]}.mp4')

    @allure.title("首页CMS collection-editors pick检查")
    @pytest.mark.parametrize('setup', [('test_102302_01_home_editors_pick_check', )], indirect=True)
    @pytest.mark.ios
    def test_102302_01_home_editors_pick_check(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1.
        """
        # todo 安卓首页的cms组件没有加标识符，无法识别
        d = get_driver
        platform = get_platform
        hp = HomePage(d, platform)

        # 关掉首页广告
        adv = self.find_element(_driver=d, ele=hp.strategy.get("popup_close"))
        if adv:
            adv.click()

        # 需要向下滑动，否则editor's pick有可能显示不出来
        self.swipe_screen(d, 0.4)

        # 1. 进入首页，获取editor's pick并校验
        editors_pick_container = hp.get_home_cms_collection('editors_pick_container')
        if editors_pick_container:
            assert editors_pick_container, f"Failed to find editors pick container, {editors_pick_container}"
            editors_pick_products = hp.get_home_products_in_cms(editors_pick_container)
            assert editors_pick_products, f"Failed to find editors pick products, {editors_pick_products}"
            # for product in editors_pick_products:
            #     assert product.find_element(*hp.strategy.get('home_add_to_cart')).is_enabled()

            editors_add_to_cart = editors_pick_container.find_elements(*hp.strategy.get('home_add_to_cart'))
            for index, i in enumerate(editors_add_to_cart):
                if index == 2:
                    break
                i.click()

            self.scroll_to_element(d, editors_pick_products[-1], editors_pick_products[0])
        else:
            pytest.mark.skip("No editors pick container found")

    @allure.title("首页CMS collection-editors pick检查")
    @pytest.mark.parametrize('setup', [('test_102302_02_home_everyday_deals_check',)], indirect=True)
    @pytest.mark.ios
    def test_102302_02_home_everyday_deals_check(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1.
        """
        # todo 安卓首页的cms组件没有加标识符，无法识别
        d = get_driver
        platform = get_platform
        hp = HomePage(d, platform)

        # 需要向下滑动，否则everyday deals有可能显示不出来
        self.swipe_screen_until(d, hp.strategy.get('everyday_deals_container'), 0.4)

        # 1. 进入首页，获取everyday deals并校验
        everyday_deals_container = hp.get_home_cms_collection('everyday_deals_container')
        if everyday_deals_container:
            assert everyday_deals_container, f"Failed to find editors pick container, {everyday_deals_container}"
            everyday_deals_products = hp.get_home_products_in_cms(everyday_deals_container)
            assert everyday_deals_products, f"Failed to find editors pick products, {everyday_deals_products}"

            everyday_deals_add_to_cart = everyday_deals_container.find_elements(*hp.strategy.get('home_add_to_cart'))
            for index, i in enumerate(everyday_deals_add_to_cart):
                if index == 2:
                    break
                i.click()

            self.scroll_to_element(d, everyday_deals_products[-1], everyday_deals_products[0])
        else:
            pytest.mark.skip("No everyday deals container found")



