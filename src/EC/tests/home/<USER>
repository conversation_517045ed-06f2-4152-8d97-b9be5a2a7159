import os
import allure
import pytest

from src.EC.page_objects.home.search import SearchPage
from src.EC.tests.base_case import BaseCase
from src.api.commonapi.commfunc import empty_cart
from src.common.recording import base64_to_mp4


@allure.story("[111131][Android]安卓anycart搜索UI验证")
class TestSingleCartCheckoutFlow(BaseCase):

    pytestmark = [pytest.mark.single_cart, pytest.mark.regression]

    @pytest.fixture(scope='function')
    def setup(self, request, autotest_header, android_header, get_driver):
        empty_cart(android_header)
        empty_cart(autotest_header)
        get_driver.start_recording_screen()
        yield
        empty_cart(android_header)
        empty_cart(autotest_header)
        video_data = get_driver.stop_recording_screen()
        if not os.path.exists(f'./video/{os.getenv("BUILD_NUMBER", 0)}'):
            os.makedirs(f'./video/{os.getenv("BUILD_NUMBER", 0)}')
        base64_to_mp4(video_data, f'./video/{os.getenv("BUILD_NUMBER", 0)}/{request.param[0]}.mp4')

    @allure.title("[111131][Android]安卓anycart搜索UI验证")
    @pytest.mark.parametrize('setup', [('test_107945_single_pantry_cart_checkout_page', )], indirect=True)
    @pytest.mark.android
    def test_111131_android_anycart_search_check(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. https://metersphere.sayweee.net/#/track/case/edit/745da743-5031-496f-8419-07a3a698fae7?projectId=4f014f7d-0089-41c3-ab11-6993d2c89860
        """
        d = get_driver
        platform = get_platform
        sp = SearchPage(d, platform)

        # 关掉首页广告
        adv = self.find_element(_driver=d, ele=sp.strategy.get("popup_close"))
        if adv:
            adv.click()

        # 1. 点击搜索框输入keyword
        sp.search_on_home_page("rice")

        # 2. 搜索列表校验
        assert sp.check_search_list_displayed(), "搜索列表未显示"

        # 3. 搜索结果校验及操作
        sp.check_search_result_displayed()

        # 4. 切换filter
        sp.switch_filter()

        # 5. 切换filter之后校验搜索结果
        sp.check_result_after_switch_filter()

        # 6. 返回首页
        sp.back_to_home_page()
