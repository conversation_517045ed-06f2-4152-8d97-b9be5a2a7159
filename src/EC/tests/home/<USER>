# Freshly Made合集测试

## 测试概述

本测试用例专门针对首页的"Freshly Made"合集进行验证，确保该CMS组件正确显示、产品加载正常、交互功能完整。

## 功能覆盖

### 测试场景
验证首页Freshly Made合集的完整功能，包括：
- 合集容器的正确显示
- 合集内产品的加载和展示
- 产品加购功能的正常工作
- 合集内产品的滚动浏览

### 验证要点
1. **合集容器存在** - Freshly Made合集在首页正确显示
2. **产品正确加载** - 合集内包含有效的产品数据
3. **加购按钮功能** - 每个产品的加购按钮可正常使用
4. **滚动浏览功能** - 合集内产品支持横向滚动浏览
5. **跨平台兼容** - Android和iOS平台均正常工作

## 测试用例

### `test_102302_02_home_freshly_made_check`
**首页CMS collection-freshly made合集检查**

**前置条件：**
- 应用已启动并进入首页
- 网络连接正常
- Freshly Made合集在CMS中已配置

**测试步骤：**
1. **进入首页并关闭广告弹窗**
   - 检测并关闭首页广告弹窗
   - 确保页面处于可操作状态

2. **定位Freshly Made合集**
   - 向下滑动页面直到找到Freshly Made合集
   - 使用`swipe_screen_until`方法确保合集可见

3. **验证合集容器**
   - 验证Freshly Made容器元素存在
   - 确认合集正确加载和显示

4. **验证合集内产品**
   - 获取合集内的所有产品元素
   - 验证产品数量大于0
   - 记录找到的产品数量

5. **测试加购功能**
   - 获取所有加购按钮
   - 验证按钮处于可用状态
   - 点击前2个产品的加购按钮
   - 统计成功加购的产品数量

6. **测试滚动浏览**
   - 如果产品数量大于1，测试横向滚动
   - 从第一个产品滚动到最后一个产品
   - 验证滚动功能正常工作

**预期结果：**
- Freshly Made合集正确显示
- 合集内包含有效产品
- 加购功能正常工作
- 滚动浏览功能正常
- 至少一个产品成功加入购物车

## 运行方式

### 运行Freshly Made测试
```bash
# 运行特定测试用例
pytest src/EC/tests/home/<USER>

# 运行所有CMS collection测试
pytest src/EC/tests/home/<USER>

# 使用标记运行
mark=regression python run.py
```

### 环境变量设置
```bash
# 设置平台
export platform=Android  # 或 iOS

# 运行测试
python run.py
```

## 页面对象

### HomePage类
位置：`src/EC/page_objects/home/<USER>

**相关方法：**
- `get_home_cms_collection(collection_name)` - 获取指定CMS合集容器
- `get_home_products_in_cms(collection_ele)` - 获取合集内的产品列表

**元素定位策略：**

#### Android平台
```python
'freshly_made_container': (AppiumBy.XPATH, '//android.widget.LinearLayout[contains(@content-desc, "fresh_daily")]')
'home_products': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().resourceId("com.sayweee.weee:id/layout_product")')
'home_add_to_cart': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().resourceId("com.sayweee.weee:id/btn_add_to_cart")')
```

#### iOS平台
```python
'freshly_made_container': (AppiumBy.ACCESSIBILITY_ID, 'cm_product_line_tabs_fresh_daily')
'home_products': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeCell[`name == "layout_product"`]')
'home_add_to_cart': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Add item to cart"`]')
```

## 测试数据

### CMS组件标识
- **Android**: 通过content-desc属性包含"fresh_daily"定位
- **iOS**: 通过accessibility-id为"cm_product_line_tabs_fresh_daily"定位

### 验证指标
- 产品数量：> 0
- 加购成功率：> 0（至少一个产品成功加购）
- 滚动功能：正常工作（当产品数量 > 1时）

## 注意事项

### 测试环境要求
1. **CMS配置**：确保Freshly Made合集在测试环境中已正确配置
2. **产品数据**：合集中应包含有效的产品数据
3. **网络连接**：稳定的网络连接以加载产品信息
4. **购物车状态**：测试前后会清空购物车

### 已知限制
1. **CMS依赖**：测试依赖于CMS系统的配置和数据
2. **产品可用性**：产品的库存状态可能影响加购功能
3. **平台差异**：Android和iOS的元素定位策略不同
4. **滑动距离**：页面滑动距离可能需要根据实际情况调整

### 故障排除
1. **合集不显示**：
   - 检查CMS配置是否正确
   - 确认滑动距离是否足够
   - 验证元素定位器是否准确

2. **产品加载失败**：
   - 检查网络连接
   - 验证产品数据是否有效
   - 确认产品元素定位器

3. **加购失败**：
   - 检查产品库存状态
   - 验证加购按钮元素定位
   - 确认用户登录状态

4. **滚动失败**：
   - 检查产品数量是否足够
   - 验证滚动元素定位
   - 调整滚动参数

## 扩展建议

### 未来改进
1. **产品详情验证**：验证产品标题、价格、图片等详细信息
2. **加购反馈验证**：验证加购后的UI反馈和购物车更新
3. **性能测试**：测试合集加载时间和响应性能
4. **A/B测试支持**：支持不同版本的合集布局测试
5. **数据驱动测试**：使用外部数据源驱动测试用例

### 测试场景扩展
- 空合集状态的处理
- 网络异常时的错误处理
- 产品售罄时的UI状态
- 不同设备尺寸的适配测试

## 维护指南

### 定期检查
- CMS合集配置是否有变更
- 产品数据是否保持有效
- 元素定位器是否仍然准确
- 新版本应用的兼容性

### 更新建议
- 根据CMS配置变更更新测试逻辑
- 根据UI变更调整元素定位器
- 根据业务需求添加新的验证点
- 优化测试执行效率和稳定性

## 相关文档
- [HomePage CMS Collection测试总览](./test_102302_home_cms_collection_check.py)
- [HomePage页面对象文档](../page_objects/home/<USER>
- [基础测试用例类文档](../base_case.py)
