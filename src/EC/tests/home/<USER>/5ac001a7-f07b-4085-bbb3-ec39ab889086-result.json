{"name": "首页CMS collection-editors pick检查", "status": "passed", "description": "\n        测试步骤：\n        1.\n        ", "parameters": [{"name": "setup", "value": "('test_102302_02_home_everyday_deals_check',)"}], "start": 1751003804290, "stop": 1751003824722, "uuid": "2042c245-84d0-490a-a63e-09e07afc7657", "historyId": "6013e202ea87c40ccf3275c4b82969eb", "testCaseId": "4e19685ea01e882a0128130d31c408c8", "fullName": "src.EC.tests.home.test_102302_home_cms_collection_check.TestHomeCMSCollectionCheck#test_102302_02_home_everyday_deals_check", "labels": [{"name": "story", "value": "[102302]首页CMS collection检查"}, {"name": "tag", "value": "ios"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.home"}, {"name": "suite", "value": "test_102302_home_cms_collection_check"}, {"name": "subSuite", "value": "TestHomeCMSCollectionCheck"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "2067-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.home.test_102302_home_cms_collection_check"}]}