{"name": "首页category UI/UX检查", "status": "broken", "statusDetails": {"message": "selenium.common.exceptions.NoSuchElementException: Message: An element could not be located on the page using the given search parameters.; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\nNoSuchElementError: An element could not be located on the page using the given search parameters.\n    at XCUITestDriver.doNativeFind (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:143:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at XCUITestDriver.findNativeElementOrElements (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:97:12)\n    at XCUITestDriver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:24:14)\n    at XCUITestDriver.findElOrElsWithProcessing (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)\n    at XCUITestDriver.findElementFromElement (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:88:12)", "trace": "self = <test_109472_home_category_ui_ux_check.TestHomeCategoryUIUXCheck object at 0x1129b74a0>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"ee4be72b-a386-4445-b17a-37db0ac57c1d\")>\nsetup = None\n\n    @allure.title(\"首页category UI/UX检查\")\n    @pytest.mark.parametrize('setup', [('test_109472_home_category_ui_ux_check', )], indirect=True)\n    @pytest.mark.android\n    def test_109472_home_category_ui_ux_check(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1. https://metersphere.sayweee.net/#/track/case/edit/af9ccdc8-ae98-651f-a3bb-e7faff293af2?projectId=4f014f7d-0089-41c3-ab11-6993d2c89860\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n        hp = HomePage(d, platform)\n    \n        # 关掉首页广告\n        adv = self.find_element(_driver=d, ele=hp.strategy.get(\"popup_close\"))\n        if adv:\n            adv.click()\n    \n        # 1. 进入首页，查看分类显示\n        hp.switch_store(hp.strategy.get(\"chinese_store\"))\n>       assert hp.check_categories_on_home(), f\"Failed to check home categories after switch store\"\n\ntest_109472_home_category_ui_ux_check.py:49: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../page_objects/home/<USER>\n    category_title = category.find_element(*self.strategy.get('category_title')).get_attribute('text')\n../../../../qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/webelement.py:114: in find_element\n    return self._execute(RemoteCommand.FIND_CHILD_ELEMENT, {'using': by, 'value': value})['value']\n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webelement.py:395: in _execute\n    return self._parent.execute(command, params)\n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute\n    self.error_handler.check_response(response)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x11294e180>\nresponse = {'status': 404, 'value': '{\"value\":{\"error\":\"no such element\",\"message\":\"An element could not be located on the page u...lement (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:88:12)\"}}'}\n\n    def check_response(self, response: Dict[str, Any]) -> None:\n        \"\"\"\n        https://www.w3.org/TR/webdriver/#errors\n        \"\"\"\n        payload = response.get('value', '')\n        if isinstance(payload, dict):\n            payload_dict = payload\n        else:\n            try:\n                payload_dict = json.loads(payload)\n            except (json.JSONDecodeError, TypeError):\n                return\n            if not isinstance(payload_dict, dict):\n                return\n        value = payload_dict.get('value')\n        if not isinstance(value, dict):\n            return\n        error = value.get('error')\n        if not error:\n            return\n    \n        message = value.get('message', error)\n        stacktrace = value.get('stacktrace', '')\n        # In theory, we should also be checking HTTP status codes.\n        # Java client, for example, prints a warning if the actual `error`\n        # value does not match to the response's HTTP status code.\n        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(\n            error, sel_exceptions.WebDriverException\n        )\n        if exception_class is sel_exceptions.WebDriverException and message:\n            if message == 'No such context found.':\n                exception_class = appium_exceptions.NoSuchContextException\n            elif message == 'That command could not be executed in the current context.':\n                exception_class = appium_exceptions.InvalidSwitchToTargetException\n    \n        if exception_class is sel_exceptions.UnexpectedAlertPresentException:\n            raise sel_exceptions.UnexpectedAlertPresentException(\n                msg=message,\n                stacktrace=format_stacktrace(stacktrace),\n                alert_text=value.get('data'),\n            )\n>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))\nE       selenium.common.exceptions.NoSuchElementException: Message: An element could not be located on the page using the given search parameters.; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nE       Stacktrace:\nE       NoSuchElementError: An element could not be located on the page using the given search parameters.\nE           at XCUITestDriver.doNativeFind (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:143:13)\nE           at processTicksAndRejections (node:internal/process/task_queues:95:5)\nE           at XCUITestDriver.findNativeElementOrElements (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:97:12)\nE           at XCUITestDriver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:24:14)\nE           at XCUITestDriver.findElOrElsWithProcessing (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)\nE           at XCUITestDriver.findElementFromElement (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:88:12)\n\n../../../../qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: NoSuchElementException"}, "description": "\n        测试步骤：\n        1. https://metersphere.sayweee.net/#/track/case/edit/af9ccdc8-ae98-651f-a3bb-e7faff293af2?projectId=4f014f7d-0089-41c3-ab11-6993d2c89860\n        ", "parameters": [{"name": "setup", "value": "('test_109472_home_category_ui_ux_check',)"}], "start": 1750921660457, "stop": 1750921695674, "uuid": "11882e5d-feea-49b3-a250-96cbd10cdc18", "historyId": "db6da1487a9bad2601480698d16127dd", "testCaseId": "c078575c89496788afe3f9124f492b43", "fullName": "src.EC.tests.home.test_109472_home_category_ui_ux_check.TestHomeCategoryUIUXCheck#test_109472_home_category_ui_ux_check", "labels": [{"name": "story", "value": "[111131][Android]安卓anycart搜索UI验证"}, {"name": "tag", "value": "android"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.home"}, {"name": "suite", "value": "test_109472_home_category_ui_ux_check"}, {"name": "subSuite", "value": "TestHomeCategoryUIUXCheck"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "84791-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.home.test_109472_home_category_ui_ux_check"}]}