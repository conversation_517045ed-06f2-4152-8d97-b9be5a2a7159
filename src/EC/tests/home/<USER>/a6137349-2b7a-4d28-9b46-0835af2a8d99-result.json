{"name": "首页CMS collection检查", "status": "broken", "statusDetails": {"message": "TypeError: Object of type WebDriver is not JSON serializable", "trace": "self = <test_102302_home_cms_collection_check.TestHomeCMSCollectionCheck object at 0x107315310>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"5c1dfa07-83f9-49a0-8586-4b9d8ae91f0e\")>\nsetup = None\n\n    @allure.title(\"首页CMS collection检查\")\n    @pytest.mark.parametrize('setup', [('test_102302_home_cms_collection_check', )], indirect=True)\n    @pytest.mark.ios\n    def test_102302_home_cms_collection_check(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1.\n        \"\"\"\n        # todo 安卓首页的cms组件没有加标识符，无法识别\n        d = get_driver\n        platform = get_platform\n        hp = HomePage(d, platform)\n    \n        # 关掉首页广告\n        adv = self.find_element(_driver=d, ele=hp.strategy.get(\"popup_close\"))\n        if adv:\n            adv.click()\n    \n        # 需要向下滑动，否则editor's pick有可能显示不出来\n        self.swipe_screen(d, 0.4)\n    \n        # 1. 进入首页，获取editor's pick并校验\n        editors_pick_container = hp.get_home_cms_collection('editors_pick_container')\n        assert editors_pick_container, f\"Failed to find editors pick container, {editors_pick_container}\"\n        editors_pick_products = hp.get_home_products_in_cms(editors_pick_container)\n        assert editors_pick_products, f\"Failed to find editors pick products, {editors_pick_products}\"\n        # for product in editors_pick_products:\n        #     assert product.find_element(*hp.strategy.get('home_add_to_cart')).is_enabled()\n    \n>       editors_add_to_cart = editors_pick_container.find_elements(d, hp.strategy.get('home_add_to_cart'))\n\ntest_102302_home_cms_collection_check.py:59: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../../../qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/webelement.py:144: in find_elements\n    return self._execute(RemoteCommand.FIND_CHILD_ELEMENTS, {'using': by, 'value': value})['value']\n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webelement.py:395: in _execute\n    return self._parent.execute(command, params)\n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:346: in execute\n    response = self.command_executor.execute(driver_command, params)\n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/remote_connection.py:298: in execute\n    data = utils.dump_json(params)\n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/utils.py:24: in dump_json\n    return json.dumps(json_struct)\n/opt/homebrew/Cellar/python@3.12/3.12.3/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/__init__.py:231: in dumps\n    return _default_encoder.encode(obj)\n/opt/homebrew/Cellar/python@3.12/3.12.3/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/encoder.py:200: in encode\n    chunks = self.iterencode(o, _one_shot=True)\n/opt/homebrew/Cellar/python@3.12/3.12.3/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/encoder.py:258: in iterencode\n    return _iterencode(o, 0)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <json.encoder.JSONEncoder object at 0x10403daf0>\no = <appium.webdriver.webdriver.WebDriver (session=\"5c1dfa07-83f9-49a0-8586-4b9d8ae91f0e\")>\n\n    def default(self, o):\n        \"\"\"Implement this method in a subclass such that it returns\n        a serializable object for ``o``, or calls the base implementation\n        (to raise a ``TypeError``).\n    \n        For example, to support arbitrary iterators, you could\n        implement default like this::\n    \n            def default(self, o):\n                try:\n                    iterable = iter(o)\n                except TypeError:\n                    pass\n                else:\n                    return list(iterable)\n                # Let the base class default method raise the TypeError\n                return super().default(o)\n    \n        \"\"\"\n>       raise TypeError(f'Object of type {o.__class__.__name__} '\n                        f'is not JSON serializable')\nE       TypeError: Object of type WebDriver is not JSON serializable\n\n/opt/homebrew/Cellar/python@3.12/3.12.3/Frameworks/Python.framework/Versions/3.12/lib/python3.12/json/encoder.py:180: TypeError"}, "description": "\n        测试步骤：\n        1.\n        ", "parameters": [{"name": "setup", "value": "('test_102302_home_cms_collection_check',)"}], "start": 1751002364544, "stop": 1751002402725, "uuid": "9495ee11-29a3-40f2-8a61-21e17d230b87", "historyId": "10fb05f86a2e8fdf1981ae4387284d2b", "testCaseId": "a4dcf4e3fb24f2ca961c09a27bde6f92", "fullName": "src.EC.tests.home.test_102302_home_cms_collection_check.TestHomeCMSCollectionCheck#test_102302_home_cms_collection_check", "labels": [{"name": "story", "value": "[102302]首页CMS collection检查"}, {"name": "tag", "value": "ios"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.home"}, {"name": "suite", "value": "test_102302_home_cms_collection_check"}, {"name": "subSuite", "value": "TestHomeCMSCollectionCheck"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "1681-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.home.test_102302_home_cms_collection_check"}]}