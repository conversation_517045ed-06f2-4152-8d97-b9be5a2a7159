{"name": "首页category UI/UX检查", "status": "passed", "description": "\n        测试步骤：\n        1. https://metersphere.sayweee.net/#/track/case/edit/af9ccdc8-ae98-651f-a3bb-e7faff293af2?projectId=4f014f7d-0089-41c3-ab11-6993d2c89860\n        ", "parameters": [{"name": "setup", "value": "('test_109472_home_category_ui_ux_check',)"}], "start": 1750921918603, "stop": 1750922134219, "uuid": "6f062798-66fe-497d-b756-8ceaa340be50", "historyId": "db6da1487a9bad2601480698d16127dd", "testCaseId": "c078575c89496788afe3f9124f492b43", "fullName": "src.EC.tests.home.test_109472_home_category_ui_ux_check.TestHomeCategoryUIUXCheck#test_109472_home_category_ui_ux_check", "labels": [{"name": "story", "value": "[111131][Android]安卓anycart搜索UI验证"}, {"name": "tag", "value": "android"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.home"}, {"name": "suite", "value": "test_109472_home_category_ui_ux_check"}, {"name": "subSuite", "value": "TestHomeCategoryUIUXCheck"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "84883-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.home.test_109472_home_category_ui_ux_check"}]}