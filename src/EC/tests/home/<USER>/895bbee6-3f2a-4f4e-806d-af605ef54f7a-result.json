{"name": "【102469】 首页store - 验证不同入口切换Store", "status": "passed", "description": "\n        测试步骤：\n        1. https://metersphere.sayweee.net/#/track/case/edit/7b30039d-9a71-78ad-2a40-85ba8d85443a?projectId=4f014f7d-0089-41c3-ab11-6993d2c89860\n        ", "parameters": [{"name": "setup", "value": "('test_102469_switch_different_store',)"}], "start": 1750841103864, "stop": 1750841283796, "uuid": "942b0f26-1304-43c1-ba0d-af347d2aef5d", "historyId": "1d5cc9d770726dbc6306e0f69a02322f", "testCaseId": "fb9e2c81866d03df1a5702b76696f308", "fullName": "src.EC.tests.home.test_102469_switch_different_store.TestSwitchStore#test_102469_switch_different_store", "labels": [{"name": "story", "value": "[111131][Android]安卓anycart搜索UI验证"}, {"name": "tag", "value": "android"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.home"}, {"name": "suite", "value": "test_102469_switch_different_store"}, {"name": "subSuite", "value": "TestSwitchStore"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "66993-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.home.test_102469_switch_different_store"}]}