{"name": "首页CMS collection检查", "status": "failed", "statusDetails": {"message": "AssertionError: assert False\n +  where False = <bound method WebElement.is_displayed of <appium.webdriver.webelement.WebElement (session=\"84acbac6-68b6-4a3c-b9ca-a5cc3b273b31\", element=\"27010000-0000-0000-3581-000000000000\")>>()\n +    where <bound method WebElement.is_displayed of <appium.webdriver.webelement.WebElement (session=\"84acbac6-68b6-4a3c-b9ca-a5cc3b273b31\", element=\"27010000-0000-0000-3581-000000000000\")>> = <appium.webdriver.webelement.WebElement (session=\"84acbac6-68b6-4a3c-b9ca-a5cc3b273b31\", element=\"27010000-0000-0000-3581-000000000000\")>.is_displayed\n +      where <appium.webdriver.webelement.WebElement (session=\"84acbac6-68b6-4a3c-b9ca-a5cc3b273b31\", element=\"27010000-0000-0000-3581-000000000000\")> = <bound method WebElement.find_element of <appium.webdriver.webelement.WebElement (session=\"84acbac6-68b6-4a3c-b9ca-a5cc3b273b31\", element=\"********-0000-0000-3581-000000000000\")>>(*('-ios class chain', '**/XCUIElementTypeButton[`name == \"Add item to cart\"`]'))\n +        where <bound method WebElement.find_element of <appium.webdriver.webelement.WebElement (session=\"84acbac6-68b6-4a3c-b9ca-a5cc3b273b31\", element=\"********-0000-0000-3581-000000000000\")>> = <appium.webdriver.webelement.WebElement (session=\"84acbac6-68b6-4a3c-b9ca-a5cc3b273b31\", element=\"********-0000-0000-3581-000000000000\")>.find_element\n +        and   ('-ios class chain', '**/XCUIElementTypeButton[`name == \"Add item to cart\"`]') = <built-in method get of dict object at 0x104665ec0>('home_add_to_cart')\n +          where <built-in method get of dict object at 0x104665ec0> = {'best_sellers_container': ('accessibility id', 'cm_item_trending'), 'categories': ('xpath', '//XCUIElementTypeCell[@name]'), 'category_container_on_home': ('accessibility id', 'cm_categories_4416926'), 'category_image': ('xpath', '//XCUIElementTypeOther/XCUIElementTypeImage'), ...}.get\n +            where {'best_sellers_container': ('accessibility id', 'cm_item_trending'), 'categories': ('xpath', '//XCUIElementTypeCell[@name]'), 'category_container_on_home': ('accessibility id', 'cm_categories_4416926'), 'category_image': ('xpath', '//XCUIElementTypeOther/XCUIElementTypeImage'), ...} = <src.EC.page_objects.home.home_page.HomePage object at 0x10451fec0>.strategy", "trace": "self = <test_102302_home_cms_collection_check.TestHomeCMSCollectionCheck object at 0x103b74a70>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"84acbac6-68b6-4a3c-b9ca-a5cc3b273b31\")>\nsetup = None\n\n    @allure.title(\"首页CMS collection检查\")\n    @pytest.mark.parametrize('setup', [('test_102302_home_cms_collection_check', )], indirect=True)\n    @pytest.mark.ios\n    def test_102302_home_cms_collection_check(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1.\n        \"\"\"\n        # todo 安卓首页的cms组件没有加标识符，无法识别\n        d = get_driver\n        platform = get_platform\n        hp = HomePage(d, platform)\n    \n        # 关掉首页广告\n        adv = self.find_element(_driver=d, ele=hp.strategy.get(\"popup_close\"))\n        if adv:\n            adv.click()\n    \n        # 需要向下滑动，否则editor's pick有可能显示不出来\n        self.swipe_screen(d, 0.4)\n    \n        # 1. 进入首页，获取editor's pick并校验\n        editors_pick_container = hp.get_home_cms_collection('editors_pick_container')\n        assert editors_pick_container, f\"Failed to find editors pick container, {editors_pick_container}\"\n        editors_pick_products = hp.get_home_products_in_cms(editors_pick_container)\n        assert editors_pick_products, f\"Failed to find editors pick products, {editors_pick_products}\"\n        for product in editors_pick_products:\n>           assert product.find_element(*hp.strategy.get('home_add_to_cart')).is_displayed()\nE           assert False\nE            +  where False = <bound method WebElement.is_displayed of <appium.webdriver.webelement.WebElement (session=\"84acbac6-68b6-4a3c-b9ca-a5cc3b273b31\", element=\"27010000-0000-0000-3581-000000000000\")>>()\nE            +    where <bound method WebElement.is_displayed of <appium.webdriver.webelement.WebElement (session=\"84acbac6-68b6-4a3c-b9ca-a5cc3b273b31\", element=\"27010000-0000-0000-3581-000000000000\")>> = <appium.webdriver.webelement.WebElement (session=\"84acbac6-68b6-4a3c-b9ca-a5cc3b273b31\", element=\"27010000-0000-0000-3581-000000000000\")>.is_displayed\nE            +      where <appium.webdriver.webelement.WebElement (session=\"84acbac6-68b6-4a3c-b9ca-a5cc3b273b31\", element=\"27010000-0000-0000-3581-000000000000\")> = <bound method WebElement.find_element of <appium.webdriver.webelement.WebElement (session=\"84acbac6-68b6-4a3c-b9ca-a5cc3b273b31\", element=\"********-0000-0000-3581-000000000000\")>>(*('-ios class chain', '**/XCUIElementTypeButton[`name == \"Add item to cart\"`]'))\nE            +        where <bound method WebElement.find_element of <appium.webdriver.webelement.WebElement (session=\"84acbac6-68b6-4a3c-b9ca-a5cc3b273b31\", element=\"********-0000-0000-3581-000000000000\")>> = <appium.webdriver.webelement.WebElement (session=\"84acbac6-68b6-4a3c-b9ca-a5cc3b273b31\", element=\"********-0000-0000-3581-000000000000\")>.find_element\nE            +        and   ('-ios class chain', '**/XCUIElementTypeButton[`name == \"Add item to cart\"`]') = <built-in method get of dict object at 0x104665ec0>('home_add_to_cart')\nE            +          where <built-in method get of dict object at 0x104665ec0> = {'best_sellers_container': ('accessibility id', 'cm_item_trending'), 'categories': ('xpath', '//XCUIElementTypeCell[@name]'), 'category_container_on_home': ('accessibility id', 'cm_categories_4416926'), 'category_image': ('xpath', '//XCUIElementTypeOther/XCUIElementTypeImage'), ...}.get\nE            +            where {'best_sellers_container': ('accessibility id', 'cm_item_trending'), 'categories': ('xpath', '//XCUIElementTypeCell[@name]'), 'category_container_on_home': ('accessibility id', 'cm_categories_4416926'), 'category_image': ('xpath', '//XCUIElementTypeOther/XCUIElementTypeImage'), ...} = <src.EC.page_objects.home.home_page.HomePage object at 0x10451fec0>.strategy\n\ntest_102302_home_cms_collection_check.py:57: AssertionError"}, "description": "\n        测试步骤：\n        1.\n        ", "parameters": [{"name": "setup", "value": "('test_102302_home_cms_collection_check',)"}], "start": 1750995689810, "stop": 1750995715520, "uuid": "1fd0992c-8fff-4148-bb8e-f731ba88d3f2", "historyId": "10fb05f86a2e8fdf1981ae4387284d2b", "testCaseId": "a4dcf4e3fb24f2ca961c09a27bde6f92", "fullName": "src.EC.tests.home.test_102302_home_cms_collection_check.TestHomeCMSCollectionCheck#test_102302_home_cms_collection_check", "labels": [{"name": "story", "value": "[102302]首页CMS collection检查"}, {"name": "tag", "value": "ios"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.home"}, {"name": "suite", "value": "test_102302_home_cms_collection_check"}, {"name": "subSuite", "value": "TestHomeCMSCollectionCheck"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "423-Main<PERSON><PERSON><PERSON>"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.home.test_102302_home_cms_collection_check"}]}