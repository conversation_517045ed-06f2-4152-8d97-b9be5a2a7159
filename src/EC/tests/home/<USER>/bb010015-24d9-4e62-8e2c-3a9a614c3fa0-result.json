{"name": "首页CMS collection-editors pick检查", "status": "passed", "description": "\n        测试步骤：\n        1.\n        ", "parameters": [{"name": "setup", "value": "('test_102302_01_home_editors_pick_check',)"}], "start": 1751003771147, "stop": 1751003790109, "uuid": "2b4c05b8-5975-40aa-8129-97181a5993b9", "historyId": "40a848cbae1ae6cc2eac5484ae5726f5", "testCaseId": "032b9238a427b668f57f24441741b9ae", "fullName": "src.EC.tests.home.test_102302_home_cms_collection_check.TestHomeCMSCollectionCheck#test_102302_01_home_editors_pick_check", "labels": [{"name": "story", "value": "[102302]首页CMS collection检查"}, {"name": "tag", "value": "ios"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.home"}, {"name": "suite", "value": "test_102302_home_cms_collection_check"}, {"name": "subSuite", "value": "TestHomeCMSCollectionCheck"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "2067-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.home.test_102302_home_cms_collection_check"}]}