{"uuid": "060f8b7e-54b5-4ebc-9f7c-659543c56423", "children": ["45209b5e-0560-486e-afef-1f5ac1164770"], "befores": [{"name": "setup", "status": "passed", "start": 1750993951242, "stop": 1750993968815}], "afters": [{"name": "setup::0", "status": "broken", "statusDetails": {"message": "binascii.Error: Incorrect padding\n", "trace": "  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/allure_commons/_allure.py\", line 221, in __call__\n    return self._fixture_function(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/_pytest/fixtures.py\", line 911, in _teardown_yield_fixture\n    next(it)\n  File \"/Users/<USER>/qa-ui-android-ios/src/EC/tests/home/<USER>\", line 28, in setup\n    base64_to_mp4(video_data, f'./video/{os.getenv(\"BUILD_NUMBER\", 0)}/{request.param[0]}.mp4')\n  File \"/Users/<USER>/qa-ui-android-ios/src/common/recording.py\", line 5, in base64_to_mp4\n    binary_data = base64.b64decode(base64_string)\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.12/3.12.3/Frameworks/Python.framework/Versions/3.12/lib/python3.12/base64.py\", line 88, in b64decode\n    return binascii.a2b_base64(s, strict_mode=validate)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1750994003902, "stop": 1750994008565}], "start": 1750993951241, "stop": 1750994008565}