{"name": "首页CMS collection检查", "status": "broken", "statusDetails": {"message": "TypeError: ActionHelpers.scroll() missing 2 required positional arguments: 'origin_el' and 'destination_el'", "trace": "self = <test_102302_home_cms_collection_check.TestHomeCMSCollectionCheck object at 0x1050213a0>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"405430d6-7bc4-4a90-8685-505db2297a73\")>\nsetup = None\n\n    @allure.title(\"首页CMS collection检查\")\n    @pytest.mark.parametrize('setup', [('test_102302_home_cms_collection_check', )], indirect=True)\n    @pytest.mark.ios\n    def test_102302_home_cms_collection_check(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1.\n        \"\"\"\n        # todo 安卓首页的cms组件没有加标识符，无法识别\n        d = get_driver\n>       d.scroll()\nE       TypeError: ActionHelpers.scroll() missing 2 required positional arguments: 'origin_el' and 'destination_el'\n\ntest_102302_home_cms_collection_check.py:40: TypeError"}, "description": "\n        测试步骤：\n        1.\n        ", "parameters": [{"name": "setup", "value": "('test_102302_home_cms_collection_check',)"}], "start": 1750995623083, "stop": 1750995623084, "uuid": "e8e19e90-022c-4933-82d1-429a85e37408", "historyId": "10fb05f86a2e8fdf1981ae4387284d2b", "testCaseId": "a4dcf4e3fb24f2ca961c09a27bde6f92", "fullName": "src.EC.tests.home.test_102302_home_cms_collection_check.TestHomeCMSCollectionCheck#test_102302_home_cms_collection_check", "labels": [{"name": "story", "value": "[102302]首页CMS collection检查"}, {"name": "tag", "value": "ios"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.home"}, {"name": "suite", "value": "test_102302_home_cms_collection_check"}, {"name": "subSuite", "value": "TestHomeCMSCollectionCheck"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "339-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.home.test_102302_home_cms_collection_check"}]}