import os
import time
import allure
import pytest

from src.api.zipcode import switch_zipcode
from src.EC.page_objects.cart.single_cart_checkout_page import SingleCartCheckoutPage
from src.EC.tests.base_case import BaseCase
from src.api.commonapi.commfunc import empty_cart
from src.common.recording import base64_to_mp4
from src.config.weee.log_help import log


@allure.story("多种类型购物车结算流程测试")
class TestSingleCartCheckoutFlow(BaseCase):

    pytestmark = [pytest.mark.single_cart, pytest.mark.regression]

    @pytest.fixture(scope='function')
    def setup(self, request, autotest_header, android_header, get_driver):
        empty_cart(android_header)
        empty_cart(autotest_header)
        get_driver.start_recording_screen()
        yield
        empty_cart(android_header)
        empty_cart(autotest_header)
        video_data = get_driver.stop_recording_screen()
        if not os.path.exists(f'./video/{os.getenv("BUILD_NUMBER", 0)}'):
            os.makedirs(f'./video/{os.getenv("BUILD_NUMBER", 0)}')
        base64_to_mp4(video_data, f'./video/{os.getenv("BUILD_NUMBER", 0)}/{request.param[0]}.mp4')

    @allure.title("单购物车pantry购物车测试")
    @pytest.mark.parametrize('setup', [('test_107945_single_pantry_cart_checkout_page', )], indirect=True)
    def test_107945_single_pantry_cart_checkout_page(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. 用户购物车有1种类型购物车，点击结算
        2. 验证购物车中各元素
        """
        d = get_driver
        platform = get_platform
        scc = SingleCartCheckoutPage(d, platform)

        # 关掉首页广告
        adv = self.find_element(_driver=d, ele=scc.strategy.get("popup_close"))
        if adv:
            adv.click()

        # 1. 加购pantry商品
        scc.add_filter_products_to_cart()

        # 2. 进入购物车页面
        assert scc.navigate_to_cart(), f"Failed to navigate to cart page"

        # 3. 验证购物车中各元素
        assert scc.check_pantry_cart_exists(), f"Pantry cart should exist in the cart"

        # 4. 点击结算按钮
        assert scc.click_checkout_button(), f"Failed to click checkout button"

        # 5. 验证结算页面
        assert scc.check_pantry_checkout_page(), f"Failed to check pantry checkout page"

        scc.go_back()

    @allure.title("单购物车grocery购物车测试")
    @pytest.mark.parametrize('setup', [('test_107945_single_grocery_cart_checkout_page',)], indirect=True)
    def test_107945_single_grocery_cart_checkout_page(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. 用户购物车有1种类型购物车，点击结算
        2. 验证购物车中各元素
        """
        d = get_driver
        platform = get_platform
        scc = SingleCartCheckoutPage(d, platform)

        # 关掉首页广告
        adv = self.find_element(_driver=d, ele=scc.strategy.get("popup_close"))
        if adv:
            adv.click()

        # 1. 加购grocery商品
        scc.add_filter_products_to_cart(filter_type='local_delivery_filter')

        # 2. 进入购物车页面
        assert scc.navigate_to_cart(), f"Failed to navigate to cart page"

        # 3. 验证购物车中各元素
        assert scc.check_grocery_cart_exists(), f"grocery cart should exist in the cart"

        # 4. 点击结算按钮
        assert scc.click_checkout_button(), f"Failed to click checkout button"

        # 5. 验证结算页面
        assert scc.check_grocery_checkout_page(), f"Failed to check grocery checkout page"

        scc.go_back()

    @allure.title("单购物车mof购物车测试")
    @pytest.mark.parametrize('setup', [('test_107945_single_mof_cart_checkout_page',)], indirect=True)
    @pytest.mark.skip(reason='cold pack id cannot be used')
    def test_107945_single_mof_cart_checkout_page(self, android_header, get_platform, get_driver, setup):
        """
        测试步骤：
        1. 用户购物车有1种类型mof购物车，点击结算
        2. 验证购物车中各元素
        """
        switch_zipcode(android_header, "01054")

        d = get_driver
        platform = get_platform

        scc = SingleCartCheckoutPage(d, platform)

        home_button = self.find_element(_driver=d, ele=scc.strategy.get("home"))
        if home_button:
            home_button.click()
            time.sleep(5)

        # 关掉首页广告
        adv = self.find_element(_driver=d, ele=scc.strategy.get("popup_close"))
        if adv:
            adv.click()

        # 切换至01054
        scc.switch_zipcode()

        # 1. 加购mof商品
        scc.add_filter_products_to_cart(filter_type='cold_pack_filter')

        # 2. 进入购物车页面
        assert scc.navigate_to_cart(), f"Failed to navigate to cart page"

        # 3. 验证购物车中各元素
        assert scc.check_grocery_cart_exists(), f"grocery cart should exist in the cart"

        # 4. 点击结算按钮
        assert scc.click_checkout_button(), f"Failed to click checkout button"

        # 5. 验证结算页面
        assert scc.check_grocery_checkout_page(), f"Failed to check grocery checkout page"

        scc.go_back()

