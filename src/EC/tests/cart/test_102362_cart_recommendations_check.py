import os
import time
import allure
import pytest

from src.api.zipcode import switch_zipcode
from src.EC.page_objects.cart.single_cart_checkout_page import SingleCartCheckoutPage
from src.EC.tests.base_case import BaseCase
from src.api.commonapi.commfunc import empty_cart
from src.common.recording import base64_to_mp4
from src.config.weee.log_help import log


@allure.story("购物车-为你推荐UI/UX验证")
class TestCartRecommendations(BaseCase):

    pytestmark = [pytest.mark.regression]

    @pytest.fixture(scope='function')
    def setup(self, request, autotest_header, android_header, get_driver):
        empty_cart(android_header)
        empty_cart(autotest_header)
        get_driver.start_recording_screen()
        yield
        empty_cart(android_header)
        empty_cart(autotest_header)
        video_data = get_driver.stop_recording_screen()
        if not os.path.exists(f'./video/{os.getenv("BUILD_NUMBER", 0)}'):
            os.makedirs(f'./video/{os.getenv("BUILD_NUMBER", 0)}')
        base64_to_mp4(video_data, f'./video/{os.getenv("BUILD_NUMBER", 0)}/{request.param[0]}.mp4')

    @allure.title("购物车-为你推荐UI/UX验证")
    @pytest.mark.parametrize('setup', [('test_107945_single_pantry_cart_checkout_page', )], indirect=True)
    def test_102362_cart_recommendations_check(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. 用户购物车有1种类型购物车，滑动到下方，校验为你推荐
        https://metersphere.sayweee.net/#/track/case/edit/fae8248c-5cde-e3ae-de0b-ca9e3157c5ee?projectId=4f014f7d-0089-41c3-ab11-6993d2c89860
        """
        d = get_driver
        platform = get_platform
        scc = SingleCartCheckoutPage(d, platform)

        # 关掉首页广告
        adv = self.find_element(_driver=d, ele=scc.strategy.get("popup_close"))
        if adv:
            adv.click()

        # 1. 点击cart按钮，进入cart, 校验为你推荐
        scc.cart_recommendations()

        # 2. 返回首页
        scc.go_back_home()

