import os
import time
import allure
import pytest

from src.api.zipcode import switch_zipcode
from src.EC.page_objects.cart.single_cart_checkout_page import SingleCartCheckoutPage
from src.EC.tests.base_case import BaseCase
from src.api.commonapi.commfunc import empty_cart
from src.common.recording import base64_to_mp4
from src.config.weee.log_help import log


@allure.story("多种类型购物车结算流程测试")
class TestSingleCartCheckoutFlow(BaseCase):

    pytestmark = [pytest.mark.single_cart, pytest.mark.regression]

    @pytest.fixture(scope='function')
    def setup(self, request, autotest_header, android_header, get_driver):
        empty_cart(android_header)
        empty_cart(autotest_header)
        get_driver.start_recording_screen()
        yield
        empty_cart(android_header)
        empty_cart(autotest_header)
        video_data = get_driver.stop_recording_screen()
        if not os.path.exists(f'./video/{os.getenv("BUILD_NUMBER", 0)}'):
            os.makedirs(f'./video/{os.getenv("BUILD_NUMBER", 0)}')
        base64_to_mp4(video_data, f'./video/{os.getenv("BUILD_NUMBER", 0)}/{request.param[0]}.mp4')

    @allure.title("结算页面，切换日期验证")
    @pytest.mark.parametrize('setup', [('test_108559_switch_delivery_date_on_checkout', )], indirect=True)
    def test_108559_switch_delivery_date_on_checkout(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. 用户购物车有1种类型购物车，点击结算
        2. 验证购物车中各元素
        """
        d = get_driver
        platform = get_platform
        scc = SingleCartCheckoutPage(d, platform)

        # 关掉首页广告
        adv = self.find_element(_driver=d, ele=scc.strategy.get("popup_close"))
        if adv:
            adv.click()

        # 1. 加购local delivery 商品
        scc.add_filter_products_to_cart(filter_type='local_delivery_filter')

        # 2. 进入购物车页面
        assert scc.navigate_to_cart(), f"Failed to navigate to cart page"

        # 3. 点击结算按钮
        assert scc.click_checkout_button(), f"Failed to click checkout button"

        # 4. 验证结算页面
        assert scc.check_grocery_checkout_page(), f"Failed to check pantry checkout page"

        # 5. 切换日期
        scc.switch_delivery_date(), f"Failed to switch delivery date"

        assert scc.check_grocery_checkout_page(), f"Failed to check pantry checkout page"

        # 回到首页，验证首页商品状态
        scc.go_back()

        # 6. 验证日期切换成功, 验证home页面商品正常显示
        assert scc.check_delivery_date_switched()

        scc.switch_original_delivery_date()





