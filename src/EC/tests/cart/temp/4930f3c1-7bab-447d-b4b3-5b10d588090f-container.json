{"uuid": "136890d0-34c9-4422-9723-7d70256de9d4", "children": ["aa012bda-5fde-4ccf-a9cc-5f9ce0ae5dba"], "befores": [{"name": "setup", "status": "passed", "start": 1750833387221, "stop": 1750833394632}], "afters": [{"name": "setup::0", "status": "broken", "statusDetails": {"message": "binascii.Error: Incorrect padding\n", "trace": "  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/allure_commons/_allure.py\", line 221, in __call__\n    return self._fixture_function(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/_pytest/fixtures.py\", line 911, in _teardown_yield_fixture\n    next(it)\n  File \"/Users/<USER>/qa-ui-android-ios/src/EC/tests/cart/test_108559_switch_delivery_date_on_checkout.py\", line 30, in setup\n    base64_to_mp4(video_data, f'./video/{os.getenv(\"BUILD_NUMBER\", 0)}/{request.param[0]}.mp4')\n  File \"/Users/<USER>/qa-ui-android-ios/src/common/recording.py\", line 5, in base64_to_mp4\n    binary_data = base64.b64decode(base64_string)\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.12/3.12.3/Frameworks/Python.framework/Versions/3.12/lib/python3.12/base64.py\", line 88, in b64decode\n    return binascii.a2b_base64(s, strict_mode=validate)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1750833451199, "stop": 1750833454951}], "start": 1750833387221, "stop": 1750833454951}