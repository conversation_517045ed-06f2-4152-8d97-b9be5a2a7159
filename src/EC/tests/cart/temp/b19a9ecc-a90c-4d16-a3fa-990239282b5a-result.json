{"name": "单购物车pantry购物车测试", "status": "passed", "description": "\n        测试步骤：\n        1. 用户购物车有1种类型购物车，点击结算\n        2. 验证购物车中各元素\n        ", "parameters": [{"name": "setup", "value": "('test_107945_single_pantry_cart_checkout_page',)"}], "start": 1750668480750, "stop": 1750668542358, "uuid": "9c9104ec-4799-4a30-85a3-88a38d6ec65e", "historyId": "54f29459cd06def0c06a00742bda1a6d", "testCaseId": "183b2684cb97bb79c3cbd1cd2551d5bb", "fullName": "src.EC.tests.cart.test_107945_single_cart_checkout_flow.TestSingleCartCheckoutFlow#test_107945_single_pantry_cart_checkout_page", "labels": [{"name": "story", "value": "多种类型购物车结算流程测试"}, {"name": "tag", "value": "single_cart"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.cart"}, {"name": "suite", "value": "test_107945_single_cart_checkout_flow"}, {"name": "subSuite", "value": "TestSingleCartCheckoutFlow"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "27676-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.cart.test_107945_single_cart_checkout_flow"}]}