{"name": "单购物车pantry购物车测试", "status": "skipped", "description": "\n        测试步骤：\n        1. 用户购物车有1种类型购物车，点击结算\n        2. 验证购物车中各元素\n        ", "parameters": [{"name": "setup", "value": "('test_107945_single_pantry_cart_checkout_page',)"}], "start": 1750668103485, "stop": 1750668103485, "uuid": "38be8f6e-41a6-4f67-b25f-4b5190d25308", "testCaseId": "183b2684cb97bb79c3cbd1cd2551d5bb", "fullName": "src.EC.tests.cart.test_107945_single_cart_checkout_flow.TestSingleCartCheckoutFlow#test_107945_single_pantry_cart_checkout_page"}