{"name": "结算页面，切换日期验证", "status": "passed", "description": "\n        测试步骤：\n        1. 用户购物车有1种类型购物车，点击结算\n        2. 验证购物车中各元素\n        ", "parameters": [{"name": "setup", "value": "('test_108559_switch_delivery_date_on_checkout',)"}], "start": 1750667887024, "stop": 1750667979753, "uuid": "3d7fbb2a-ebcb-42fc-9c46-ffc3cd0b86f8", "historyId": "bd0e9a75d6cb16cc6079b5837be3886b", "testCaseId": "b32038cf2d3f0447f6ae8820464626cd", "fullName": "src.EC.tests.cart.test_108559_switch_delivery_date_on_checkout.TestSingleCartCheckoutFlow#test_108559_switch_delivery_date_on_checkout", "labels": [{"name": "story", "value": "多种类型购物车结算流程测试"}, {"name": "tag", "value": "single_cart"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.cart"}, {"name": "suite", "value": "test_108559_switch_delivery_date_on_checkout"}, {"name": "subSuite", "value": "TestSingleCartCheckoutFlow"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "27434-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.cart.test_108559_switch_delivery_date_on_checkout"}]}