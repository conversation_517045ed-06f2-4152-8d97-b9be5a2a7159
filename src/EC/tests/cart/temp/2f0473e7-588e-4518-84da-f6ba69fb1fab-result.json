{"name": "购物车-为你推荐UI/UX验证", "status": "failed", "statusDetails": {"message": "AssertionError", "trace": "self = <src.EC.tests.cart.test_102362_cart_recommendations_check.TestCartRecommendations object at 0x11014c980>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"939be178-975c-4439-9a5a-8df06f5120fe\")>\nsetup = None\n\n    @allure.title(\"购物车-为你推荐UI/UX验证\")\n    @pytest.mark.parametrize('setup', [('test_107945_single_pantry_cart_checkout_page', )], indirect=True)\n    def test_102362_cart_recommendations_check(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1. 用户购物车有1种类型购物车，滑动到下方，校验为你推荐\n        https://metersphere.sayweee.net/#/track/case/edit/fae8248c-5cde-e3ae-de0b-ca9e3157c5ee?projectId=4f014f7d-0089-41c3-ab11-6993d2c89860\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n        scc = SingleCartCheckoutPage(d, platform)\n    \n        # 关掉首页广告\n        adv = self.find_element(_driver=d, ele=scc.strategy.get(\"popup_close\"))\n        if adv:\n            adv.click()\n    \n        # 1. 点击cart按钮，进入cart, 校验为你推荐\n>       scc.cart_recommendations()\n\ntest_102362_cart_recommendations_check.py:50: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../page_objects/cart/single_cart_checkout_page.py:290: in cart_recommendations\n    self.check_recommendations_product(recommendations_products)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <src.EC.page_objects.cart.single_cart_checkout_page.SingleCartCheckoutPage object at 0x1101fafc0>\n_recommendations_products = [<appium.webdriver.webelement.WebElement (session=\"939be178-975c-4439-9a5a-8df06f5120fe\", element=\"********-0000-0000-...ebelement.WebElement (session=\"939be178-975c-4439-9a5a-8df06f5120fe\", element=\"********-0000-0000-F378-000000000000\")>]\n\n    def check_recommendations_product(self, _recommendations_products):\n        if _recommendations_products:\n            for index, item in enumerate(_recommendations_products):\n                if index == 1:\n                    break\n                a = item.find_element(*self.strategy.get('recommendations_product_image'))\n                b =  item.find_element(*self.strategy.get('recommendations_product_title'))\n                c = item.find_element(*self.strategy.get('recommendations_product_price'))\n                d = item.find_element(*self.strategy.get('recommendations_product_favorite'))\n    \n                assert item.find_element(*self.strategy.get('recommendations_product_image')).is_displayed()\n>               assert item.find_element(*self.strategy.get('recommendations_product_title')).is_displayed()\nE               AssertionError\n\n../../page_objects/cart/single_cart_checkout_page.py:309: AssertionError"}, "description": "\n        测试步骤：\n        1. 用户购物车有1种类型购物车，滑动到下方，校验为你推荐\n        https://metersphere.sayweee.net/#/track/case/edit/fae8248c-5cde-e3ae-de0b-ca9e3157c5ee?projectId=4f014f7d-0089-41c3-ab11-6993d2c89860\n        ", "parameters": [{"name": "setup", "value": "('test_107945_single_pantry_cart_checkout_page',)"}], "start": 1750907341988, "stop": 1750907362044, "uuid": "ddbdd5f7-53d0-4fe0-a866-1b7e3fca5241", "historyId": "348563d6a5e6c7e66eba9663e0123322", "testCaseId": "d57ff2c25bf9f4510cf709769854a66f", "fullName": "src.EC.tests.cart.test_102362_cart_recommendations_check.TestCartRecommendations#test_102362_cart_recommendations_check", "labels": [{"name": "story", "value": "购物车-为你推荐UI/UX验证"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.cart"}, {"name": "suite", "value": "test_102362_cart_recommendations_check"}, {"name": "subSuite", "value": "TestCartRecommendations"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "81598-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.cart.test_102362_cart_recommendations_check"}]}