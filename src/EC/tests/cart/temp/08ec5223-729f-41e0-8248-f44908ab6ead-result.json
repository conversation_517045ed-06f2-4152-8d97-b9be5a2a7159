{"name": "结算页面，切换日期验证", "status": "passed", "description": "\n        测试步骤：\n        1. 用户购物车有1种类型购物车，点击结算\n        2. 验证购物车中各元素\n        ", "parameters": [{"name": "setup", "value": "('test_108559_switch_delivery_date_on_checkout',)"}], "start": 1750665609449, "stop": 1750665894290, "uuid": "a1039516-aa42-4106-b410-986077b60bcf", "testCaseId": "b32038cf2d3f0447f6ae8820464626cd", "fullName": "src.EC.tests.cart.test_108559_switch_delivery_date_on_checkout.TestSingleCartCheckoutFlow#test_108559_switch_delivery_date_on_checkout"}