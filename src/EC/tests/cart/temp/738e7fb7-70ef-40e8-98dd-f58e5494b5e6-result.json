{"name": "购物车-为你推荐UI/UX验证", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'click'", "trace": "self = <src.EC.tests.cart.test_102362_cart_recommendations_check.TestCartRecommendations object at 0x106e06060>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"44027167-1f55-471c-82cb-0d52deb34780\")>\nsetup = None\n\n    @allure.title(\"购物车-为你推荐UI/UX验证\")\n    @pytest.mark.parametrize('setup', [('test_107945_single_pantry_cart_checkout_page', )], indirect=True)\n    def test_102362_cart_recommendations_check(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1. 用户购物车有1种类型购物车，滑动到下方，校验为你推荐\n        https://metersphere.sayweee.net/#/track/case/edit/fae8248c-5cde-e3ae-de0b-ca9e3157c5ee?projectId=4f014f7d-0089-41c3-ab11-6993d2c89860\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n        scc = SingleCartCheckoutPage(d, platform)\n    \n        # 关掉首页广告\n        adv = self.find_element(_driver=d, ele=scc.strategy.get(\"popup_close\"))\n        if adv:\n            adv.click()\n    \n        # 1. 点击cart按钮，进入cart, 校验为你推荐\n        scc.cart_recommendations()\n    \n        # 2. 返回首页\n>       scc.go_back_home()\n\ntest_102362_cart_recommendations_check.py:53: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <src.EC.page_objects.cart.single_cart_checkout_page.SingleCartCheckoutPage object at 0x107ef0b00>\n\n    def go_back_home(self):\n>       self.find_element(self.driver, self.strategy.get('back_button')).click()\nE       AttributeError: 'NoneType' object has no attribute 'click'\n\n../../page_objects/cart/single_cart_checkout_page.py:325: AttributeError"}, "description": "\n        测试步骤：\n        1. 用户购物车有1种类型购物车，滑动到下方，校验为你推荐\n        https://metersphere.sayweee.net/#/track/case/edit/fae8248c-5cde-e3ae-de0b-ca9e3157c5ee?projectId=4f014f7d-0089-41c3-ab11-6993d2c89860\n        ", "parameters": [{"name": "setup", "value": "('test_107945_single_pantry_cart_checkout_page',)"}], "start": 1750831565528, "stop": 1750831604116, "uuid": "f50c49a0-7fc3-4c1d-a4d0-d069d92665f2", "historyId": "348563d6a5e6c7e66eba9663e0123322", "testCaseId": "d57ff2c25bf9f4510cf709769854a66f", "fullName": "src.EC.tests.cart.test_102362_cart_recommendations_check.TestCartRecommendations#test_102362_cart_recommendations_check", "labels": [{"name": "story", "value": "购物车-为你推荐UI/UX验证"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.cart"}, {"name": "suite", "value": "test_102362_cart_recommendations_check"}, {"name": "subSuite", "value": "TestCartRecommendations"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "64014-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.cart.test_102362_cart_recommendations_check"}]}