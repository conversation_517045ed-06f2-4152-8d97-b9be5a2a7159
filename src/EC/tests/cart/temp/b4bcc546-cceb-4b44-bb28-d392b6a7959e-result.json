{"name": "单购物车grocery购物车测试", "status": "passed", "description": "\n        测试步骤：\n        1. 用户购物车有1种类型购物车，点击结算\n        2. 验证购物车中各元素\n        ", "parameters": [{"name": "setup", "value": "('test_107945_single_grocery_cart_checkout_page',)"}], "start": 1750668209020, "stop": 1750668274374, "uuid": "f0783c30-a202-474d-8f91-ff5fc931ac34", "historyId": "ee24747e7fb1a759fde3198419f947ce", "testCaseId": "256d1c2d5546a538bceb4189a4e44172", "fullName": "src.EC.tests.cart.test_107945_single_cart_checkout_flow.TestSingleCartCheckoutFlow#test_107945_single_grocery_cart_checkout_page", "labels": [{"name": "story", "value": "多种类型购物车结算流程测试"}, {"name": "tag", "value": "single_cart"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.cart"}, {"name": "suite", "value": "test_107945_single_cart_checkout_flow"}, {"name": "subSuite", "value": "TestSingleCartCheckoutFlow"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "27515-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.cart.test_107945_single_cart_checkout_flow"}]}