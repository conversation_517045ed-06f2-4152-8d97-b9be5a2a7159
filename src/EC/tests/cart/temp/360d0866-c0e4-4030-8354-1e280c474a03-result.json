{"name": "购物车-为你推荐UI/UX验证", "status": "passed", "description": "\n        测试步骤：\n        1. 用户购物车有1种类型购物车，滑动到下方，校验为你推荐\n        https://metersphere.sayweee.net/#/track/case/edit/fae8248c-5cde-e3ae-de0b-ca9e3157c5ee?projectId=4f014f7d-0089-41c3-ab11-6993d2c89860\n        ", "parameters": [{"name": "setup", "value": "('test_107945_single_pantry_cart_checkout_page',)"}], "start": 1750907439234, "stop": 1750907471311, "uuid": "e5919a56-7161-450c-966f-b1a8759fd836", "historyId": "348563d6a5e6c7e66eba9663e0123322", "testCaseId": "d57ff2c25bf9f4510cf709769854a66f", "fullName": "src.EC.tests.cart.test_102362_cart_recommendations_check.TestCartRecommendations#test_102362_cart_recommendations_check", "labels": [{"name": "story", "value": "购物车-为你推荐UI/UX验证"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.cart"}, {"name": "suite", "value": "test_102362_cart_recommendations_check"}, {"name": "subSuite", "value": "TestCartRecommendations"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "82318-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.cart.test_102362_cart_recommendations_check"}]}