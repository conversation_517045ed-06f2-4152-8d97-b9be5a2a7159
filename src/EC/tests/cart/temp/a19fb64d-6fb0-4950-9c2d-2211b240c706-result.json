{"name": "结算页面，切换日期验证", "status": "failed", "statusDetails": {"message": "AssertionError: Failed to click checkout button\nassert False\n +  where False = <bound method SingleCartCheckoutPage.click_checkout_button of <src.EC.page_objects.cart.single_cart_checkout_page.SingleCartCheckoutPage object at 0x1056257c0>>()\n +    where <bound method SingleCartCheckoutPage.click_checkout_button of <src.EC.page_objects.cart.single_cart_checkout_page.SingleCartCheckoutPage object at 0x1056257c0>> = <src.EC.page_objects.cart.single_cart_checkout_page.SingleCartCheckoutPage object at 0x1056257c0>.click_checkout_button", "trace": "self = <src.EC.tests.cart.test_108559_switch_delivery_date_on_checkout.TestSingleCartCheckoutFlow object at 0x1056271a0>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"7d13ee26-de22-4543-a247-431703e03047\")>\nsetup = None\n\n    @allure.title(\"结算页面，切换日期验证\")\n    @pytest.mark.parametrize('setup', [('test_108559_switch_delivery_date_on_checkout', )], indirect=True)\n    def test_108559_switch_delivery_date_on_checkout(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1. 用户购物车有1种类型购物车，点击结算\n        2. 验证购物车中各元素\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n        scc = SingleCartCheckoutPage(d, platform)\n    \n        # 关掉首页广告\n        adv = self.find_element(_driver=d, ele=scc.strategy.get(\"popup_close\"))\n        if adv:\n            adv.click()\n    \n        # 1. 加购local delivery 商品\n        scc.add_filter_products_to_cart(filter_type='local_delivery_filter')\n    \n        # 2. 进入购物车页面\n        assert scc.navigate_to_cart(), f\"Failed to navigate to cart page\"\n    \n        # 3. 点击结算按钮\n>       assert scc.click_checkout_button(), f\"Failed to click checkout button\"\nE       AssertionError: Failed to click checkout button\nE       assert False\nE        +  where False = <bound method SingleCartCheckoutPage.click_checkout_button of <src.EC.page_objects.cart.single_cart_checkout_page.SingleCartCheckoutPage object at 0x1056257c0>>()\nE        +    where <bound method SingleCartCheckoutPage.click_checkout_button of <src.EC.page_objects.cart.single_cart_checkout_page.SingleCartCheckoutPage object at 0x1056257c0>> = <src.EC.page_objects.cart.single_cart_checkout_page.SingleCartCheckoutPage object at 0x1056257c0>.click_checkout_button\n\ntest_108559_switch_delivery_date_on_checkout.py:56: AssertionError"}, "description": "\n        测试步骤：\n        1. 用户购物车有1种类型购物车，点击结算\n        2. 验证购物车中各元素\n        ", "parameters": [{"name": "setup", "value": "('test_108559_switch_delivery_date_on_checkout',)"}], "start": 1750833394633, "stop": 1750833451150, "uuid": "aa012bda-5fde-4ccf-a9cc-5f9ce0ae5dba", "historyId": "bd0e9a75d6cb16cc6079b5837be3886b", "testCaseId": "b32038cf2d3f0447f6ae8820464626cd", "fullName": "src.EC.tests.cart.test_108559_switch_delivery_date_on_checkout.TestSingleCartCheckoutFlow#test_108559_switch_delivery_date_on_checkout", "labels": [{"name": "story", "value": "多种类型购物车结算流程测试"}, {"name": "tag", "value": "single_cart"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.cart"}, {"name": "suite", "value": "test_108559_switch_delivery_date_on_checkout"}, {"name": "subSuite", "value": "TestSingleCartCheckoutFlow"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "64511-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.cart.test_108559_switch_delivery_date_on_checkout"}]}