{"name": "单购物车pantry购物车测试", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'is_displayed'", "trace": "self = <src.EC.tests.cart.test_107945_single_cart_checkout_flow.TestSingleCartCheckoutFlow object at 0x103338920>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"aa792fc7-6df9-4683-9a0e-63ccfc7e0444\")>\nsetup = None\n\n    @allure.title(\"单购物车pantry购物车测试\")\n    @pytest.mark.parametrize('setup', [('test_107945_single_pantry_cart_checkout_page', )], indirect=True)\n    def test_107945_single_pantry_cart_checkout_page(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1. 用户购物车有1种类型购物车，点击结算\n        2. 验证购物车中各元素\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n        scc = SingleCartCheckoutPage(d, platform)\n    \n        # 关掉首页广告\n        adv = self.find_element(_driver=d, ele=scc.strategy.get(\"popup_close\"))\n        if adv:\n            adv.click()\n    \n        # 1. 加购pantry商品\n        scc.add_filter_products_to_cart()\n    \n        # 2. 进入购物车页面\n        assert scc.navigate_to_cart(), f\"Failed to navigate to cart page\"\n    \n        # 3. 验证购物车中各元素\n        assert scc.check_pantry_cart_exists(), f\"Pantry cart should exist in the cart\"\n    \n        # 4. 点击结算按钮\n        assert scc.click_checkout_button(), f\"Failed to click checkout button\"\n    \n        # 5. 验证结算页面\n>       assert scc.check_pantry_checkout_page(), f\"Failed to check pantry checkout page\"\n\ntest_107945_single_cart_checkout_flow.py:62: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <src.EC.page_objects.cart.single_cart_checkout_page.SingleCartCheckoutPage object at 0x1036a2240>\n\n    def check_pantry_checkout_page(self):\n        a = self.find_element(self.driver, self.strategy.get('checkout_pantry_title')).is_displayed()\n        b = self.find_element(self.driver, self.strategy.get('checkout_delivery_date')).is_displayed()\n>       c = self.find_element(self.driver, self.strategy.get('checkout_pantry_ship_info')).is_displayed()\nE       AttributeError: 'NoneType' object has no attribute 'is_displayed'\n\n../../page_objects/cart/single_cart_checkout_page.py:219: AttributeError"}, "description": "\n        测试步骤：\n        1. 用户购物车有1种类型购物车，点击结算\n        2. 验证购物车中各元素\n        ", "parameters": [{"name": "setup", "value": "('test_107945_single_pantry_cart_checkout_page',)"}], "start": 1750668127828, "stop": 1750668195969, "uuid": "c32b4ead-eba4-4b53-8433-d06c1d09c6c6", "historyId": "54f29459cd06def0c06a00742bda1a6d", "testCaseId": "183b2684cb97bb79c3cbd1cd2551d5bb", "fullName": "src.EC.tests.cart.test_107945_single_cart_checkout_flow.TestSingleCartCheckoutFlow#test_107945_single_pantry_cart_checkout_page", "labels": [{"name": "story", "value": "多种类型购物车结算流程测试"}, {"name": "tag", "value": "single_cart"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.cart"}, {"name": "suite", "value": "test_107945_single_cart_checkout_flow"}, {"name": "subSuite", "value": "TestSingleCartCheckoutFlow"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "27515-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.cart.test_107945_single_cart_checkout_flow"}]}