{"name": "单购物车mof购物车测试", "status": "skipped", "statusDetails": {"message": "Skipped: cold pack id cannot be used", "trace": "('/Users/<USER>/qa-ui-android-ios/src/EC/tests/cart/test_107945_single_cart_checkout_flow.py', 100, 'Skipped: cold pack id cannot be used')"}, "description": "\n        测试步骤：\n        1. 用户购物车有1种类型mof购物车，点击结算\n        2. 验证购物车中各元素\n        ", "parameters": [{"name": "setup", "value": "('test_107945_single_mof_cart_checkout_page',)"}], "start": 1750668624416, "stop": 1750668624416, "uuid": "d0d70a4c-9239-462e-8d0e-04a1dbe3e1d2", "historyId": "8c888767f853f7bf0944e56bf64c9614", "testCaseId": "1c59a270ddfa9381a0d8f9a7620bbce0", "fullName": "src.EC.tests.cart.test_107945_single_cart_checkout_flow.TestSingleCartCheckoutFlow#test_107945_single_mof_cart_checkout_page", "labels": [{"name": "story", "value": "多种类型购物车结算流程测试"}, {"name": "tag", "value": "@pytest.mark.skip(reason='cold pack id cannot be used')"}, {"name": "tag", "value": "single_cart"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.cart"}, {"name": "suite", "value": "test_107945_single_cart_checkout_flow"}, {"name": "subSuite", "value": "TestSingleCartCheckoutFlow"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "27676-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.cart.test_107945_single_cart_checkout_flow"}]}