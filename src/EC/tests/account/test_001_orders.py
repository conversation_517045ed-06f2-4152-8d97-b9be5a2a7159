import os
import time
import allure
import pytest

from src.EC.tests.base_case import BaseCase
from src.common.recording import base64_to_mp4
from src.EC.page_objects.account.account_page import AccountPage


@allure.story("查看订单页面各状态标签")
class TestOrders(BaseCase):

    pytestmark = [pytest.mark.orders]

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
        get_driver.start_recording_screen()
        yield
        video_data = get_driver.stop_recording_screen()
        if not os.path.exists(f'./video/{os.getenv("BUILD_NUMBER", 0)}'):
            os.makedirs(f'./video/{os.getenv("BUILD_NUMBER", 0)}')
        base64_to_mp4(video_data, f'./video/{os.getenv("BUILD_NUMBER", 0)}/{request.param[0]}.mp4')

    @allure.title("查看订单页面各状态标签")
    @pytest.mark.parametrize('setup', [('test_001_check_order_tabs', )], indirect=True)
    def test_001_check_order_tabs(self, get_driver, get_platform, setup):
        """
        测试步骤：
        1. 进入account页面
        2. 点击my orders,进入orders页面
        3. 依次点击"pending, unshipped, shipped, to review, cancelled"查看订单
        """
        d = get_driver
        account_page = AccountPage(d, get_platform)

        # 1. 进入account页面
        account_page.navigate_to_account_tab()

        # 2. 点击my orders,进入orders页面
        account_page.navigate_to_my_orders()


        # 3. 依次点击"pending, unshipped, shipped, to review, cancelled"查看订单

        # 点击并验证Pending标签页
        account_page.click(d, account_page.strategy.get('pending_tab'))
        time.sleep(2)  # 等待内容加载
        assert account_page.is_tab_selected('pending'), "Pending tab is not selected"
        assert account_page.is_content_displayed('pending'), "Pending content is not displayed"

        # 点击并验证Unshipped标签页
        account_page.click(d, account_page.strategy.get('unshipped_tab'))
        time.sleep(2)  # 等待内容加载
        assert account_page.is_tab_selected('unshipped'), "Unshipped tab is not selected"
        assert account_page.is_content_displayed('unshipped'), "Unshipped content is not displayed"

        # 点击并验证Shipped标签页
        account_page.click(d, account_page.strategy.get('shipped_tab'))
        time.sleep(2)  # 等待内容加载
        assert account_page.is_tab_selected('shipped'), "Shipped tab is not selected"
        assert account_page.is_content_displayed('shipped'), "Shipped content is not displayed"

        # 点击并验证To Review标签页
        account_page.click(d, account_page.strategy.get('to_review_tab'))
        time.sleep(2)  # 等待内容加载
        assert account_page.is_tab_selected('to_review'), "To Review tab is not selected"
        assert account_page.is_content_displayed('to_review'), "To Review content is not displayed"

        # 点击并验证Cancelled标签页
        account_page.click(d, account_page.strategy.get('cancelled_tab'))
        time.sleep(2)  # 等待内容加载
        assert account_page.is_tab_selected('cancelled'), "Cancelled tab is not selected"
        # assert account_page.is_content_displayed('cancelled'), "Cancelled content is not displayed"

        # 返回到Account页面
        account_page.go_back()


