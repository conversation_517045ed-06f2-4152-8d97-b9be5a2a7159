import os
import time
import allure
import pytest

from src.EC.page_objects.account.account_page import AccountPage
from src.EC.page_objects.account.gift_card_page import GiftCardPage
from src.EC.tests.base_case import BaseCase
from src.common.recording import base64_to_mp4
from src.config.weee.log_help import log


@allure.story("Gift Card购买流程测试")
class TestGiftCardPurchaseFlow(BaseCase):

    pytestmark = [pytest.mark.gift_card, pytest.mark.regression]

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
        get_driver.start_recording_screen()
        yield
        video_data = get_driver.stop_recording_screen()
        if not os.path.exists(f'./video/{os.getenv("BUILD_NUMBER", 0)}'):
            os.makedirs(f'./video/{os.getenv("BUILD_NUMBER", 0)}')
        base64_to_mp4(video_data, f'./video/{os.getenv("BUILD_NUMBER", 0)}/{request.param[0]}.mp4')

    def navigate_to_gift_card_page(self, driver, platform):
        """导航到Gift Card页面"""
        try:
            # 初始化账户页面对象
            account_page = AccountPage(driver, platform)
            gift_card_page = GiftCardPage(driver, platform)
            
            # 点击账户标签
            account_tab = self.find_element(_driver=driver, ele=account_page.strategies.get(platform).get("account"))
            if account_tab:
                account_tab.click()
                time.sleep(3)
                log.info("Navigated to account page")
                
                # 查找并点击Gift Card入口
                # 这里需要根据实际的Gift Card入口位置调整
                # 需要向下翻一屏
                self.swipe_screen(_driver=driver, distance=0.8)
                if platform == 'Android':
                    self.swipe_screen(_driver=driver, distance=0.4)
                gift_card_entry = self.find_element(_driver=driver, ele=gift_card_page.strategies.get(platform).get("gift_card_entry"))
                if gift_card_entry:
                    gift_card_entry.click()
                    time.sleep(3)
                    log.info("Navigated to gift card page")
                    return True
                else:
                    log.error("Gift card entry not found")
                    return False
            else:
                log.error("Account tab not found")
                return False
        except Exception as e:
            log.error(f"Error navigating to gift card page: {str(e)}")
            return False

    @allure.title("Gift Card完整购买流程测试")
    @pytest.mark.parametrize('setup', [('test_gift_card_purchase_flow_complete', )], indirect=True)
    def test_112237_gift_card_purchase_flow_complete(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. 进入Gift card主页面，处理欢迎弹窗
        2. 填入收件人邮箱，验证输入成功
        3. 点击checkout，进入礼品卡checkout页面
        4. 点击支付方式下拉栏，选择PayPal
        5. 点击place order，跳转到PayPal第三方支付页面
        """
        d = get_driver
        platform = get_platform

        # 步骤1: 进入Gift card主页面
        navigation_success = self.navigate_to_gift_card_page(d, platform)
        assert navigation_success, "Failed to navigate to gift card page"

        # 初始化Gift Card页面对象
        gift_card_page = GiftCardPage(d, platform)

        # 处理欢迎弹窗
        welcome_popup_exists = gift_card_page.check_welcome_popup_exists()
        if welcome_popup_exists:
            popup_closed = gift_card_page.close_welcome_popup()
            assert popup_closed, "Failed to close welcome popup"
            log.info("✓ Welcome popup closed successfully")
        else:
            log.info("ℹ No welcome popup found")

        # 校验页面元素
        assert gift_card_page.check_page_elements, "Welcome popup still exists"

        # 步骤2: 填入收件人邮箱
        test_email = "<EMAIL>"
        email_entered = gift_card_page.enter_recipient_email(test_email)
        assert email_entered, f"Failed to enter recipient email: {test_email}"
        

        # 步骤3: 点击checkout
        checkout_clicked = gift_card_page.click_checkout_button()
        assert checkout_clicked, "Failed to click checkout button"
        
        # 验证进入checkout页面
        checkout_page_loaded = gift_card_page.check_checkout_page_loaded()
        assert checkout_page_loaded, "Failed to load gift card checkout page"
        log.info("✓ Successfully navigated to gift card checkout page")

        # 步骤4: 点击支付方式下拉栏，选择PayPal
        dropdown_clicked = gift_card_page.click_payment_method_dropdown()
        assert dropdown_clicked, "Failed to click payment method dropdown"
        log.info("✓ Payment method dropdown opened")

        paypal_selected = gift_card_page.select_paypal_payment()
        assert paypal_selected, "Failed to select PayPal payment method"
        
        # 步骤5: 点击place order
        place_order_clicked = gift_card_page.click_place_order_button()
        assert place_order_clicked, "Failed to click place order button"
        
        # 验证跳转到PayPal第三方支付页面
        paypal_page_loaded = gift_card_page.check_paypal_payment_page()
        assert paypal_page_loaded, "Failed to navigate to PayPal payment page"
        log.info("✓ Successfully navigated to PayPal third-party payment page")

        gift_card_page.navigate_back()

        # 测试完成总结
        log.info("🎉 Gift Card purchase flow test completed successfully!")
        log.info("All steps executed correctly:")
        log.info("  1. ✓ Gift card main page accessed and welcome popup handled")
        log.info("  2. ✓ Recipient email entered and verified")
        log.info("  3. ✓ Checkout page loaded successfully")
        log.info("  4. ✓ PayPal payment method selected")
        log.info("  5. ✓ PayPal third-party payment page loaded")
