{"name": "未支付订单UI检查", "status": "broken", "statusDetails": {"message": "binascii.Error: Incorrect padding", "trace": "self = <src.EC.tests.account.test_108221_unpaid_orders_check.TestUnpaidOrdersCheck object at 0x1056dff20>\nrequest = <SubRequest 'setup' for <Function test_108221_unpaid_orders_check[setup0]>>\nautotest_header = {'Content-Type': 'application/json;charset=UTF-8', 'app-version': 'null', 'authorization': 'Bearer eyJraWQiOiJkZjBlZDI...-i_HjjfgE8smSzm8gNzPnakgwwuujh4jWy2iTrbPbgfyTr-HewfSRLnZ3ZQ64CAu0JSlf-QqKRKqjyHMU0W9vY', 'b-cookie': '**********', ...}\nandroid_header = {'Content-Type': 'application/json;charset=UTF-8', 'app-version': 'null', 'authorization': 'Bearer eyJraWQiOiJkZjBlZDI...Get40_wpsStoNXbsV8tu6QIz1a-KF8MFhQ2HXpAMxYRYymwuDGYdjhjcoxmRnbiBW6VYH9A-hx0OhoqLnWLTKM', 'b-cookie': '**********', ...}\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"c10ff9da-15e0-463e-9783-f8621f6dd27c\")>\n\n    @pytest.fixture(scope='function')\n    def setup(self, request, autotest_header, android_header, get_driver):\n        empty_cart(android_header)\n        empty_cart(autotest_header)\n        get_driver.start_recording_screen()\n        yield\n        empty_cart(android_header)\n        empty_cart(autotest_header)\n        video_data = get_driver.stop_recording_screen()\n        if not os.path.exists(f'./video/{os.getenv(\"BUILD_NUMBER\", 0)}'):\n            os.makedirs(f'./video/{os.getenv(\"BUILD_NUMBER\", 0)}')\n>       base64_to_mp4(video_data, f'./video/{os.getenv(\"BUILD_NUMBER\", 0)}/{request.param[0]}.mp4')\n\ntest_108221_unpaid_orders_check.py:30: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../../common/recording.py:5: in base64_to_mp4\n    binary_data = base64.b64decode(base64_string)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\ns = b'{\"value\":\"AAAAHGZ0eXBpc29tAAACAGlzb21pc28ybXA0MQAAAAhmcmVlAgw2dW1kYXT/2P/gABBKRklGAAECAABIAEgAAP/+AA9MYXZjNjEuMy4xMD...AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA'\naltchars = None, validate = False\n\n    def b64decode(s, altchars=None, validate=False):\n        \"\"\"Decode the Base64 encoded bytes-like object or ASCII string s.\n    \n        Optional altchars must be a bytes-like object or ASCII string of length 2\n        which specifies the alternative alphabet used instead of the '+' and '/'\n        characters.\n    \n        The result is returned as a bytes object.  A binascii.Error is raised if\n        s is incorrectly padded.\n    \n        If validate is False (the default), characters that are neither in the\n        normal base-64 alphabet nor the alternative alphabet are discarded prior\n        to the padding check.  If validate is True, these non-alphabet characters\n        in the input result in a binascii.Error.\n        For more information about the strict base64 check, see:\n    \n        https://docs.python.org/3.11/library/binascii.html#binascii.a2b_base64\n        \"\"\"\n        s = _bytes_from_decode_data(s)\n        if altchars is not None:\n            altchars = _bytes_from_decode_data(altchars)\n            assert len(altchars) == 2, repr(altchars)\n            s = s.translate(bytes.maketrans(altchars, b'+/'))\n>       return binascii.a2b_base64(s, strict_mode=validate)\nE       binascii.Error: Incorrect padding\n\n/opt/homebrew/Cellar/python@3.12/3.12.3/Frameworks/Python.framework/Versions/3.12/lib/python3.12/base64.py:88: Error"}, "description": "\n        测试步骤：\n        1. 用户购物车有1种类型购物车，点击结算\n        2. 进入orders页面，验证未支付订单UI\n        ", "parameters": [{"name": "setup", "value": "('test_108221_unpaid_orders_check',)"}], "start": *************, "stop": *************, "uuid": "a49d031f-9bb2-410c-87d9-3f3d711ef5ec", "historyId": "d99a03e0b3c112d1ce0166c2b3157249", "testCaseId": "4759fd6ce44ea1ef5c87b82d991960d3", "fullName": "src.EC.tests.account.test_108221_unpaid_orders_check.TestUnpaidOrdersCheck#test_108221_unpaid_orders_check", "labels": [{"name": "story", "value": "多种类型购物车结算流程测试"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.account"}, {"name": "suite", "value": "test_108221_unpaid_orders_check"}, {"name": "subSuite", "value": "TestUnpaidOrdersCheck"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "66904-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.account.test_108221_unpaid_orders_check"}]}