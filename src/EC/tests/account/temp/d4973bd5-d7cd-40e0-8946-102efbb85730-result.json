{"name": "查看订单页面各状态标签", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'click'", "trace": "self = <src.EC.tests.account.test_001_orders.TestOrders object at 0x1102a0260>\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"1756fc12-d1fb-4ff6-93c4-3c8cf0bac4ca\")>\nget_platform = 'iOS', setup = None\n\n    @allure.title(\"查看订单页面各状态标签\")\n    @pytest.mark.parametrize('setup', [('test_001_check_order_tabs', )], indirect=True)\n    def test_001_check_order_tabs(self, get_driver, get_platform, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入account页面\n        2. 点击my orders,进入orders页面\n        3. 依次点击\"pending, unshipped, shipped, to review, cancelled\"查看订单\n        \"\"\"\n        d = get_driver\n        account_page = AccountPage(d, get_platform)\n    \n        # 1. 进入account页面\n        account_page.navigate_to_account_tab()\n    \n        # 2. 点击my orders,进入orders页面\n>       account_page.navigate_to_my_orders()\n\ntest_001_orders.py:41: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../page_objects/account/account_page.py:69: in navigate_to_my_orders\n    self.click(self.driver, self.strategy.get('my_orders'))\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <src.EC.page_objects.account.account_page.AccountPage object at 0x1106b5100>\n_driver = <appium.webdriver.webdriver.WebDriver (session=\"1756fc12-d1fb-4ff6-93c4-3c8cf0bac4ca\")>\nele = ('-ios class chain', '**/XCUIElementTypeStaticText[`name == \"My Orders\"`]')\n\n    def click(self, _driver, ele):\n        element = self.find_element(_driver, ele)\n>       element.click()\nE       AttributeError: 'NoneType' object has no attribute 'click'\n\n../../../common/base.py:29: AttributeError"}, "description": "\n        测试步骤：\n        1. 进入account页面\n        2. 点击my orders,进入orders页面\n        3. 依次点击\"pending, unshipped, shipped, to review, cancelled\"查看订单\n        ", "parameters": [{"name": "setup", "value": "('test_001_check_order_tabs',)"}], "start": *************, "stop": *************, "uuid": "8bc74602-b367-44b3-8053-c271caf7fafd", "historyId": "f45987b666f9249d3a834b070fe1c59f", "testCaseId": "8d92ff0f7c4862dd2ca541fd8a3dfc5e", "fullName": "src.EC.tests.account.test_001_orders.TestOrders#test_001_check_order_tabs", "labels": [{"name": "story", "value": "查看订单页面各状态标签"}, {"name": "tag", "value": "orders"}, {"name": "parentSuite", "value": "src.EC.tests.account"}, {"name": "suite", "value": "test_001_orders"}, {"name": "subSuite", "value": "TestOrders"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "52387-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.account.test_001_orders"}]}