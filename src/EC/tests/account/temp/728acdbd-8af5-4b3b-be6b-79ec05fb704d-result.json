{"name": "查看订单页面各状态标签", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'click'", "trace": "self = <src.EC.tests.account.test_001_orders.TestOrders object at 0x108238da0>\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"e376f719-26fc-45d0-9777-dc1beb927ce6\")>\nget_platform = 'iOS', setup = None\n\n    @allure.title(\"查看订单页面各状态标签\")\n    @pytest.mark.parametrize('setup', [('test_001_check_order_tabs', )], indirect=True)\n    def test_001_check_order_tabs(self, get_driver, get_platform, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入account页面\n        2. 点击my orders,进入orders页面\n        3. 依次点击\"pending, unshipped, shipped, to review, cancelled\"查看订单\n        \"\"\"\n        d = get_driver\n        account_page = AccountPage(d, get_platform)\n    \n        # 1. 进入account页面\n        account_page.navigate_to_account_tab()\n    \n        # 2. 点击my orders,进入orders页面\n        account_page.navigate_to_my_orders()\n    \n    \n        # 3. 依次点击\"pending, unshipped, shipped, to review, cancelled\"查看订单\n    \n        # 点击并验证Pending标签页\n        account_page.click(d, account_page.strategy.get('pending_tab'))\n        time.sleep(2)  # 等待内容加载\n        assert account_page.is_tab_selected('pending'), \"Pending tab is not selected\"\n        assert account_page.is_content_displayed('pending'), \"Pending content is not displayed\"\n    \n        # 点击并验证Unshipped标签页\n        account_page.click(d, account_page.strategy.get('unshipped_tab'))\n        time.sleep(2)  # 等待内容加载\n        assert account_page.is_tab_selected('unshipped'), \"Unshipped tab is not selected\"\n        assert account_page.is_content_displayed('unshipped'), \"Unshipped content is not displayed\"\n    \n        # 点击并验证Shipped标签页\n        account_page.click(d, account_page.strategy.get('shipped_tab'))\n        time.sleep(2)  # 等待内容加载\n        assert account_page.is_tab_selected('shipped'), \"Shipped tab is not selected\"\n        assert account_page.is_content_displayed('shipped'), \"Shipped content is not displayed\"\n    \n        # 点击并验证To Review标签页\n        account_page.click(d, account_page.strategy.get('to_review_tab'))\n        time.sleep(2)  # 等待内容加载\n        assert account_page.is_tab_selected('to_review'), \"To Review tab is not selected\"\n        assert account_page.is_content_displayed('to_review'), \"To Review content is not displayed\"\n    \n        # 点击并验证Cancelled标签页\n        account_page.click(d, account_page.strategy.get('cancelled_tab'))\n        time.sleep(2)  # 等待内容加载\n        assert account_page.is_tab_selected('cancelled'), \"Cancelled tab is not selected\"\n        # assert account_page.is_content_displayed('cancelled'), \"Cancelled content is not displayed\"\n    \n        # 返回到Account页面\n>       account_page.go_back()\n\ntest_001_orders.py:77: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../page_objects/account/account_page.py:102: in go_back\n    self.click(self.driver, self.strategy.get('back_button'))\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <src.EC.page_objects.account.account_page.AccountPage object at 0x108238290>\n_driver = <appium.webdriver.webdriver.WebDriver (session=\"e376f719-26fc-45d0-9777-dc1beb927ce6\")>\nele = ('accessibility id', 'Back')\n\n    def click(self, _driver, ele):\n        element = self.find_element(_driver, ele)\n>       element.click()\nE       AttributeError: 'NoneType' object has no attribute 'click'\n\n../../../common/base.py:29: AttributeError"}, "description": "\n        测试步骤：\n        1. 进入account页面\n        2. 点击my orders,进入orders页面\n        3. 依次点击\"pending, unshipped, shipped, to review, cancelled\"查看订单\n        ", "parameters": [{"name": "setup", "value": "('test_001_check_order_tabs',)"}], "start": *************, "stop": *************, "uuid": "cd4d3cb6-c23c-408b-b689-5b4a7de21c76", "historyId": "f45987b666f9249d3a834b070fe1c59f", "testCaseId": "8d92ff0f7c4862dd2ca541fd8a3dfc5e", "fullName": "src.EC.tests.account.test_001_orders.TestOrders#test_001_check_order_tabs", "labels": [{"name": "story", "value": "查看订单页面各状态标签"}, {"name": "tag", "value": "orders"}, {"name": "parentSuite", "value": "src.EC.tests.account"}, {"name": "suite", "value": "test_001_orders"}, {"name": "subSuite", "value": "TestOrders"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "93189-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.account.test_001_orders"}]}