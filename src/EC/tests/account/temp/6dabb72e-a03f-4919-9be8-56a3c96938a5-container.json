{"uuid": "6b2fed07-9bf5-4e0a-8f99-ae5ccce8b358", "children": ["a49d031f-9bb2-410c-87d9-3f3d711ef5ec"], "befores": [{"name": "setup", "status": "passed", "start": *************, "stop": 1750403683607}], "afters": [{"name": "setup::0", "status": "broken", "statusDetails": {"message": "binascii.Error: Incorrect padding\n", "trace": "  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/allure_commons/_allure.py\", line 221, in __call__\n    return self._fixture_function(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/_pytest/fixtures.py\", line 911, in _teardown_yield_fixture\n    next(it)\n  File \"/Users/<USER>/qa-ui-android-ios/src/EC/tests/account/test_108221_unpaid_orders_check.py\", line 30, in setup\n    base64_to_mp4(video_data, f'./video/{os.getenv(\"BUILD_NUMBER\", 0)}/{request.param[0]}.mp4')\n  File \"/Users/<USER>/qa-ui-android-ios/src/common/recording.py\", line 5, in base64_to_mp4\n    binary_data = base64.b64decode(base64_string)\n                  ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.12/3.12.3/Frameworks/Python.framework/Versions/3.12/lib/python3.12/base64.py\", line 88, in b64decode\n    return binascii.a2b_base64(s, strict_mode=validate)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": *************, "stop": *************}], "start": *************, "stop": *************}