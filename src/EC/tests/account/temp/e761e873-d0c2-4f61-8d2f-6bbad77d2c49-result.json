{"name": "Gift Card完整购买流程测试", "status": "broken", "statusDetails": {"message": "selenium.common.exceptions.UnknownMethodException: Message: Method has not yet been implemented\nStacktrace:\nNotYetImplementedError: Method has not yet been implemented\n    at XCUITestDriver.executeCommand (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/driver.ts:100:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at XCUITestDriver.executeCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/driver.js:1068:12)\n    at defaultBehavior (/usr/local/lib/node_modules/appium/lib/appium.js:1109:14)\n    at AppiumDriver.executeWrappedCommand (/usr/local/lib/node_modules/appium/lib/appium.js:1215:16)\n    at AppiumDriver.executeCommand (/usr/local/lib/node_modules/appium/lib/appium.js:1121:17)\n    at asyncHandler (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/protocol.js:393:19)", "trace": "self = <appium.webdriver.webdriver.WebDriver (session=\"0d7e5f99-0636-4e3e-b266-aa054aa7eae5\")>\nkeycode = 21, metastate = None, flags = None\n\n    def press_keycode(self, keycode: int, metastate: Optional[int] = None, flags: Optional[int] = None) -> 'WebDriver':\n        \"\"\"Sends a keycode to the device.\n    \n        Android only. Possible keycodes can be found\n        in http://developer.android.com/reference/android/view/KeyEvent.html.\n    \n        Args:\n            keycode: the keycode to be sent to the device\n            metastate: meta information about the keycode being sent\n            flags: the set of key event flags\n    \n        Returns:\n            Union['WebDriver', 'Keyboard']: Self instance\n        \"\"\"\n        ext_name = 'mobile: pressKey'\n        args = {'keycode': keycode}\n        if metastate is not None:\n            args['metastate'] = metastate\n        if flags is not None:\n            args['flags'] = flags\n        try:\n>           self.assert_extension_exists(ext_name).execute_script(ext_name, args)\n\n../../../../qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/keyboard.py:112: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:408: in execute_script\n    return self.execute(command, {\"script\": script, \"args\": converted_args})[\"value\"]\n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute\n    self.error_handler.check_response(response)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x1074145c0>\nresponse = {'status': 405, 'value': '{\"value\":{\"error\":\"unknown method\",\"message\":\"Method is not implemented\",\"stacktrace\":\"NotIm...asyncHandler (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/protocol.js:393:19)\"}}'}\n\n    def check_response(self, response: Dict[str, Any]) -> None:\n        \"\"\"\n        https://www.w3.org/TR/webdriver/#errors\n        \"\"\"\n        payload = response.get('value', '')\n        if isinstance(payload, dict):\n            payload_dict = payload\n        else:\n            try:\n                payload_dict = json.loads(payload)\n            except (json.JSONDecodeError, TypeError):\n                return\n            if not isinstance(payload_dict, dict):\n                return\n        value = payload_dict.get('value')\n        if not isinstance(value, dict):\n            return\n        error = value.get('error')\n        if not error:\n            return\n    \n        message = value.get('message', error)\n        stacktrace = value.get('stacktrace', '')\n        # In theory, we should also be checking HTTP status codes.\n        # Java client, for example, prints a warning if the actual `error`\n        # value does not match to the response's HTTP status code.\n        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(\n            error, sel_exceptions.WebDriverException\n        )\n        if exception_class is sel_exceptions.WebDriverException and message:\n            if message == 'No such context found.':\n                exception_class = appium_exceptions.NoSuchContextException\n            elif message == 'That command could not be executed in the current context.':\n                exception_class = appium_exceptions.InvalidSwitchToTargetException\n    \n        if exception_class is sel_exceptions.UnexpectedAlertPresentException:\n            raise sel_exceptions.UnexpectedAlertPresentException(\n                msg=message,\n                stacktrace=format_stacktrace(stacktrace),\n                alert_text=value.get('data'),\n            )\n>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))\nE       selenium.common.exceptions.UnknownMethodException: Message: Method is not implemented\nE       Stacktrace:\nE       NotImplementedError: Method is not implemented\nE           at XCUITestDriver.execute (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/execute.js:124:13)\nE           at commandExecutor (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/driver.ts:106:18)\nE           at /usr/local/lib/node_modules/appium/node_modules/async-lock/lib/index.js:171:12\nE           at AsyncLock._promiseTry (/usr/local/lib/node_modules/appium/node_modules/async-lock/lib/index.js:306:31)\nE           at exec (/usr/local/lib/node_modules/appium/node_modules/async-lock/lib/index.js:170:9)\nE           at AsyncLock.acquire (/usr/local/lib/node_modules/appium/node_modules/async-lock/lib/index.js:189:3)\nE           at XCUITestDriver.executeCommand (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/driver.ts:122:39)\nE           at processTicksAndRejections (node:internal/process/task_queues:95:5)\nE           at XCUITestDriver.executeCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/driver.js:1068:12)\nE           at defaultBehavior (/usr/local/lib/node_modules/appium/lib/appium.js:1109:14)\nE           at AppiumDriver.executeWrappedCommand (/usr/local/lib/node_modules/appium/lib/appium.js:1215:16)\nE           at AppiumDriver.executeCommand (/usr/local/lib/node_modules/appium/lib/appium.js:1121:17)\nE           at asyncHandler (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/protocol.js:393:19)\n\n../../../../qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: UnknownMethodException\n\nDuring handling of the above exception, another exception occurred:\n\nself = <src.EC.tests.account.test_112237_gift_card_purchase_flow.TestGiftCardPurchaseFlow object at 0x104c2cdd0>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"0d7e5f99-0636-4e3e-b266-aa054aa7eae5\")>\nsetup = None\n\n    @allure.title(\"Gift Card完整购买流程测试\")\n    @pytest.mark.parametrize('setup', [('test_gift_card_purchase_flow_complete', )], indirect=True)\n    def test_112237_gift_card_purchase_flow_complete(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入Gift card主页面，处理欢迎弹窗\n        2. 填入收件人邮箱，验证输入成功\n        3. 点击checkout，进入礼品卡checkout页面\n        4. 点击支付方式下拉栏，选择PayPal\n        5. 点击place order，跳转到PayPal第三方支付页面\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n    \n        # 步骤1: 进入Gift card主页面\n        navigation_success = self.navigate_to_gift_card_page(d, platform)\n        assert navigation_success, \"Failed to navigate to gift card page\"\n    \n        # 初始化Gift Card页面对象\n        gift_card_page = GiftCardPage(d, platform)\n    \n        # 处理欢迎弹窗\n        welcome_popup_exists = gift_card_page.check_welcome_popup_exists()\n        if welcome_popup_exists:\n            popup_closed = gift_card_page.close_welcome_popup()\n            assert popup_closed, \"Failed to close welcome popup\"\n            log.info(\"✓ Welcome popup closed successfully\")\n        else:\n            log.info(\"ℹ No welcome popup found\")\n    \n        # 校验页面元素\n        assert gift_card_page.check_page_elements, \"Welcome popup still exists\"\n    \n        # 步骤2: 填入收件人邮箱\n        test_email = \"<EMAIL>\"\n        email_entered = gift_card_page.enter_recipient_email(test_email)\n        assert email_entered, f\"Failed to enter recipient email: {test_email}\"\n    \n    \n        # 步骤3: 点击checkout\n        checkout_clicked = gift_card_page.click_checkout_button()\n        assert checkout_clicked, \"Failed to click checkout button\"\n    \n        # 验证进入checkout页面\n        checkout_page_loaded = gift_card_page.check_checkout_page_loaded()\n        assert checkout_page_loaded, \"Failed to load gift card checkout page\"\n        log.info(\"✓ Successfully navigated to gift card checkout page\")\n    \n        # 步骤4: 点击支付方式下拉栏，选择PayPal\n        dropdown_clicked = gift_card_page.click_payment_method_dropdown()\n        assert dropdown_clicked, \"Failed to click payment method dropdown\"\n        log.info(\"✓ Payment method dropdown opened\")\n    \n        paypal_selected = gift_card_page.select_paypal_payment()\n        assert paypal_selected, \"Failed to select PayPal payment method\"\n    \n        # 步骤5: 点击place order\n        place_order_clicked = gift_card_page.click_place_order_button()\n        assert place_order_clicked, \"Failed to click place order button\"\n    \n        # 验证跳转到PayPal第三方支付页面\n        paypal_page_loaded = gift_card_page.check_paypal_payment_page()\n        assert paypal_page_loaded, \"Failed to navigate to PayPal payment page\"\n        log.info(\"✓ Successfully navigated to PayPal third-party payment page\")\n    \n>       gift_card_page.navigate_back()\n\ntest_112237_gift_card_purchase_flow.py:128: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../page_objects/account/gift_card_page.py:273: in navigate_back\n    self.driver.keyevent(21)\n../../../../qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/keyboard.py:89: in keyevent\n    return self.press_keycode(keycode=keycode, metastate=metastate)\n../../../../qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/keyboard.py:115: in press_keycode\n    self.mark_extension_absence(ext_name).execute(Command.PRESS_KEYCODE, args)\n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute\n    self.error_handler.check_response(response)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x1074145c0>\nresponse = {'status': 405, 'value': '{\"value\":{\"error\":\"unknown method\",\"message\":\"Method has not yet been implemented\",\"stacktra...asyncHandler (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/protocol.js:393:19)\"}}'}\n\n    def check_response(self, response: Dict[str, Any]) -> None:\n        \"\"\"\n        https://www.w3.org/TR/webdriver/#errors\n        \"\"\"\n        payload = response.get('value', '')\n        if isinstance(payload, dict):\n            payload_dict = payload\n        else:\n            try:\n                payload_dict = json.loads(payload)\n            except (json.JSONDecodeError, TypeError):\n                return\n            if not isinstance(payload_dict, dict):\n                return\n        value = payload_dict.get('value')\n        if not isinstance(value, dict):\n            return\n        error = value.get('error')\n        if not error:\n            return\n    \n        message = value.get('message', error)\n        stacktrace = value.get('stacktrace', '')\n        # In theory, we should also be checking HTTP status codes.\n        # Java client, for example, prints a warning if the actual `error`\n        # value does not match to the response's HTTP status code.\n        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(\n            error, sel_exceptions.WebDriverException\n        )\n        if exception_class is sel_exceptions.WebDriverException and message:\n            if message == 'No such context found.':\n                exception_class = appium_exceptions.NoSuchContextException\n            elif message == 'That command could not be executed in the current context.':\n                exception_class = appium_exceptions.InvalidSwitchToTargetException\n    \n        if exception_class is sel_exceptions.UnexpectedAlertPresentException:\n            raise sel_exceptions.UnexpectedAlertPresentException(\n                msg=message,\n                stacktrace=format_stacktrace(stacktrace),\n                alert_text=value.get('data'),\n            )\n>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))\nE       selenium.common.exceptions.UnknownMethodException: Message: Method has not yet been implemented\nE       Stacktrace:\nE       NotYetImplementedError: Method has not yet been implemented\nE           at XCUITestDriver.executeCommand (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/driver.ts:100:13)\nE           at processTicksAndRejections (node:internal/process/task_queues:95:5)\nE           at XCUITestDriver.executeCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/driver.js:1068:12)\nE           at defaultBehavior (/usr/local/lib/node_modules/appium/lib/appium.js:1109:14)\nE           at AppiumDriver.executeWrappedCommand (/usr/local/lib/node_modules/appium/lib/appium.js:1215:16)\nE           at AppiumDriver.executeCommand (/usr/local/lib/node_modules/appium/lib/appium.js:1121:17)\nE           at asyncHandler (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/protocol/protocol.js:393:19)\n\n../../../../qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: UnknownMethodException"}, "description": "\n        测试步骤：\n        1. 进入Gift card主页面，处理欢迎弹窗\n        2. 填入收件人邮箱，验证输入成功\n        3. 点击checkout，进入礼品卡checkout页面\n        4. 点击支付方式下拉栏，选择PayPal\n        5. 点击place order，跳转到PayPal第三方支付页面\n        ", "parameters": [{"name": "setup", "value": "('test_gift_card_purchase_flow_complete',)"}], "start": *************, "stop": *************, "uuid": "c4965512-8fa5-47a6-b9af-74360a95d77f", "historyId": "8c210d3ba6326eb98ba4bc72043c9e72", "testCaseId": "35964ddb6a6f21389640eda3abcb38f9", "fullName": "src.EC.tests.account.test_112237_gift_card_purchase_flow.TestGiftCardPurchaseFlow#test_112237_gift_card_purchase_flow_complete", "labels": [{"name": "story", "value": "Gift Card购买流程测试"}, {"name": "tag", "value": "gift_card"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.account"}, {"name": "suite", "value": "test_112237_gift_card_purchase_flow"}, {"name": "subSuite", "value": "TestGiftCardPurchaseFlow"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "6034-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.account.test_112237_gift_card_purchase_flow"}]}