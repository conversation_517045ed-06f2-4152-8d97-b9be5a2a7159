{"name": "查看订单页面各状态标签", "status": "failed", "statusDetails": {"message": "AssertionError: Pending content is not displayed\nassert False\n +  where False = <bound method AccountPage.is_content_displayed of <src.EC.page_objects.account.account_page.AccountPage object at 0x1073c3950>>('pending')\n +    where <bound method AccountPage.is_content_displayed of <src.EC.page_objects.account.account_page.AccountPage object at 0x1073c3950>> = <src.EC.page_objects.account.account_page.AccountPage object at 0x1073c3950>.is_content_displayed", "trace": "self = <src.EC.tests.account.test_001_orders.TestOrders object at 0x10732ef00>\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"d029a4ab-d090-4b6d-9165-ad201110ef8c\")>\nget_platform = 'iOS', setup = None\n\n    @allure.title(\"查看订单页面各状态标签\")\n    @pytest.mark.parametrize('setup', [('test_001_check_order_tabs', )], indirect=True)\n    def test_001_check_order_tabs(self, get_driver, get_platform, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入account页面\n        2. 点击my orders,进入orders页面\n        3. 依次点击\"pending, unshipped, shipped, to review, cancelled\"查看订单\n        \"\"\"\n        d = get_driver\n        account_page = AccountPage(d, get_platform)\n    \n        # 1. 进入account页面\n        account_page.navigate_to_account_tab()\n    \n        # 2. 点击my orders,进入orders页面\n        account_page.navigate_to_my_orders()\n    \n    \n        # 3. 依次点击\"pending, unshipped, shipped, to review, cancelled\"查看订单\n    \n        # 点击并验证Pending标签页\n        account_page.click(d, account_page.strategy.get('pending_tab'))\n        time.sleep(2)  # 等待内容加载\n        assert account_page.is_tab_selected('pending'), \"Pending tab is not selected\"\n>       assert account_page.is_content_displayed('pending'), \"Pending content is not displayed\"\nE       AssertionError: Pending content is not displayed\nE       assert False\nE        +  where False = <bound method AccountPage.is_content_displayed of <src.EC.page_objects.account.account_page.AccountPage object at 0x1073c3950>>('pending')\nE        +    where <bound method AccountPage.is_content_displayed of <src.EC.page_objects.account.account_page.AccountPage object at 0x1073c3950>> = <src.EC.page_objects.account.account_page.AccountPage object at 0x1073c3950>.is_content_displayed\n\ntest_001_orders.py:50: AssertionError"}, "description": "\n        测试步骤：\n        1. 进入account页面\n        2. 点击my orders,进入orders页面\n        3. 依次点击\"pending, unshipped, shipped, to review, cancelled\"查看订单\n        ", "parameters": [{"name": "setup", "value": "('test_001_check_order_tabs',)"}], "start": *************, "stop": *************, "uuid": "b1a237a6-b04e-45af-8f1e-e62ea55f4d37", "historyId": "f45987b666f9249d3a834b070fe1c59f", "testCaseId": "8d92ff0f7c4862dd2ca541fd8a3dfc5e", "fullName": "src.EC.tests.account.test_001_orders.TestOrders#test_001_check_order_tabs", "labels": [{"name": "story", "value": "查看订单页面各状态标签"}, {"name": "tag", "value": "orders"}, {"name": "parentSuite", "value": "src.EC.tests.account"}, {"name": "suite", "value": "test_001_orders"}, {"name": "subSuite", "value": "TestOrders"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "54730-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.account.test_001_orders"}]}