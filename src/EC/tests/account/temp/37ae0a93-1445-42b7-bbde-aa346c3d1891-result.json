{"name": "未支付订单UI检查", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'click'", "trace": "self = <src.EC.tests.account.test_108221_unpaid_orders_check.TestUnpaidOrdersCheck object at 0x1070516a0>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"15caa0da-1810-4782-b602-89addfb2cd11\")>\nsetup = None\n\n    @allure.title(\"未支付订单UI检查\")\n    @pytest.mark.parametrize('setup', [('test_108221_unpaid_orders_check', )], indirect=True)\n    def test_108221_unpaid_orders_check(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1. 用户购物车有1种类型购物车，点击结算\n        2. 进入orders页面，验证未支付订单UI\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n        orders = Orders(d, platform)\n    \n        # 关掉首页广告\n        adv = self.find_element(_driver=d, ele=orders.strategy.get(\"popup_close\"))\n        if adv:\n            adv.click()\n    \n        # 1. 加购pantry商品\n        orders.add_product_from_home()\n    \n        # 2. 进入购物车页面\n        assert orders.navigate_to_cart(), f\"Failed to navigate to cart page\"\n    \n        # 3. 验证购物车中各元素\n        assert orders.click_checkout_button(), f\"Pantry cart should exist in the cart\"\n    \n        # 4. 点击结算按钮\n        assert orders.place_order(), f\"Failed to click checkout button\"\n    \n        orders.go_back_from_paypal_to_home()\n    \n>       orders.navigate_to_account_tab()\n\ntest_108221_unpaid_orders_check.py:63: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../page_objects/account/orders.py:129: in navigate_to_account_tab\n    self.click(self.driver, self.strategy.get('account_tab'))\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <src.EC.page_objects.account.orders.Orders object at 0x1070ede80>\n_driver = <appium.webdriver.webdriver.WebDriver (session=\"15caa0da-1810-4782-b602-89addfb2cd11\")>\nele = ('accessibility id', 'Account')\n\n    def click(self, _driver, ele):\n        element = self.find_element(_driver, ele)\n>       element.click()\nE       AttributeError: 'NoneType' object has no attribute 'click'\n\n../../../common/base.py:29: AttributeError"}, "description": "\n        测试步骤：\n        1. 用户购物车有1种类型购物车，点击结算\n        2. 进入orders页面，验证未支付订单UI\n        ", "parameters": [{"name": "setup", "value": "('test_108221_unpaid_orders_check',)"}], "start": *************, "stop": *************, "uuid": "fe88f6f1-6893-413c-9a79-6e36e5b6e1a7", "historyId": "d99a03e0b3c112d1ce0166c2b3157249", "testCaseId": "4759fd6ce44ea1ef5c87b82d991960d3", "fullName": "src.EC.tests.account.test_108221_unpaid_orders_check.TestUnpaidOrdersCheck#test_108221_unpaid_orders_check", "labels": [{"name": "story", "value": "多种类型购物车结算流程测试"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.account"}, {"name": "suite", "value": "test_108221_unpaid_orders_check"}, {"name": "subSuite", "value": "TestUnpaidOrdersCheck"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "66782-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.account.test_108221_unpaid_orders_check"}]}