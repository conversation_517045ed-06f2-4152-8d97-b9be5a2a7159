{"name": "Gift Card完整购买流程测试", "status": "failed", "statusDetails": {"message": "AssertionError: Failed to navigate to gift card page\nassert False", "trace": "self = <src.EC.tests.account.test_gift_card_purchase_flow.TestGiftCardPurchaseFlow object at 0x1038db2f0>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"da595705-7948-4747-9fc8-ff3bd1d1dc84\")>\nsetup = None\n\n    @allure.title(\"Gift Card完整购买流程测试\")\n    @pytest.mark.parametrize('setup', [('test_gift_card_purchase_flow_complete', )], indirect=True)\n    def test_112237_gift_card_purchase_flow_complete(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入Gift card主页面，处理欢迎弹窗\n        2. 填入收件人邮箱，验证输入成功\n        3. 点击checkout，进入礼品卡checkout页面\n        4. 点击支付方式下拉栏，选择PayPal\n        5. 点击place order，跳转到PayPal第三方支付页面\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n    \n        # 步骤1: 进入Gift card主页面\n        navigation_success = self.navigate_to_gift_card_page(d, platform)\n>       assert navigation_success, \"Failed to navigate to gift card page\"\nE       AssertionError: Failed to navigate to gift card page\nE       assert False\n\ntest_gift_card_purchase_flow.py:76: AssertionError"}, "description": "\n        测试步骤：\n        1. 进入Gift card主页面，处理欢迎弹窗\n        2. 填入收件人邮箱，验证输入成功\n        3. 点击checkout，进入礼品卡checkout页面\n        4. 点击支付方式下拉栏，选择PayPal\n        5. 点击place order，跳转到PayPal第三方支付页面\n        ", "parameters": [{"name": "setup", "value": "('test_gift_card_purchase_flow_complete',)"}], "start": *************, "stop": *************, "uuid": "284f7470-3a86-4436-946c-55ba22bd7036", "historyId": "2d9ac06c9ea15f5df9aabe1c87f51b79", "testCaseId": "a5703f4b213298971fdb796e9152c5bc", "fullName": "src.EC.tests.account.test_gift_card_purchase_flow.TestGiftCardPurchaseFlow#test_112237_gift_card_purchase_flow_complete", "labels": [{"name": "story", "value": "Gift Card购买流程测试"}, {"name": "tag", "value": "gift_card"}, {"name": "tag", "value": "purchase_flow"}, {"name": "parentSuite", "value": "src.EC.tests.account"}, {"name": "suite", "value": "test_gift_card_purchase_flow"}, {"name": "subSuite", "value": "TestGiftCardPurchaseFlow"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "41564-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.account.test_gift_card_purchase_flow"}]}