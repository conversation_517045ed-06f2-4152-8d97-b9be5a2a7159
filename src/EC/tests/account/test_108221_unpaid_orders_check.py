import os
import time
import allure
import pytest

from src.EC.page_objects.account.orders import Orders
from src.EC.page_objects.cart.single_cart_checkout_page import SingleCartCheckoutPage
from src.EC.tests.base_case import BaseCase
from src.api.commonapi.commfunc import empty_cart
from src.common.recording import base64_to_mp4
from src.config.weee.log_help import log


@allure.story("多种类型购物车结算流程测试")
class TestUnpaidOrdersCheck(BaseCase):

    pytestmark = [pytest.mark.regression]

    @pytest.fixture(scope='function')
    def setup(self, request, autotest_header, android_header, get_driver):
        empty_cart(android_header)
        empty_cart(autotest_header)
        get_driver.start_recording_screen()
        yield
        empty_cart(android_header)
        empty_cart(autotest_header)
        video_data = get_driver.stop_recording_screen()
        if not os.path.exists(f'./video/{os.getenv("BUILD_NUMBER", 0)}'):
            os.makedirs(f'./video/{os.getenv("BUILD_NUMBER", 0)}')
        base64_to_mp4(video_data, f'./video/{os.getenv("BUILD_NUMBER", 0)}/{request.param[0]}.mp4')

    @allure.title("未支付订单UI检查")
    @pytest.mark.parametrize('setup', [('test_108221_unpaid_orders_check', )], indirect=True)
    def test_108221_unpaid_orders_check(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. 用户购物车有1种类型购物车，点击结算
        2. 进入orders页面，验证未支付订单UI
        """
        d = get_driver
        platform = get_platform
        orders = Orders(d, platform)

        # 关掉首页广告
        adv = self.find_element(_driver=d, ele=orders.strategy.get("popup_close"))
        if adv:
            adv.click()

        # 1. 加购pantry商品
        orders.add_product_from_home()

        # 2. 进入购物车页面
        assert orders.navigate_to_cart(), f"Failed to navigate to cart page"

        # 3. 验证购物车中各元素
        assert orders.click_checkout_button(), f"Pantry cart should exist in the cart"

        # 4. 点击结算按钮
        assert orders.place_order(), f"Failed to click checkout button"

        orders.go_back_from_paypal_to_home()

        orders.navigate_to_account_tab()

        orders.navigate_to_my_orders()

        orders.go_to_unpaid_orders_tab()

        assert orders.check_unpaid_order_ui(), f"Failed to check unpaid order UI"

        orders.cancel_unpaid_orders()

        orders.go_back_from_my_orders_to_account()



