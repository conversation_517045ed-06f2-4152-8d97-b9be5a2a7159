import os
import time
import allure
import pytest

from src.EC.page_objects.account.orders import Orders
from src.EC.page_objects.cart.single_cart_checkout_page import SingleCartCheckoutPage
from src.EC.tests.base_case import BaseCase
from src.api.commonapi.commfunc import empty_cart
from src.common.recording import base64_to_mp4
from src.config.weee.log_help import log


@allure.story("多种类型购物车结算流程测试")
class TestRewardsTotalSavingCheck(BaseCase):

    pytestmark = [pytest.mark.regression]

    @pytest.fixture(scope='function')
    def setup(self, request, autotest_header, android_header, get_driver):
        empty_cart(android_header)
        empty_cart(autotest_header)
        get_driver.start_recording_screen()
        yield
        empty_cart(android_header)
        empty_cart(autotest_header)
        video_data = get_driver.stop_recording_screen()
        if not os.path.exists(f'./video/{os.getenv("BUILD_NUMBER", 0)}'):
            os.makedirs(f'./video/{os.getenv("BUILD_NUMBER", 0)}')
        base64_to_mp4(video_data, f'./video/{os.getenv("BUILD_NUMBER", 0)}/{request.param[0]}.mp4')

    @allure.title("未支付订单UI检查")
    @pytest.mark.parametrize('setup', [('test_111415_rewards_total_saving_check', )], indirect=True)
    @pytest.mark.skip(reason="pending, 几个账号均没有这样的数据，等有数据后再编写")
    def test_111415_rewards_total_saving_check(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. 进入rewards portal，点击banner total saving横幅
        2. 验证total saving弹窗数据
        """
        # todo 几个账号均没有这样的数据，等有数据后再编写
        d = get_driver
        platform = get_platform
        orders = Orders(d, platform)

        # 关掉首页广告
        adv = self.find_element(_driver=d, ele=orders.strategy.get("popup_close"))
        if adv:
            adv.click()



