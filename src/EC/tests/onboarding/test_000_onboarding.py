import os
import time

import allure
import pytest

from src.EC.tests.base_case import BaseCase
from src.api.commonapi.commfunc import empty_cart
from src.common.recording import base64_to_mp4
from src.EC.page_objects.onboarding.onboarding_page import Onboarding


@allure.story("android-onboarding,先购买和后登陆和先登陆后购买2个流程")
class TestOnboarding(BaseCase):

    pytestmark = [pytest.mark.android_onboarding]
    @pytest.fixture(scope='function')
    def setup(self, request, android_header, get_onboarding_driver):
        # 清空购物车
        empty_cart(android_header)
        get_onboarding_driver.start_recording_screen()
        yield
        video_data = get_onboarding_driver.stop_recording_screen()
        if not os.path.exists(f'./video/{os.getenv("BUILD_NUMBER", 0)}'):
            os.makedirs(f'./video/{os.getenv("BUILD_NUMBER", 0)}')
        base64_to_mp4(video_data, f'./video/{os.getenv("BUILD_NUMBER", 0)}/{request.param[0]}.mp4')

    @allure.title("onboarding-先购买checkout时登陆")
    @pytest.mark.parametrize('setup', [('test_000_onboarding_without_login', )], indirect=True)
    def test_000_onboarding_without_login(self, get_onboarding_driver, get_platform, setup):
        """先加购，checkout时再登陆"""
        d = get_onboarding_driver
        onboarding_page = Onboarding(d, get_platform)
        # self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get("switch_language")).click()

        onboarding_page.onboarding()
        onboarding_page.add_to_cart_on_home()
        onboarding_page.goto_cart_and_checkout()
        onboarding_page.login_with_email(email='<EMAIL>', password='********')
        onboarding_page.checkout_after_login()
        # onboarding_page.find_element(key='store_locator')

    @allure.title("onboarding-先登陆后购买")
    @pytest.mark.parametrize('setup', [('test_001_onboarding_with_login', )], indirect=True)
    def test_001_onboarding_with_login(self, get_onboarding_driver, get_platform, setup):
        """先登陆后加购"""
        d = get_onboarding_driver
        onboarding_page = Onboarding(d, get_platform)
        onboarding_page.onboarding()
        # 登陆
        self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get("home_account")).click()
        self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get("login_button")).click()
        onboarding_page.login_with_email(email='<EMAIL>', password='********')
        time.sleep(5)
        # ios no pick price page
        pick_price_skip = self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get("pick_price_skip"))
        # 这里是动态的
        if pick_price_skip:
            pick_price_skip.click()
        else:
            self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get("home")).click()
            time.sleep(8)
            # ios有广告，安卓不知道有没有，如果有，也要加上
            if os.getenv("platform") == 'iOS':
                # 去除首页广告
                adv = self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get("popup_close"))
                if adv:
                    adv.click()
                time.sleep(5)
                not_now = self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get("not_now"))
                if not_now:
                    not_now.click()
            d.swipe(500, 500, 500, 350)

        onboarding_page.add_to_cart_on_home()
        onboarding_page.goto_cart_and_checkout()
        self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get("place_order")).click()








