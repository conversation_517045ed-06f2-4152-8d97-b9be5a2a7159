{"name": "onboarding-先购买checkout时登陆", "status": "broken", "statusDetails": {"message": "TypeError: object of type 'NoneType' has no len()", "trace": "self = <src.EC.tests.onboarding.test_000_onboarding.TestOnboarding object at 0x105dda1b0>\nget_onboarding_driver = <appium.webdriver.webdriver.WebDriver (session=\"ebf1d8b1-660e-4ff8-aff3-8263fe2b33c4\")>\nget_platform = 'iOS', setup = None\n\n>   ???\n\n/Users/<USER>/qa-ui-android/src/EC/tests/onboarding/test_000_onboarding.py:34: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <src.EC.page_objects.onboarding.onboarding_page.Onboarding object at 0x1099b77d0>\n\n    def add_to_cart_on_home(self):\n        self.driver.swipe(500, 500, 500, 350)\n        add_to_cart_button = self.find_elements(self.driver, self.strategy.get(\"home_add_to_cart\"))\n>       log.info(f\"add_to_cart_button===> {len(add_to_cart_button)}\")\nE       TypeError: object of type 'NoneType' has no len()\n\n../../page_objects/onboarding/onboarding_page.py:99: TypeError"}, "description": "先加购，checkout时再登陆", "parameters": [{"name": "setup", "value": "('test_000_onboarding_without_login',)"}], "start": 1748223250676, "stop": 1748223314484, "uuid": "407e7ce9-a86a-432d-8c4c-adc73b87c844", "historyId": "af3e511ef39aea9764ba3e71dbd2763f", "testCaseId": "97107ccb9079c24e48c6229d6e599e2b", "fullName": "src.EC.tests.onboarding.test_000_onboarding.TestOnboarding#test_000_onboarding_without_login", "labels": [{"name": "story", "value": "android-onboarding,先购买和后登陆和先登陆后购买2个流程"}, {"name": "tag", "value": "android_onboarding"}, {"name": "parentSuite", "value": "src.EC.tests.onboarding"}, {"name": "suite", "value": "test_000_onboarding"}, {"name": "subSuite", "value": "TestOnboarding"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "18481-Main<PERSON><PERSON>ead"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.onboarding.test_000_onboarding"}]}