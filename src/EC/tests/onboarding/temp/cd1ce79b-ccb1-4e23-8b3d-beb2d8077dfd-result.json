{"name": "onboarding-先购买checkout时登陆", "status": "broken", "statusDetails": {"message": "selenium.common.exceptions.NoSuchElementException: Message: An element could not be located on the page using the given search parameters.; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\nNoSuchElementError: An element could not be located on the page using the given search parameters.\n    at XCUITestDriver.doNativeFind (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:143:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at XCUITestDriver.findNativeElementOrElements (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:97:12)\n    at XCUITestDriver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:24:14)\n    at XCUITestDriver.findElOrElsWithProcessing (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)\n    at XCUITestDriver.findElement (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:75:12)", "trace": "@pytest.fixture(scope=\"function\")\n    def get_onboarding_driver():\n        driver = <PERSON><PERSON>Manager(os.getenv(\"platform\", \"Android\")).initialize_driver()\n        # iphone比较复杂，需要特殊处理\n        if os.getenv('platform') == 'iOS':\n            driver.quit()\n>           get_preferences_driver()\n\n../../conftest.py:98: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../conftest.py:114: in get_preferences_driver\n    preferences_driver.find_element(*ele_preference_weee).click()\n../../../../qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/webdriver.py:384: in find_element\n    return self.execute(RemoteCommand.FIND_ELEMENT, {'using': by, 'value': value})['value']\n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute\n    self.error_handler.check_response(response)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x106a0ba40>\nresponse = {'status': 404, 'value': '{\"value\":{\"error\":\"no such element\",\"message\":\"An element could not be located on the page u...lement (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:75:12)\"}}'}\n\n    def check_response(self, response: Dict[str, Any]) -> None:\n        \"\"\"\n        https://www.w3.org/TR/webdriver/#errors\n        \"\"\"\n        payload = response.get('value', '')\n        if isinstance(payload, dict):\n            payload_dict = payload\n        else:\n            try:\n                payload_dict = json.loads(payload)\n            except (json.JSONDecodeError, TypeError):\n                return\n            if not isinstance(payload_dict, dict):\n                return\n        value = payload_dict.get('value')\n        if not isinstance(value, dict):\n            return\n        error = value.get('error')\n        if not error:\n            return\n    \n        message = value.get('message', error)\n        stacktrace = value.get('stacktrace', '')\n        # In theory, we should also be checking HTTP status codes.\n        # Java client, for example, prints a warning if the actual `error`\n        # value does not match to the response's HTTP status code.\n        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(\n            error, sel_exceptions.WebDriverException\n        )\n        if exception_class is sel_exceptions.WebDriverException and message:\n            if message == 'No such context found.':\n                exception_class = appium_exceptions.NoSuchContextException\n            elif message == 'That command could not be executed in the current context.':\n                exception_class = appium_exceptions.InvalidSwitchToTargetException\n    \n        if exception_class is sel_exceptions.UnexpectedAlertPresentException:\n            raise sel_exceptions.UnexpectedAlertPresentException(\n                msg=message,\n                stacktrace=format_stacktrace(stacktrace),\n                alert_text=value.get('data'),\n            )\n>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))\nE       selenium.common.exceptions.NoSuchElementException: Message: An element could not be located on the page using the given search parameters.; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nE       Stacktrace:\nE       NoSuchElementError: An element could not be located on the page using the given search parameters.\nE           at XCUITestDriver.doNativeFind (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:143:13)\nE           at processTicksAndRejections (node:internal/process/task_queues:95:5)\nE           at XCUITestDriver.findNativeElementOrElements (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:97:12)\nE           at XCUITestDriver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:24:14)\nE           at XCUITestDriver.findElOrElsWithProcessing (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)\nE           at XCUITestDriver.findElement (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:75:12)\n\n../../../../qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: NoSuchElementException"}, "description": "先加购，checkout时再登陆", "parameters": [{"name": "setup", "value": "('test_000_onboarding_without_login',)"}], "start": 1747990609712, "stop": 1747990609712, "uuid": "ef062030-61b2-4210-9937-8485709b2439", "historyId": "af3e511ef39aea9764ba3e71dbd2763f", "testCaseId": "97107ccb9079c24e48c6229d6e599e2b", "fullName": "src.EC.tests.onboarding.test_000_onboarding.TestOnboarding#test_000_onboarding_without_login", "labels": [{"name": "story", "value": "android-onboarding,先购买和后登陆和先登陆后购买2个流程"}, {"name": "tag", "value": "android_onboarding"}, {"name": "parentSuite", "value": "src.EC.tests.onboarding"}, {"name": "suite", "value": "test_000_onboarding"}, {"name": "subSuite", "value": "TestOnboarding"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "54248-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.onboarding.test_000_onboarding"}]}