{"name": "onboarding-先购买checkout时登陆", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'click'", "trace": "self = <src.EC.tests.onboarding.test_000_onboarding.TestOnboarding object at 0x103150560>\nget_onboarding_driver = <appium.webdriver.webdriver.WebDriver (session=\"6cdef54e-58ae-45cf-85e1-d7393998e63b\")>\nget_platform = 'iOS', setup = None\n\n    @allure.title(\"onboarding-先购买checkout时登陆\")\n    @pytest.mark.parametrize('setup', [('test_000_onboarding_without_login', )], indirect=True)\n    def test_000_onboarding_without_login(self, get_onboarding_driver, get_platform, setup):\n        \"\"\"先加购，checkout时再登陆\"\"\"\n        d = get_onboarding_driver\n        onboarding_page = Onboarding(d, get_platform)\n        # self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get(\"switch_language\")).click()\n    \n        onboarding_page.onboarding()\n        onboarding_page.add_to_cart_on_home()\n        onboarding_page.goto_cart_and_checkout()\n>       onboarding_page.login_with_email(email='<EMAIL>', password='********')\n\ntest_000_onboarding.py:39: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../page_objects/onboarding/onboarding_page.py:139: in login_with_email\n    self.click(self.driver, self.strategy.get(\"enter_your_email\"))\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <src.EC.page_objects.onboarding.onboarding_page.Onboarding object at 0x103c3d4c0>\n_driver = <appium.webdriver.webdriver.WebDriver (session=\"6cdef54e-58ae-45cf-85e1-d7393998e63b\")>\nele = ('accessibility id', 'et_account')\n\n    def click(self, _driver, ele):\n        element = self.find_element(_driver, ele)\n>       element.click()\nE       AttributeError: 'NoneType' object has no attribute 'click'\n\n../../../common/base.py:29: AttributeError"}, "description": "先加购，checkout时再登陆", "parameters": [{"name": "setup", "value": "('test_000_onboarding_without_login',)"}], "start": *************, "stop": *************, "uuid": "dbbc1967-7eb8-4b63-9848-db5920b4328d", "historyId": "af3e511ef39aea9764ba3e71dbd2763f", "testCaseId": "97107ccb9079c24e48c6229d6e599e2b", "fullName": "src.EC.tests.onboarding.test_000_onboarding.TestOnboarding#test_000_onboarding_without_login", "labels": [{"name": "story", "value": "android-onboarding,先购买和后登陆和先登陆后购买2个流程"}, {"name": "tag", "value": "android_onboarding"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.onboarding"}, {"name": "suite", "value": "test_000_onboarding"}, {"name": "subSuite", "value": "TestOnboarding"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "42801-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.onboarding.test_000_onboarding"}]}