{"name": "onboarding-先登陆后购买", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'click'", "trace": "self = <src.EC.tests.onboarding.test_000_onboarding.TestOnboarding object at 0x107e12cc0>\nget_onboarding_driver = <appium.webdriver.webdriver.WebDriver (session=\"3f842505-bf51-4911-83a6-1084f40b5e5b\")>\nget_platform = 'iOS', setup = None\n\n    @allure.title(\"onboarding-先登陆后购买\")\n    @pytest.mark.parametrize('setup', [('test_001_onboarding_with_login', )], indirect=True)\n    def test_001_onboarding_with_login(self, get_onboarding_driver, get_platform, setup):\n        \"\"\"先登陆后加购\"\"\"\n        d = get_onboarding_driver\n        onboarding_page = Onboarding(d, get_platform)\n        onboarding_page.onboarding()\n        # 登陆\n        self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get(\"home_account\")).click()\n        self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get(\"login_button\")).click()\n        onboarding_page.login_with_email(email='<EMAIL>', password='********')\n        time.sleep(5)\n        # ios no pick price page\n        pick_price_skip = self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get(\"pick_price_skip\"))\n        # 这里是动态的\n        if pick_price_skip:\n            pick_price_skip.click()\n        else:\n            self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get(\"home\")).click()\n            time.sleep(8)\n            # ios有广告，安卓不知道有没有，如果有，也要加上\n            if os.getenv(\"platform\") == 'iOS':\n                # 去除首页广告\n                adv = self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get(\"popup_close\"))\n                if adv:\n                    adv.click()\n                time.sleep(5)\n                not_now = self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get(\"not_now\"))\n                if not_now:\n                    not_now.click()\n            d.swipe(500, 500, 500, 350)\n    \n        onboarding_page.add_to_cart_on_home()\n>       onboarding_page.goto_cart_and_checkout()\n\ntest_000_onboarding.py:76: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../page_objects/onboarding/onboarding_page.py:128: in goto_cart_and_checkout\n    time.sleep(3)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <src.EC.page_objects.onboarding.onboarding_page.Onboarding object at 0x107e122a0>\n_driver = <appium.webdriver.webdriver.WebDriver (session=\"3f842505-bf51-4911-83a6-1084f40b5e5b\")>\nele = ('accessibility id', 'tv_checkout')\n\n    def click(self, _driver, ele):\n        element = self.find_element(_driver, ele)\n>       element.click()\nE       AttributeError: 'NoneType' object has no attribute 'click'\n\n../../../common/base.py:29: AttributeError"}, "description": "先登陆后加购", "parameters": [{"name": "setup", "value": "('test_001_onboarding_with_login',)"}], "start": 1748227265152, "stop": 1748228375766, "uuid": "0784d52b-8e6b-4da8-97f9-6989b3cf36fb", "historyId": "0225758dfc921cd392b6151543cb35c1", "testCaseId": "55f5d1d59dde2a1689d477c33d45b90a", "fullName": "src.EC.tests.onboarding.test_000_onboarding.TestOnboarding#test_001_onboarding_with_login", "labels": [{"name": "story", "value": "android-onboarding,先购买和后登陆和先登陆后购买2个流程"}, {"name": "tag", "value": "android_onboarding"}, {"name": "parentSuite", "value": "src.EC.tests.onboarding"}, {"name": "suite", "value": "test_000_onboarding"}, {"name": "subSuite", "value": "TestOnboarding"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "37667-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.onboarding.test_000_onboarding"}]}