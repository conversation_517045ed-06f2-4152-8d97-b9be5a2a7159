{"name": "onboarding-先登陆后购买", "status": "passed", "description": "先登陆后加购", "parameters": [{"name": "setup", "value": "('test_001_onboarding_with_login',)"}], "start": 1748229068772, "stop": 1748229220540, "uuid": "ca903816-**************-08a8c3a8845f", "historyId": "0225758dfc921cd392b6151543cb35c1", "testCaseId": "55f5d1d59dde2a1689d477c33d45b90a", "fullName": "src.EC.tests.onboarding.test_000_onboarding.TestOnboarding#test_001_onboarding_with_login", "labels": [{"name": "story", "value": "android-onboarding,先购买和后登陆和先登陆后购买2个流程"}, {"name": "tag", "value": "android_onboarding"}, {"name": "parentSuite", "value": "src.EC.tests.onboarding"}, {"name": "suite", "value": "test_000_onboarding"}, {"name": "subSuite", "value": "TestOnboarding"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "48448-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.onboarding.test_000_onboarding"}]}