{"name": "onboarding-先购买checkout时登陆", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'click'", "trace": "self = <src.EC.tests.onboarding.test_000_onboarding.TestOnboarding object at 0x10240a060>\nget_onboarding_driver = <appium.webdriver.webdriver.WebDriver (session=\"6ced971e-94c1-4faf-a1e9-a3d44c4f1c49\")>\nget_platform = 'iOS', setup = None\n\n>   ???\n\n/Users/<USER>/qa-ui-android/src/EC/tests/onboarding/test_000_onboarding.py:33: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../page_objects/onboarding/onboarding_page.py:75: in onboarding\n    self.click(self.driver, self.strategy.get('shop_now'))\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <src.EC.page_objects.onboarding.onboarding_page.Onboarding object at 0x1040b77d0>\n_driver = <appium.webdriver.webdriver.WebDriver (session=\"6ced971e-94c1-4faf-a1e9-a3d44c4f1c49\")>\nele = ('-ios class chain', '**/XCUIElementTypeStaticText[`name == \"Shop now\"`]')\n\n    def click(self, _driver, ele):\n        element = self.find_element(_driver, ele)\n>       element.click()\nE       AttributeError: 'NoneType' object has no attribute 'click'\n\n../../../common/base.py:29: AttributeError"}, "description": "先加购，checkout时再登陆", "parameters": [{"name": "setup", "value": "('test_000_onboarding_without_login',)"}], "start": 1747988755068, "stop": 1747988765465, "uuid": "fabb85a3-cce7-46ab-8afd-2a633a6baf22", "historyId": "af3e511ef39aea9764ba3e71dbd2763f", "testCaseId": "97107ccb9079c24e48c6229d6e599e2b", "fullName": "src.EC.tests.onboarding.test_000_onboarding.TestOnboarding#test_000_onboarding_without_login", "labels": [{"name": "story", "value": "android-onboarding,先购买和后登陆和先登陆后购买2个流程"}, {"name": "tag", "value": "android_onboarding"}, {"name": "parentSuite", "value": "src.EC.tests.onboarding"}, {"name": "suite", "value": "test_000_onboarding"}, {"name": "subSuite", "value": "TestOnboarding"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "42741-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.onboarding.test_000_onboarding"}]}