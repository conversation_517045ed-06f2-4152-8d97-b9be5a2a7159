{"name": "onboarding-先购买checkout时登陆", "status": "broken", "statusDetails": {"message": "selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: socket hang up\nStacktrace:\nUnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: socket hang up\n    at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)\n    at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)", "trace": "self = <src.EC.tests.onboarding.test_000_onboarding.TestOnboarding object at 0x103578860>\nrequest = <SubRequest 'setup' for <Function test_000_onboarding_without_login[setup0]>>\nandroid_header = {'Content-Type': 'application/json;charset=UTF-8', 'app-version': 'null', 'authorization': 'Bearer eyJraWQiOiJkZjBlZDI...wTHq6NN17-io-FlpCoeFQLsiwexZ2BJKEkHSjyYMMHm4gK1dhmaTLjD_H9Jg5i0b5a4B4E7hcqvQhCHRsZzp5k', 'b-cookie': '1277035045', ...}\nget_onboarding_driver = <appium.webdriver.webdriver.WebDriver (session=\"d2177491-a0e5-491a-a016-cf0b57099bc2\")>\n\n    @pytest.fixture(scope='function')\n    def setup(self, request, android_header, get_onboarding_driver):\n        # 清空购物车\n        empty_cart(android_header)\n>       get_onboarding_driver.start_recording_screen()\n\ntest_000_onboarding.py:21: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../../../qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/screen_record.py:162: in start_recording_screen\n    return self.execute(Command.START_RECORDING_SCREEN, {'options': options})['value']\n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute\n    self.error_handler.check_response(response)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x1053d3ce0>\nresponse = {'status': 500, 'value': '{\"value\":{\"error\":\"unknown error\",\"message\":\"An unknown server-side error occurred while pro...cordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)\"}}'}\n\n    def check_response(self, response: Dict[str, Any]) -> None:\n        \"\"\"\n        https://www.w3.org/TR/webdriver/#errors\n        \"\"\"\n        payload = response.get('value', '')\n        if isinstance(payload, dict):\n            payload_dict = payload\n        else:\n            try:\n                payload_dict = json.loads(payload)\n            except (json.JSONDecodeError, TypeError):\n                return\n            if not isinstance(payload_dict, dict):\n                return\n        value = payload_dict.get('value')\n        if not isinstance(value, dict):\n            return\n        error = value.get('error')\n        if not error:\n            return\n    \n        message = value.get('message', error)\n        stacktrace = value.get('stacktrace', '')\n        # In theory, we should also be checking HTTP status codes.\n        # Java client, for example, prints a warning if the actual `error`\n        # value does not match to the response's HTTP status code.\n        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(\n            error, sel_exceptions.WebDriverException\n        )\n        if exception_class is sel_exceptions.WebDriverException and message:\n            if message == 'No such context found.':\n                exception_class = appium_exceptions.NoSuchContextException\n            elif message == 'That command could not be executed in the current context.':\n                exception_class = appium_exceptions.InvalidSwitchToTargetException\n    \n        if exception_class is sel_exceptions.UnexpectedAlertPresentException:\n            raise sel_exceptions.UnexpectedAlertPresentException(\n                msg=message,\n                stacktrace=format_stacktrace(stacktrace),\n                alert_text=value.get('data'),\n            )\n>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))\nE       selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: socket hang up\nE       Stacktrace:\nE       UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: socket hang up\nE           at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)\nE           at processTicksAndRejections (node:internal/process/task_queues:95:5)\nE           at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)\nE           at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)\n\n../../../../qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: WebDriverException"}, "description": "先加购，checkout时再登陆", "parameters": [{"name": "setup", "value": "('test_000_onboarding_without_login',)"}], "start": 1749112572913, "stop": 1749112572913, "uuid": "f523cdb8-c61b-4f2e-815b-e0ce7495c81f", "historyId": "af3e511ef39aea9764ba3e71dbd2763f", "testCaseId": "97107ccb9079c24e48c6229d6e599e2b", "fullName": "src.EC.tests.onboarding.test_000_onboarding.TestOnboarding#test_000_onboarding_without_login", "labels": [{"name": "story", "value": "android-onboarding,先购买和后登陆和先登陆后购买2个流程"}, {"name": "tag", "value": "android_onboarding"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.onboarding"}, {"name": "suite", "value": "test_000_onboarding"}, {"name": "subSuite", "value": "TestOnboarding"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "42681-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.onboarding.test_000_onboarding"}]}