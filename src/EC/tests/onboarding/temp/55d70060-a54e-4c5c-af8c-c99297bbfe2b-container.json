{"uuid": "a597ad9f-f4a7-4258-8ac6-79dae9ac06d7", "children": ["f523cdb8-c61b-4f2e-815b-e0ce7495c81f"], "befores": [{"name": "setup", "status": "broken", "statusDetails": {"message": "selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: socket hang up\nStacktrace:\nUnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: socket hang up\n    at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)\n    at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)\n", "trace": "  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/pluggy/_callers.py\", line 77, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/_pytest/fixtures.py\", line 1123, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/_pytest/fixtures.py\", line 895, in call_fixture_func\n    fixture_result = next(generator)\n                     ^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/src/EC/tests/onboarding/test_000_onboarding.py\", line 21, in setup\n    get_onboarding_driver.start_recording_screen()\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/screen_record.py\", line 162, in start_recording_screen\n    return self.execute(Command.START_RECORDING_SCREEN, {'options': options})['value']\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py\", line 348, in execute\n    self.error_handler.check_response(response)\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py\", line 125, in check_response\n    raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))\n"}, "start": 1749112650253, "stop": 1749112651732}], "start": 1749112650253, "stop": 1749112651899}