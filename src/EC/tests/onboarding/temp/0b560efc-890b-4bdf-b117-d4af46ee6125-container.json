{"uuid": "1aa726f0-7066-43fd-8500-4078e3aec11c", "children": ["dbbc1967-7eb8-4b63-9848-db5920b4328d"], "befores": [{"name": "get_onboarding_driver", "status": "passed", "start": 1749112677315, "stop": 1749112726347}], "afters": [{"name": "get_onboarding_driver::0", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/allure_commons/_allure.py\", line 221, in __call__\n    return self._fixture_function(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/_pytest/fixtures.py\", line 911, in _teardown_yield_fixture\n    next(it)\n  File \"/Users/<USER>/qa-ui-android-ios/src/EC/conftest.py\", line 103, in get_onboarding_driver\n    driver.quit()\n    ^^^^^^\n  File \"_pydevd_bundle/pydevd_pep_669_tracing_cython.pyx\", line 554, in _pydevd_bundle.pydevd_pep_669_tracing_cython.py_line_callback\n  File \"_pydevd_bundle/pydevd_pep_669_tracing_cython.pyx\", line 547, in _pydevd_bundle.pydevd_pep_669_tracing_cython.py_line_callback\n  File \"/Applications/PyCharm CE.app/Contents/plugins/python-ce/helpers/pydev/pydevd.py\", line 1220, in do_wait_suspend\n    self._do_wait_suspend(thread, frame, event, arg, suspend_type, from_this_thread)\n  File \"/Applications/PyCharm CE.app/Contents/plugins/python-ce/helpers/pydev/pydevd.py\", line 1235, in _do_wait_suspend\n    time.sleep(0.01)\n"}, "start": 1749112878680, "stop": 1749112984030}], "start": 1749112677315, "stop": 1749112984030}