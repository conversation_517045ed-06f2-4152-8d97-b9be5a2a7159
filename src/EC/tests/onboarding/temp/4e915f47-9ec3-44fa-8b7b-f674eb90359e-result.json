{"name": "onboarding-先购买checkout时登陆", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'click'", "trace": "self = <src.EC.tests.onboarding.test_000_onboarding.TestOnboarding object at 0x1082bcd70>\nget_onboarding_driver = <appium.webdriver.webdriver.WebDriver (session=\"f1d3d855-6d14-4315-acc8-4bd031f25863\")>\nget_platform = 'iOS', setup = None\n\n    @allure.title(\"onboarding-先购买checkout时登陆\")\n    @pytest.mark.parametrize('setup', [('test_000_onboarding_without_login', )], indirect=True)\n    def test_000_onboarding_without_login(self, get_onboarding_driver, get_platform, setup):\n        \"\"\"先加购，checkout时再登陆\"\"\"\n        d = get_onboarding_driver\n        onboarding_page = Onboarding(d, get_platform)\n        # self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get(\"switch_language\")).click()\n    \n        onboarding_page.onboarding()\n        onboarding_page.add_to_cart_on_home()\n        onboarding_page.goto_cart_and_checkout()\n>       onboarding_page.login_with_email(email='<EMAIL>', password='********')\n\ntest_000_onboarding.py:36: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../page_objects/onboarding/onboarding_page.py:120: in login_with_email\n    self.click(self.driver, self.strategy.get(\"email_next\"))\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <src.EC.page_objects.onboarding.onboarding_page.Onboarding object at 0x107a85220>\n_driver = <appium.webdriver.webdriver.WebDriver (session=\"f1d3d855-6d14-4315-acc8-4bd031f25863\")>\nele = ('-ios class chain', '**/XCUIElementTypeButton[`name == \"Next\"`]')\n\n    def click(self, _driver, ele):\n        element = self.find_element(_driver, ele)\n>       element.click()\nE       AttributeError: 'NoneType' object has no attribute 'click'\n\n../../../common/base.py:29: AttributeError"}, "description": "先加购，checkout时再登陆", "parameters": [{"name": "setup", "value": "('test_000_onboarding_without_login',)"}], "start": 1747992972132, "stop": 1747993125124, "uuid": "da6e8c64-dff1-4416-bcd8-b928b6304425", "historyId": "af3e511ef39aea9764ba3e71dbd2763f", "testCaseId": "97107ccb9079c24e48c6229d6e599e2b", "fullName": "src.EC.tests.onboarding.test_000_onboarding.TestOnboarding#test_000_onboarding_without_login", "labels": [{"name": "story", "value": "android-onboarding,先购买和后登陆和先登陆后购买2个流程"}, {"name": "tag", "value": "android_onboarding"}, {"name": "parentSuite", "value": "src.EC.tests.onboarding"}, {"name": "suite", "value": "test_000_onboarding"}, {"name": "subSuite", "value": "TestOnboarding"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "67793-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.onboarding.test_000_onboarding"}]}