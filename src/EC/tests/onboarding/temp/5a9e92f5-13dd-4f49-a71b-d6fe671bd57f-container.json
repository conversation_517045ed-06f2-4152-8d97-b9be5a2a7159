{"uuid": "e5020a99-d9d1-493c-906a-460e80a509fc", "children": ["ef062030-61b2-4210-9937-8485709b2439"], "befores": [{"name": "get_onboarding_driver", "status": "broken", "statusDetails": {"message": "selenium.common.exceptions.NoSuchElementException: Message: An element could not be located on the page using the given search parameters.; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#no-such-element-exception\nStacktrace:\nNoSuchElementError: An element could not be located on the page using the given search parameters.\n    at XCUITestDriver.doNativeFind (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:143:13)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at XCUITestDriver.findNativeElementOrElements (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:97:12)\n    at XCUITestDriver.findElOrEls (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/find.js:24:14)\n    at XCUITestDriver.findElOrElsWithProcessing (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:60:12)\n    at XCUITestDriver.findElement (/usr/local/lib/node_modules/appium/node_modules/@appium/base-driver/lib/basedriver/commands/find.ts:75:12)\n", "trace": "  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/pluggy/_callers.py\", line 77, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/_pytest/fixtures.py\", line 1123, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/_pytest/fixtures.py\", line 895, in call_fixture_func\n    fixture_result = next(generator)\n                     ^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/src/EC/conftest.py\", line 98, in get_onboarding_driver\n    get_preferences_driver()\n  File \"/Users/<USER>/qa-ui-android-ios/src/EC/conftest.py\", line 114, in get_preferences_driver\n    preferences_driver.find_element(*ele_preference_weee).click()\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/webdriver.py\", line 384, in find_element\n    return self.execute(RemoteCommand.FIND_ELEMENT, {'using': by, 'value': value})['value']\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py\", line 348, in execute\n    self.error_handler.check_response(response)\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py\", line 125, in check_response\n    raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))\n"}, "start": 1747990609723, "stop": 1747990902688}], "start": 1747990609723, "stop": 1747990902819}