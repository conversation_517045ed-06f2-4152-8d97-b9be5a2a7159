{"name": "onboarding-先登陆后购买", "status": "broken", "statusDetails": {"message": "AttributeError: 'NoneType' object has no attribute 'click'", "trace": "self = <src.EC.tests.onboarding.test_000_onboarding.TestOnboarding object at 0x1054c3b60>\nget_onboarding_driver = <appium.webdriver.webdriver.WebDriver (session=\"bc387747-1488-4c06-af72-11c9ece32579\")>\nget_platform = 'iOS', setup = None\n\n>   ???\nE   AttributeError: 'NoneType' object has no attribute 'click'\n\n/Users/<USER>/qa-ui-android/src/EC/tests/onboarding/test_000_onboarding.py:58: AttributeError"}, "description": "先登陆后加购", "parameters": [{"name": "setup", "value": "('test_001_onboarding_with_login',)"}], "start": 1748222161300, "stop": 1748222258412, "uuid": "66868169-f95d-45ed-a4f8-b1102332ac3b", "historyId": "0225758dfc921cd392b6151543cb35c1", "testCaseId": "55f5d1d59dde2a1689d477c33d45b90a", "fullName": "src.EC.tests.onboarding.test_000_onboarding.TestOnboarding#test_001_onboarding_with_login", "labels": [{"name": "story", "value": "android-onboarding,先购买和后登陆和先登陆后购买2个流程"}, {"name": "tag", "value": "android_onboarding"}, {"name": "parentSuite", "value": "src.EC.tests.onboarding"}, {"name": "suite", "value": "test_000_onboarding"}, {"name": "subSuite", "value": "TestOnboarding"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "18028-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.onboarding.test_000_onboarding"}]}