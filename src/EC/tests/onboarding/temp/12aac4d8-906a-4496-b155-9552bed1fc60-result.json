{"name": "onboarding-先登陆后购买", "status": "passed", "description": "先登陆后加购", "parameters": [{"name": "setup", "value": "('test_001_onboarding_with_login',)"}], "start": 1748228472936, "stop": 1748228662805, "uuid": "6dd907e4-7f8b-4b96-9622-3e73ebf89a10", "historyId": "0225758dfc921cd392b6151543cb35c1", "testCaseId": "55f5d1d59dde2a1689d477c33d45b90a", "fullName": "src.EC.tests.onboarding.test_000_onboarding.TestOnboarding#test_001_onboarding_with_login", "labels": [{"name": "story", "value": "android-onboarding,先购买和后登陆和先登陆后购买2个流程"}, {"name": "tag", "value": "android_onboarding"}, {"name": "parentSuite", "value": "src.EC.tests.onboarding"}, {"name": "suite", "value": "test_000_onboarding"}, {"name": "subSuite", "value": "TestOnboarding"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "45665-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.onboarding.test_000_onboarding"}]}