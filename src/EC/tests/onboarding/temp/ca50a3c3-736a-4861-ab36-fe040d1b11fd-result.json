{"name": "onboarding-先登陆后购买", "status": "broken", "statusDetails": {"message": "selenium.common.exceptions.StaleElementReferenceException: Message: The previously found element \"\"tv_checkout\" Button\" is not present in the current view anymore. Make sure the application UI has the expected state. You could also try to switch the binding strategy using the 'boundElementsByIndex' setting for the element lookup. Original error: No matches found for Identity Binding from input {(\n    <PERSON><PERSON>, {{245.0, 749.0}, {125.0, 40.0}}, identifier: 'tv_checkout', label: '    Place order    '\n)}; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\nStacktrace:\nStaleElementReferenceError: The previously found element \"\"tv_checkout\" Button\" is not present in the current view anymore. Make sure the application UI has the expected state. You could also try to switch the binding strategy using the 'boundElementsByIndex' setting for the element lookup. Original error: No matches found for Identity Binding from input {(\n    <PERSON><PERSON>, {{245.0, 749.0}, {125.0, 40.0}}, identifier: 'tv_checkout', label: '    Place order    '\n)}\n    at errorFromW3CJsonCode (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:1085:25)\n    at ProxyRequestError.getActualError (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:954:14)\n    at JWProxy.proxyReqRes (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:415:58)\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)", "trace": "self = <src.EC.tests.onboarding.test_000_onboarding.TestOnboarding object at 0x104cb4dd0>\nget_onboarding_driver = <appium.webdriver.webdriver.WebDriver (session=\"7982c91d-71d3-45d3-b6be-af81bbe6003f\")>\nget_platform = 'iOS', setup = None\n\n    @allure.title(\"onboarding-先登陆后购买\")\n    @pytest.mark.parametrize('setup', [('test_001_onboarding_with_login', )], indirect=True)\n    def test_001_onboarding_with_login(self, get_onboarding_driver, get_platform, setup):\n        \"\"\"先登陆后加购\"\"\"\n        d = get_onboarding_driver\n        onboarding_page = Onboarding(d, get_platform)\n        onboarding_page.onboarding()\n        # 登陆\n        self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get(\"home_account\")).click()\n        self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get(\"login_button\")).click()\n        onboarding_page.login_with_email(email='<EMAIL>', password='********')\n        time.sleep(5)\n        # ios no pick price page\n        pick_price_skip = self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get(\"pick_price_skip\"))\n        # 这里是动态的\n        if pick_price_skip:\n            pick_price_skip.click()\n        else:\n            self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get(\"home\")).click()\n            time.sleep(8)\n            # ios有广告，安卓不知道有没有，如果有，也要加上\n            if os.getenv(\"platform\") == 'iOS':\n                # 去除首页广告\n                adv = self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get(\"popup_close\"))\n                if adv:\n                    adv.click()\n                time.sleep(5)\n                not_now = self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get(\"not_now\"))\n                if not_now:\n                    not_now.click()\n            d.swipe(500, 500, 500, 350)\n    \n        onboarding_page.add_to_cart_on_home()\n        onboarding_page.goto_cart_and_checkout()\n>       self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get(\"place_order\")).click()\n\ntest_000_onboarding.py:77: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webelement.py:94: in click\n    self._execute(Command.CLICK_ELEMENT)\n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webelement.py:395: in _execute\n    return self._parent.execute(command, params)\n../../../../qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute\n    self.error_handler.check_response(response)\n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x104ce8ec0>\nresponse = {'status': 404, 'value': '{\"value\":{\"error\":\"stale element reference\",\"message\":\"The previously found element \\\\\"\\\\\"tv...iver/lib/jsonwp-proxy/proxy.js:415:58)\\\\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)\"}}'}\n\n    def check_response(self, response: Dict[str, Any]) -> None:\n        \"\"\"\n        https://www.w3.org/TR/webdriver/#errors\n        \"\"\"\n        payload = response.get('value', '')\n        if isinstance(payload, dict):\n            payload_dict = payload\n        else:\n            try:\n                payload_dict = json.loads(payload)\n            except (json.JSONDecodeError, TypeError):\n                return\n            if not isinstance(payload_dict, dict):\n                return\n        value = payload_dict.get('value')\n        if not isinstance(value, dict):\n            return\n        error = value.get('error')\n        if not error:\n            return\n    \n        message = value.get('message', error)\n        stacktrace = value.get('stacktrace', '')\n        # In theory, we should also be checking HTTP status codes.\n        # Java client, for example, prints a warning if the actual `error`\n        # value does not match to the response's HTTP status code.\n        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(\n            error, sel_exceptions.WebDriverException\n        )\n        if exception_class is sel_exceptions.WebDriverException and message:\n            if message == 'No such context found.':\n                exception_class = appium_exceptions.NoSuchContextException\n            elif message == 'That command could not be executed in the current context.':\n                exception_class = appium_exceptions.InvalidSwitchToTargetException\n    \n        if exception_class is sel_exceptions.UnexpectedAlertPresentException:\n            raise sel_exceptions.UnexpectedAlertPresentException(\n                msg=message,\n                stacktrace=format_stacktrace(stacktrace),\n                alert_text=value.get('data'),\n            )\n>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))\nE       selenium.common.exceptions.StaleElementReferenceException: Message: The previously found element \"\"tv_checkout\" Button\" is not present in the current view anymore. Make sure the application UI has the expected state. You could also try to switch the binding strategy using the 'boundElementsByIndex' setting for the element lookup. Original error: No matches found for Identity Binding from input {(\nE           Button, {{245.0, 749.0}, {125.0, 40.0}}, identifier: 'tv_checkout', label: '    Place order    '\nE       )}; For documentation on this error, please visit: https://www.selenium.dev/documentation/webdriver/troubleshooting/errors#stale-element-reference-exception\nE       Stacktrace:\nE       StaleElementReferenceError: The previously found element \"\"tv_checkout\" Button\" is not present in the current view anymore. Make sure the application UI has the expected state. You could also try to switch the binding strategy using the 'boundElementsByIndex' setting for the element lookup. Original error: No matches found for Identity Binding from input {(\nE           Button, {{245.0, 749.0}, {125.0, 40.0}}, identifier: 'tv_checkout', label: '    Place order    '\nE       )}\nE           at errorFromW3CJsonCode (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:1085:25)\nE           at ProxyRequestError.getActualError (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/protocol/errors.js:954:14)\nE           at JWProxy.proxyReqRes (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:415:58)\nE           at processTicksAndRejections (node:internal/process/task_queues:95:5)\n\n../../../../qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: StaleElementReferenceException"}, "description": "先登陆后加购", "parameters": [{"name": "setup", "value": "('test_001_onboarding_with_login',)"}], "start": 1748226298162, "stop": 1748226449687, "uuid": "bba4c5a1-dc28-45a3-a749-fde82b01e80f", "historyId": "0225758dfc921cd392b6151543cb35c1", "testCaseId": "55f5d1d59dde2a1689d477c33d45b90a", "fullName": "src.EC.tests.onboarding.test_000_onboarding.TestOnboarding#test_001_onboarding_with_login", "labels": [{"name": "story", "value": "android-onboarding,先购买和后登陆和先登陆后购买2个流程"}, {"name": "tag", "value": "android_onboarding"}, {"name": "parentSuite", "value": "src.EC.tests.onboarding"}, {"name": "suite", "value": "test_000_onboarding"}, {"name": "subSuite", "value": "TestOnboarding"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "31082-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.onboarding.test_000_onboarding"}]}