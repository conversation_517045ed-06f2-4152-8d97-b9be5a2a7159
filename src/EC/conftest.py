import asyncio
import copy
import json
import time
import pytest
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.tests.base_case import BaseCase
from src.api.commonapi.get_header import login_header
import os

from src.config.devices import caps_preferences_ios, caps_weee_ios
from src.config.weee.config import RunR<PERSON>ult, weeeConfig, Message
from src.config.weee.log_help import log
from src.config.weee.mysqlUtil import MysqlUtil
from src.config.weee.mysqlconnection import tb1_mysql_update
from src.config.weee.secret import get_secret
from src.config.weee.send_report import Report
from src.lib.device_manger import DeviceManager
from appium import webdriver
from appium.options.ios import XCUITestOptions
from appium.webdriver.common.appiumby import AppiumBy
from appium.webdriver.webdriver import WebDriver

strategies = {
        'Android': {
            'switch_language': (AppiumBy.ID, 'com.sayweee.weee:id/tv_language'),
            'english': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("English")'),
            'shop_now': (AppiumBy.ID, 'com.sayweee.weee:id/tv_commit'),
            'skip': (AppiumBy.ID, 'com.sayweee.weee:id/tv_skip'),
            "enter_zipcode": (AppiumBy.ID, 'com.sayweee.weee:id/et_input'),
            "zipcode_next": (AppiumBy.ID, 'com.sayweee.weee:id/tv_go'),
            "chinsese": (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Chinese")'),
            "home_add_to_cart": (AppiumBy.XPATH, '(//android.widget.ImageView[@resource-id="com.sayweee.weee:id/iv_edit_right"])'),
            "cart_button": (AppiumBy.ID,"com.sayweee.weee:id/iv_cart"),
            "checkout_button": (AppiumBy.ID, "com.sayweee.weee:id/tv_checkout"),
            "enter_your_email": (AppiumBy.ID, "com.sayweee.weee:id/et_account"),
            "email_next": (AppiumBy.ID, "com.sayweee.weee:id/tv_account_next"),
            "enter_your_password": (AppiumBy.ID, "com.sayweee.weee:id/et_password"),
            "password_next": (AppiumBy.ID, "com.sayweee.weee:id/tv_next"),
            "place_order": (AppiumBy.ID, "com.sayweee.weee:id/tv_checkout"),
            # "home_account": (AppiumBy.XPATH, '//android.widget.TextView[@resource-id="com.sayweee.weee:id/txt" and @text="Account"]'),
            "home_account": (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Account")'),
            "login_button": (AppiumBy.ID, "com.sayweee.weee:id/btn_login"),
            "pick_price_skip": (AppiumBy.ACCESSIBILITY_ID, "Skip"),
            # 首页home button
            "home": (AppiumBy.XPATH, '//android.widget.TextView[@resource-id="com.sayweee.weee:id/txt" and @text="Home"]'),

        },
        'iOS': {
            'switch_language': (AppiumBy.XPATH,
                                '//XCUIElementTypeWindow/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeImage[2]'),
            'english': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "English"`]'),
            'shop_now': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Shop now"`]'),
            'skip': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Skip"`]'),
            "enter_zipcode": (AppiumBy.IOS_PREDICATE, 'value == "Enter your zip code"'),
            "zipcode_next": (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Next"`]'),
            "chinsese": (AppiumBy.ID, 'Chinese'),
            "not_now": (),
            "home_add_to_cart": (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "CartAction add"`]'),
            "cart_button": (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeWindow[1]/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[2]/XCUIElementTypeOther/XCUIElementTypeButton[2]/XCUIElementTypeStaticText[1]'),
            "checkout_button": (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "    Checkout    "`]'),
            "enter_your_email": (AppiumBy.IOS_PREDICATE, 'value == "Enter your email"'),
            "email_next": (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]'),
            "enter_your_password": (AppiumBy.CLASS_NAME, 'XCUIElementTypeSecureTextField'),
            "password_next": (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Next"`]'),
            "place_order": (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "    Place order    "`]'),
            "home_account": (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Account"`]'),
            "login_button": (AppiumBy.ACCESSIBILITY_ID, 'Log In or Sign Up'),
            # ios没有这个页面
            "pick_price_skip": (AppiumBy.ID, ''),
            # 首页home button
            "home": (AppiumBy.ACCESSIBILITY_ID, 'Home'),
            # account button
            "account_tab": (AppiumBy.ACCESSIBILITY_ID, 'enki-account'),
            "sign_in_or_login": (AppiumBy.ACCESSIBILITY_ID, 'Log In or Sign Up')
        }
    }

from dotenv import load_dotenv
load_dotenv()

@pytest.fixture(scope='session')
def android_header():
    header = login_header(email='<EMAIL>', password='********')
    if header.get('authorization'):
        return header
    else:
        raise Exception(f"get autotest token failed: header={header}")


@pytest.fixture(scope="function")
def get_onboarding_driver():
    driver = DeviceManager(os.getenv("platform", "Android")).initialize_driver()
    # iphone比较复杂，需要特殊处理
    if os.getenv('platform') == 'iOS':
        driver.quit()
        get_preferences_driver()
        driver = get_installed_ios_driver()
        time.sleep(3)
    yield driver
    log.info("test finished, quit driver")
    driver.quit()
    # driver.quit()

def get_preferences_driver(platform='ios'):
    preferences_driver = webdriver.Remote("http://127.0.0.1:4723",
                                          options=XCUITestOptions().load_capabilities(
                                              caps_preferences_ios))

    ele_cellular = (AppiumBy.ACCESSIBILITY_ID, 'com.apple.settings.cellular')
    ele_preference_weee = (AppiumBy.ACCESSIBILITY_ID, 'Weee!, Off')
    # ele_preference_weee_data = (AppiumBy.ACCESSIBILITY_ID, 'com.differentsocial.Weee.wireless')
    ele_wlan_cellular = (AppiumBy.ACCESSIBILITY_ID, 'WLAN & Cellular Data')
    preferences_driver.find_element(*ele_cellular).click()
    preferences_driver.find_element(*ele_preference_weee).click()
    # preferences_driver.find_element(*ele_preference_weee_data).click()
    preferences_driver.find_element(*ele_wlan_cellular).click()
    time.sleep(2)
    preferences_driver.quit()
    time.sleep(3)

def get_installed_ios_driver(platform='ios'):
    weee_driver = webdriver.Remote("http://127.0.0.1:4723",
                                   options=XCUITestOptions().load_capabilities(
                                       caps_weee_ios)) if platform == 'ios' else None
    return weee_driver

@pytest.fixture(scope="session")
def get_driver():
    f = os.getenv("platform", "Android")
    driver = DeviceManager(os.getenv("platform", "Android")).initialize_driver(is_installed=True)
    strategy = strategies.get(os.getenv("platform", "Android"))
    try:
        account = driver.find_element(*strategy.get("account_tab")).is_enabled()
    except Exception as e:
        account = None
    if account:
        log.info("already logged in")
    else:
        login(driver, "english", "<EMAIL>", "********", strategy)


    yield driver
    log.info("test finished, quit driver")
    driver.quit()

def login(_driver, lang, email, password, _strategy):
    time.sleep(5)
    if os.getenv("platform", 'Android') == 'Android':
        _driver.find_element(*_strategy.get('switch_language')).click()
        _driver.find_element(*_strategy.get(lang)).click()
    _driver.find_element(*_strategy.get('shop_now')).click()
    time.sleep(3)
    _driver.find_element(*_strategy.get('skip')).click()
    time.sleep(3)
    _driver.find_element(*_strategy.get('enter_zipcode')).click()
    _driver.find_element(*_strategy.get('enter_zipcode')).send_keys('98011')
    _driver.find_element(*_strategy.get('zipcode_next')).click()
    time.sleep(10)
    _driver.find_element(*_strategy.get("chinsese")).click()
    time.sleep(30)
    _driver.find_element(*_strategy.get("home_account")).click()
    time.sleep(5)
    _driver.find_element(*_strategy.get("login_button")).click()
    time.sleep(5)
    _driver.tap([(500, 500)])
    time.sleep(5)
    # _driver.find_element(*_strategy.get("enter_your_email")).click()
    _driver.find_element(*_strategy.get("enter_your_email")).send_keys(email)
    _driver.find_element(*_strategy.get("email_next")).click()
    time.sleep(5)
    _driver.tap([(500, 500)])
    _driver.tap([(500, 500)])
    _driver.find_element(*_strategy.get("enter_your_password")).send_keys(password)
    _driver.find_element(*_strategy.get("password_next")).click()
    time.sleep(5)
    if os.getenv("platform", 'Android') == 'Android':
        if BaseCase().find_element(_driver=_driver, ele=_strategy.get("pick_price_skip")):
            BaseCase().find_element(_driver=_driver, ele=_strategy.get("pick_price_skip")).click()

@pytest.fixture(scope="function")
def get_platform():
    print("platform===>", os.getenv("platform", "Android"))
    return os.getenv("platform", "Android")

def pytest_addoption(parser):
    # add multiple self define params when need
    parser.addoption("--cmdopt", action="store", default=None, help="self define cmd parameters for pytest")


CASE_DETAIL = []  # 用例详情
# 每个用例的执行结果
EVERY_CASES_RESULT = []
# 存入数据库的结果数据
DB_EVERY_CASES_RESULT = []

def get_decorators(func):
    # 获取测试用例函数的装饰器列表
    return getattr(func, "__decorators__", [])


def get_decorators_kv(func):
    # 获取测试用例函数的装饰器列表
    return getattr(func, "__decorators_kv__", {})


def get_case_type(func, case_name: str):
    case_type = func.__annotations__.get("case_type")

    if case_type:
        return case_type
    if "simple" in case_name.split("::")[0].lower():
        return "simple"
    else:
        return "scene"


# def pytest_collection_modifyitems(config, items):
#     new_items = []
#
#     # 遍历测试用例列表
#     for i in range(len(items) - 1, -1, -1):
#         item = items[i]
#         dic = get_decorators_kv(item.function)
#         if dic.get("all"):
#             new_items = items
#         else:
#
#             # 获取测试用例函数的装饰器信息
#             decorators = get_decorators(item.function)
#
#             # 打印装饰器信息的测试用例
#             if decorators:
#                 for decorator in decorators:
#                     for item in items:
#                         if decorator in item.originalname:
#                             new_items.append(item)
#                         elif decorator == item.nodeid.split("::")[1]:
#                             #  获取class下的方法
#                             new_items.append(item)
#                         # 检查测试用例是否满足条件
#                         elif item.location[0].replace('\\', '/') in item.nodeid.split("::")[0]:
#                             new_items.append(item)
#             else:
#                 if item.location[0].replace('\\', '/') in item.nodeid.split("::")[0]:
#                     new_items.append(item)
#
#         # 去重
#     new_items = list(set(new_items))
#
#     items[:] = new_items
#
#     # 重新设置测试用例列表的长度
#     config.pluginmanager.get_plugin("terminalreporter")._numcollected = len(items)


# def pytest_addoption(parser):
#     mygroup = parser.getgroup("reportgroup")  # group将下面所有的 optiongroup都展示在这个group下。
#     mygroup.addoption("--report",  # 注册一个命令行选项
#                       action="store",  # 存储一个值
#                       default=None,  # 参数的默认值
#                       help='Path to the test report file'  # 生成html的测试报告
#                       )
#
#
# def pytest_configure(config):
#     config.report_value = config.getoption("--report")
#
#
# def pytest_configure(config):
#     # 获取测试用例目录路径
#     test_dir_path = config.rootdir
#     weeeConfig.project_root_dir = test_dir_path
#     # if not os.path.basename(test_dir_path).startswith("test"):
#     #     raise ValueError("测试用例目录必须以'test'开头！")
#
#
# # register_assert_rewrite('tests.utils')


@pytest.hookimpl(hookwrapper=True, tryfirst=True)
def pytest_runtest_makereport(item, call):
    """
    用例执行结果 hook 函数
    :param item:
    :param call:
    :return:
    """
    result = yield
    report = result.get_result()
    _case_nodeid = item.nodeid  # 获取用例函数的名称

    step_status = 0  # 0：成功，1：失败，2：错误，3：跳过
    out_message = ''  # 输出信息
    error_message = ''  # 错误信息
    step_desc = ''  # 步骤描述
    case_name = item.nodeid  # 获取用例函数的名称
    dev_case_name = item.originalname  # 获取用例函数的名称
    RunResult.CURRENT_CASE_NAME = item.originalname
    desc = '' if item.function.__doc__ is None else item.function.__doc__  # 获取用例函数的名称的文档
    run_time = round(report.duration, 4)  # 获取用例setup执行时间

    if report.when == 'setup':
        step_name = "SetUp"
        if report.outcome == 'passed':
            step_status = 0  # 0：成功，1：失败，2：错误，3：跳过

        elif report.outcome == 'failed':
            step_status = 2  # 0：成功，1：失败，2：错误，3：跳过
            out_message = str(call.excinfo.value)  # 获取用例执行失败的输出信息
            error_message = report.longreprtext  # 获取用例执行失败的错误信息
            log.error('\n异常信息: {}'.format(call.excinfo.value))
            log.error('\n详细异常错误定位: {}'.format(report.longreprtext))
        elif report.outcome == 'skipped':
            step_status = 3  # 0：成功，1：失败，2：错误，3：跳过

        # 测试开发报告详情
        CASE_DETAIL.append(
            {"case_name": case_name, "step_name": step_name, "step_desc": step_desc, "status": step_status,
             "run_time": run_time, "out_message": out_message, "error_message": error_message})

    elif report.when == 'call':
        step_name = "Call"
        if report.outcome == 'passed':
            step_status = 0  # 0：成功，1：失败，2：错误，3：跳过
        elif report.outcome == 'failed':
            step_status = 1  # 0：成功，1：失败，2：错误，3：跳过
            out_message = str(call.excinfo.value)  # 获取用例执行失败的输出信息
            error_message = report.longreprtext  # 获取用例执行失败的错误信息
            log.error('\n用例异常信息：{}'.format(call.excinfo.value))
            log.error('\n详细异常错误定位：{}'.format(report.longreprtext))
        # 测试开发报告详情
        CASE_DETAIL.append(
            {"case_name": case_name, "step_name": step_name, "step_desc": step_desc, "status": step_status,
             "run_time": run_time, "out_message": out_message, "error_message": error_message})


    elif report.when == 'teardown':
        step_name = "TearDown"
        if report.outcome == 'passed':
            step_status = 0  # 0：成功，1：失败，2：错误，3：跳过
        elif report.outcome == 'failed':
            step_status = 2  # 0：成功，1：失败，2：错误，3：跳过
            out_message = str(call.excinfo.value)  # 获取用例执行失败的输出信息
            error_message = report.longreprtext  # 获取用例执行失败的错误信息
            log.error('\n后置条件异常: {}'.format(call.excinfo.value))
            log.error('\n详细异常错误定位: {}'.format(report.longreprtext))
        elif report.outcome == 'skipped':
            step_status = 3  # 0：成功，1：失败，2：错误，3：跳过

        # 测试报告详情
        CASE_DETAIL.append(
            {"case_name": case_name, "step_name": step_name, "step_desc": step_desc, "status": step_status,
             "run_time": run_time, "out_message": out_message, "error_message": error_message})

        # ************************* >> 用例执行结果处理  << *******************************
        # ************************* >> 测试人员报告数据处理 start << **************************
        case_details = []  # 用例详情列表
        case_status = 0  # 0：成功，1：失败，2：错误，3：跳过
        run_time_sum = 0  # 用例执行时间总和

        for i in CASE_DETAIL:
            if i['case_name'] == case_name:
                case_details.append(i)  # 将用例详情添加到用例详情列表

        for i in case_details:  # 遍历用例详情列表
            run_time_sum += i['run_time']  # 获取用例执行时间总和
            if i['status'] == 2:  # 如果用例详情列表中的用例状态为2，即用例错误
                case_status = 2
                break
            elif i['status'] == 1:  # 如果用例详情列表中的用例状态为1，即用例失败
                case_status = 1
                break
            elif i['status'] == 3:  # 如果用例详情列表中的用例状态为3，即用例跳过
                case_status = 3
                break
            else:  # 如果用例详情列表中的用例状态为3，即用例跳过
                num = 0
                num += 1

        # case_type = item.function.__annotations__.get("case_type")
        case_type = get_case_type(func=item.function, case_name=item.nodeid)
        # 测试报告每个用例结果
        EVERY_CASES_RESULT.append(
            {"case_name": case_name, "desc": desc, "status": case_status, "run_time": round(run_time_sum, 4),
             "case_detail": case_details, "case_type": case_type})  # 每个用例的结果

        DB_EVERY_CASES_RESULT.append(
            {"case_name": case_name, "desc": desc, "status": case_status, "run_time": round(run_time_sum, 4),
             "case_detail": [i for i in case_details if i['step_name'] == 'Call'], "case_type": case_type,
             "case_url": "item.instance.response_all.request.path_url",
             "case_response_status": "item.instance.response_all.status_code", "case_node_id": item.nodeid})

        res = copy.deepcopy(EVERY_CASES_RESULT)
        # case_desc加入case_detail
        [j.update({"case_desc": r["desc"]}) for r in res for j in r['case_detail']]
        # 将执行成功的用例过滤掉，邮件中只展示错误的用例
        case_detail = [j for item in res for j in item["case_detail"] if j["status"] != 0 and j["step_name"] == "Call"]
        # 将run_time和error_message置空，否则邮件中错误用例会显示2次（rerun=1）
        [item.update({"run_time": "", "error_message": ""}) for item in case_detail]
        # 将同名用例合并，只显示一条
        new_list = [dict(d) for d in (set([tuple(d.items()) for d in case_detail]))]
        # 用例名太长，截取最后一部分作为用例名
        [item.update({"case_name": item["case_name"].split("::")[-1]}) for item in new_list]
        # RunResult.EVERY_CASE_RES中保存的是错误用例的信息
        RunResult.EVERY_CASE_RES = new_list

        #############################根据模块分组############################
        # all_case_json_string = copy.deepcopy(EVERY_CASES_RESULT)
        # new_case_json_dict = []
        # for item in all_case_json_string:
        #     case_desc = item["desc"].replace("\n", "")
        #     case_module = item["case_name"].split("/")[4]
        #     case_status = item["status"]
        #     new_case_json_dict.append(
        #         {
        #             "case_desc": case_desc,
        #             "case_module": case_module,
        #             "case_status": case_status
        #         }
        #     )
        #
        # # 根据module分组
        # new_list_sort = sorted(new_case_json_dict, key=lambda x: (x["case_module"], x["case_status"]))
        # new_list_group = itertools.groupby(new_list_sort, key=lambda x: (x["case_module"]))
        # final_data_list = []
        # for key, gro in new_list_group:
        #     j = list(gro)
        #     final_data = {
        #         "module": key,
        #         "success": len([i for i in j if i["case_status"] == 0]),
        #         'fail': len([i for i in j if i["case_status"] == 1]),
        #         'error': len([i for i in j if i["case_status"] == 2]),
        #         'skip': len([i for i in j if i["case_status"] == 3]),
        #     }
        #     final_data_list.append(final_data)
        # print("final_data_list===>", final_data_list)
        # RunResult.GROUP_BY_RESULT = final_data_list

        # ******************** >> 测试人员报告数据处理 end  << **************************

        # ******************** >> 开发数据处理 start << **************************

        dev_case_details = []  # 用例详情列表

        dev_out_message = ''  # 开发者输出信息
        dev_error_message = ''  # 开发者错误信息

        # 开发报告详情
        for i in RunResult.DEV_EVERY_CASES_RES:  # 遍历用例详情列表
            if i['case_name'] == dev_case_name:
                dev_case_details.append(i)  # 将用例详情添加到用例详情列表
        for i in CASE_DETAIL:
            if i['case_name'] == case_name:
                if i['status'] == 0:  # 如果用例详情列表中的用例状态为0，即用例成功
                    # 将数据添加到dev测试报告数据列表中
                    if len(i['out_message']) > 0:
                        dev_out_message = '\n' + i['step_name'] + ':\n' + i['out_message']

                if i['status'] == 2:  # 如果用例详情列表中的用例状态为2，即用例错误
                    # 将数据添加到dev测试报告数据列表中
                    if len(i['error_message']) > 0:
                        dev_out_message = '\n' + i['step_name'] + ':\n' + i['out_message']
                        dev_error_message = '\n' + i['step_name'] + ':\n' + i['error_message']
                elif i['status'] == 1:  # 如果用例详情列表中的用例状态为1，即用例失败
                    # 将数据添加到dev测试报告数据列表中
                    if len(i['error_message']) > 0:
                        dev_out_message = '\n' + i['step_name'] + ':\n' + i['out_message']
                        dev_error_message = '\n' + i['step_name'] + ':\n' + i['error_message']
                elif i['status'] == 3:  # 如果用例详情列表中的用例状态为3，即用例跳过
                    # 将数据添加到dev测试报告数据列表中
                    if len(i['out_message']) > 0:
                        dev_out_message = '\n' + i['step_name'] + ':\n' + i['out_message']

        # 开发测试报告每个用例结果
        RunResult.DEV_EVERY_CASES_RESULT.append(
            {"case_name": dev_case_name, "desc": desc, "status": case_status, "run_time": round(run_time_sum, 4),
             "case_detail": dev_case_details, "out_message": dev_out_message,
             "error_message": dev_error_message})  # 每个用例的结果

        # print("DB_EVERY_CASES_RESULT====>", DB_EVERY_CASES_RESULT)
        # print("EVERY_CASES_RESULT====>", EVERY_CASES_RESULT)

        # ******************** >> 开发报告数据处理 end << **************************


def pytest_terminal_summary(terminalreporter, config):
    """
    收集测试结果
    """
    pytest_total_num = terminalreporter._numcollected
    pass_num = len(terminalreporter.stats.get('passed', []))  # 用例通过数
    fail_num = len(terminalreporter.stats.get('failed', []))  # 用例失败数
    error_num = len(terminalreporter.stats.get('error', []))  # 用例错误数
    skip_num = len(terminalreporter.stats.get('skipped', []))  # 用例跳过数
    RunResult.end_time = time.strftime("%Y-%m-%d %H:%M:%S")  # 测试结束时间
    RunResult.duration = time.strftime("%H:%M:%S",
                                       time.gmtime(time.time() - terminalreporter._sessionstarttime))  # 测试耗时转换成时分秒
    RunResult.passed = pass_num  # 用例通过数
    RunResult.failed = fail_num  # 用例失败数
    RunResult.errors = error_num  # 用例错误数
    RunResult.skipped = skip_num  # 用例跳过数
    total_num = pass_num + fail_num + error_num + skip_num  # 用例总数
    if total_num != 0:
        RunResult.pass_rate = str(round(pass_num / total_num * 100, 2)) + '%'  # 用例通过率
        RunResult.skip_rate = str(round(skip_num / total_num * 100, 2)) + '%'  # 用例跳过率
        RunResult.failure_rate = str(round(fail_num / total_num * 100, 2)) + '%'  # 用例失败率
        RunResult.error_rate = str(round(error_num / total_num * 100, 2)) + '%'  # 用例错误率
    else:
        RunResult.pass_rate = '0.00%'
        RunResult.skip_rate = '0.00%'
        RunResult.failure_rate = '0.00%'
        RunResult.error_rate = '0.00%'

    print("terminal.stats.passed", terminalreporter.stats.get('passed', []))
    print("terminal.stats.failed", terminalreporter.stats.get('failed', []))

    def get_base_data():
        return {
            "title": weeeConfig.report_title,
            "start_time": RunResult.start_time,
            "end_time": RunResult.end_time,
            "duration": RunResult.duration,
            "tester": weeeConfig.report_tester,
            "description": weeeConfig.report_description
        }

    def get_total_data():
        return {
            "pass_num": RunResult.passed,
            "pass_percent": RunResult.pass_rate,
            "fail_num": RunResult.failed,
            "fail_percent": RunResult.failure_rate,
            "error_num": RunResult.errors,
            "error_percent": RunResult.error_rate,
            "skip_num": RunResult.skipped,
            "skip_percent": RunResult.skip_rate,
        }

    # Jenkins 参加合并报告汇总数据
    platform = os.getenv("upstream_env", None)
    upstream_branch = os.getenv("upstream_branch", None)
    upstream_build_number = os.getenv("upstream_build_number", None)
    if upstream_branch is not None and upstream_build_number is not None:
        version = upstream_branch + '[' + upstream_build_number + ']'
    else:
        version = None
    app = os.getenv("upstream_app", None)

    CASE_RESULT = {
        "base": {
            **get_base_data(),
            "platform": platform,
            "version": version,
            "app": app,
        },
        "total": get_total_data(),
    }

    # ============================= 将每个用例的信息保存至数据库，方便统计, 开始 ============================
    # 到jenkins上不使用明文的用户名和密码，使用get_secret()获得
    db_config = get_secret()

    if os.getenv("JOB_NAME", None) == 'iOS-UI-automation':
        with MysqlUtil(host='weee.db.tb1.sayweee.net', user=db_config['db_erp_username'],
                       password=db_config['db_erp_password'], db='autotest_result') as conn:
            for i in DB_EVERY_CASES_RESULT:
                # out_message = "null" if i['case_detail'][0]['out_message'] == '' or i['status'] == 3 else i['case_detail'][0][
                #     'out_message'].replace("'", "")
                try:
                    if len(i['case_detail']) == 0:
                        out_message = "null"
                    elif i['case_detail'][0]['out_message'] == '':
                        out_message = "null"
                    else:
                        out_message = i['case_detail'][0]['out_message'].replace("'", "")
                        # error message太长且格式错乱，所以不存库
                        error_msg = {i['case_detail'][0]['error_message']}

                    sql = f""" insert into autotest_result.case_stat (allure_epic, allure_feature, allure_story, allure_severity,allure_title,
                            allure_description, allure_tage, case_file_path, case_class_name, case_fuction_name, case_parameter,
                            case_url, case_header, case_request, case_request_time, case_response_status, case_response, case_desc,
                            case_status, case_type, case_dev_user, case_review_user, case_run_time, case_out_message, 
                            case_error_message, create_time, pytest_case_nodeid, pytest_step_name, build_num) values 
                            ('', '', '', '', '', '', '', '{i['case_name'].split('::')[0]}', '{i['case_name'].split('::')[1].replace("'", "")}', '{i['case_name'].split('::')[2].replace("'", "")}', '', 
                            '{i['case_url']}', '', '', null, '{i['case_response_status']}', '', '{i['desc'].replace("'", "")}', {i['status']}, '{i['case_type']}', '', 
                            '', {i['run_time']}, '{out_message}', '', null, '{i['case_node_id'].replace("'", "")}',
                            'Call', {os.getenv("BUILD_NUMBER", 0)}) """
                    print("sql===>", sql)
                    asyncio.run(tb1_mysql_update(sql=sql, conn=conn))
                except Exception as e:
                    log.info(f"此条记录存储数据库case_stat表失败,记录为：{json.dumps(i)} " + str(e))

        with MysqlUtil(host='weee.db.tb1.sayweee.net', user=db_config['db_erp_username'],
                       password=db_config['db_erp_password'], db='autotest_result') as connection:
            try:
                jenkins_sql = f""" 
                           insert into autotest_result.jenkins_stat (build_name, build_number, build_user, build_email, build_version, build_env, build_branch, total_num, pass_num, failed_num, skiped_num, create_time, pass_rate, failed_rate, skip_rate,allure,project_name, false_alarm_num, real_alarm_num) values 
                           ('{weeeConfig.report_title}', {os.getenv("BUILD_NUMBER", 0)}, '{weeeConfig.report_tester}', '', '', '', '{upstream_branch}', '{total_num}', '{pass_num}', '{fail_num}', '{skip_num}', '{RunResult.start_time}', '{RunResult.pass_rate}', '{RunResult.failure_rate}', '{RunResult.skip_rate}', 'http://autotest.sayweee.net:9090/job/{os.getenv("JOB_NAME", None)}/{os.getenv("BUILD_NUMBER", 0)}/allure','iOS-UI-automation', 0, 0)
                           """
                print("jenkins sql===>", jenkins_sql)
                asyncio.run(tb1_mysql_update(sql=jenkins_sql, conn=connection))
            except Exception as e:
                log.info(f"此条记录存储数据库jenkins_stat表失败,sql为：{jenkins_sql} " + str(e))
    else:
        log.info("此次运行不是IOS-UI-automation工程或为本地运行")

    # ============================= 将每个用例的信息保存至数据库，结束 ============================

    # 测试报告测试
    case_re = json.dumps(CASE_RESULT, ensure_ascii=False, indent=4)  # 用例结果转换成json格式
    case_details = json.dumps(EVERY_CASES_RESULT, ensure_ascii=False, indent=4)  # 用例详情转换成json格式
    # 测试报告处理
    dev_case_details = json.dumps(RunResult.DEV_EVERY_CASES_RESULT, ensure_ascii=False, indent=4)  # 用例详情转换成json格式

    report = Report(weeeConfig.report_path)  # 实例化Report类
    # 开发报告日志
    log.debug('weeeTest插件获取测试报告路径: {}'.format(weeeConfig.report_path))
    log.debug('weeeTest插件执行结果汇总json数据: {}'.format(case_re))
    log.debug('weeeTest插件执行结果详情json数据: {}'.format(case_details))
    # 开发报告详情日志
    log.debug('weeeTest插件执行结果详情dev的json数据: {}'.format(dev_case_details))

    # 提示信息
    if pytest_total_num != total_num:
        log.info(
            f'注意：weeeTest获取到用例数量为：{pytest_total_num}个, 实际执行的用例数量为：{total_num}个不一致, 请检查用例是否全部被执行。')

    if error_num > 0:
        log.info(f'注意：weeeTest执行错误用例数量为：{error_num}个, 请检查用例代码是否有错.')

    if fail_num > 0:
        log.error(f'注意：weeeTest执行断言用例数量为：{fail_num}个, 请检查断言是否有误.')

    report.generate_report(case_re, case_details)  # 测试开发测试报告生成
    report.generate_report_dev(case_re, dev_case_details)  # 开发测试报告生成

    print("build number===>", os.getenv("BUILD_NUMBER", None))
    print("build user===>", os.getenv("BUILD_USER"))
    print("build job name===>", os.getenv("JOB_NAME"))
    print("mark===>", os.getenv("mark"))
    print("to===>", os.getenv("to"))
    print("cc===>", os.getenv("cc"))
    if os.getenv("BUILD_NUMBER", None):
        # if Message.to is not None and len(Message.to) > 0:
        if os.getenv("to", None) is not None and len(os.getenv("to", None)) > 0:
            # report.send(send_type='email', to=Message.to)
            try:
                print("======= mail is sending =======")
                report.send(send_type='email', to=os.getenv("to"), cc=os.getenv("cc"))
            except Exception as e:
                print("send email with os.getenv failed " + str(e))
        else:
            try:
                print("======= mail is sending =======")
                report.send(send_type='email', to=Message.to, cc=Message.cc)
            except Exception as e:
                print("send email with Message failed " + str(e))


@pytest.fixture(scope="session", autouse=True)
def user_setup():
    RunResult.start_time = time.strftime("%Y-%m-%d %H:%M:%S")  # 测试开始时间
    yield
