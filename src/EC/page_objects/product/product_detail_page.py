import time
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.page_objects.base_page import BasePage
from src.config.weee.log_help import log


class ProductDetailPage(BasePage):
    strategies = {
        'Android': {
            # 产品详情页元素
            'product_title': (AppiumBy.ID, 'com.sayweee.weee:id/tv_product_name'),
            'product_price': (AppiumBy.ID, 'com.sayweee.weee:id/tv_price'),
            'add_to_cart_button': (AppiumBy.ID, 'com.sayweee.weee:id/iv_edit_right'),
            
            # 视频相关元素
            'videos_section': (AppiumBy.XPATH, '//android.widget.TextView[@text="Videos"]'),
            'videos_count': (AppiumBy.XPATH, '//android.widget.TextView[contains(@text, "Videos")]//following-sibling::android.widget.TextView'),
            'see_all_videos': (AppiumBy.XPATH, '//android.widget.TextView[@text="See all"]'),
            
            # 视频分类
            'review_videos': (AppiumBy.XPATH, '//android.widget.TextView[@text="Review"]'),
            'recipes_videos': (AppiumBy.XPATH, '//android.widget.TextView[@text="Recipes"]'),
            'unboxing_videos': (AppiumBy.XPATH, '//android.widget.TextView[@text="Unboxing"]'),
            
            # 发布视频入口
            'publish_video_tip': (AppiumBy.XPATH, '//android.widget.TextView[contains(@text, "Share your experience")]'),
            'publish_video_button': (AppiumBy.ID, 'com.sayweee.weee:id/btn_publish_video'),
            
            # 返回按钮
            'back_button': (AppiumBy.XPATH, '//android.widget.ImageButton[@content-desc="Navigate up"]'),
        },
        'iOS': {
            # 产品详情页元素
            'product_title': (AppiumBy.IOS_PREDICATE, 'type == "XCUIElementTypeStaticText" AND name CONTAINS "product_name"'),
            'product_price': (AppiumBy.IOS_PREDICATE, 'type == "XCUIElementTypeStaticText" AND name CONTAINS "product_price"'),
            'add_to_cart_button': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "CartAction add"`]'),
            
            # 视频相关元素
            'videos_section': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeImage[`name == "product_video_back"`]'),
            'videos_count': (AppiumBy.XPATH, '//XCUIElementTypeStaticText[contains(@name, "(")]'),
            'see_all_videos': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "See all"`]'),
            
            # 视频分类
            'review_videos': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Reviews"`][2]'),
            'recipes_videos': (AppiumBy.ACCESSIBILITY_ID, 'Recipes'),
            'unboxing_videos': (AppiumBy.ACCESSIBILITY_ID, 'Unboxing'),
            
            # 发布视频入口
            'publish_video_tip': (AppiumBy.IOS_PREDICATE, 'type == "XCUIElementTypeStaticText" AND name CONTAINS "Share your experience"'),
            'publish_video_button': (AppiumBy.ACCESSIBILITY_ID, 'Publish Video'),
            
            # 返回按钮
            'back_button': (AppiumBy.ACCESSIBILITY_ID, 'Back'),
        }
    }

    def __init__(self, driver, platform):
        self.platform = platform
        self.driver = driver
        self.strategy = self.strategies.get(self.platform)
    
    def get_videos_count(self):
        """获取视频数量"""
        try:
            videos_count_element = self.find_element(self.driver, self.strategy.get('videos_count'))
            if videos_count_element:
                # 提取数字部分
                count_text = videos_count_element.text.strip('()')
                # 假设格式为 "(5)" 或 "5"
                return int(count_text) if count_text else 0
            return 0
        except Exception as e:
            log.error(f"Failed to get videos count: {str(e)}")
            return 0
    
    def check_videos_section_exists(self):
        """检查视频部分是否存在"""
        # 需要向下滑动，否则看不见视频
        self.swipe_screen(_driver=self.driver)
        try:
            videos_section = self.find_elements(self.driver, self.strategy.get('videos_section'))
            return videos_section is not None and len(videos_section) > 0
        except Exception as e:
            log.error(f"Failed to check videos section: {str(e)}")
            return False
    
    def click_see_all_videos(self):
        """点击查看所有视频"""
        try:
            self.click(self.driver, self.strategy.get('see_all_videos'))
            log.info("Clicked on See all videos")
            time.sleep(3)  # 等待页面加载
            return True
        except Exception as e:
            log.error(f"Failed to click see all videos: {str(e)}")
            return False
    
    def check_video_categories_order(self):
        """检查视频分类的顺序"""
        try:
            # 获取各分类的元素
            review = self.find_element(self.driver, self.strategy.get('review_videos'))
            recipes = self.find_element(self.driver, self.strategy.get('recipes_videos'))
            unboxing = self.find_element(self.driver, self.strategy.get('unboxing_videos'))
            
            if not (review and recipes and unboxing):
                log.error("One or more video categories not found")
                return False
            else:
                return True

        except Exception as e:
            log.error(f"Failed to check video categories order: {str(e)}")
            return False
    


