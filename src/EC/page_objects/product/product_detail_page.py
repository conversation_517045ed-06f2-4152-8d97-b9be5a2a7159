import time
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.page_objects.base_page import BasePage
from src.config.weee.log_help import log


class ProductDetailPage(BasePage):
    strategies = {
        'Android': {
            # 产品详情页元素
            'product_title': (AppiumBy.ID, 'com.sayweee.weee:id/tv_product_name'),
            'product_price': (AppiumBy.ID, 'com.sayweee.weee:id/tv_price'),
            'add_to_cart_button': (AppiumBy.ID, 'com.sayweee.weee:id/iv_edit_right'),

            # 视频相关元素
            'time_banner': (AppiumBy.ID, 'com.sayweee.weee:id/iv_cancel'),
            'videos_section': (AppiumBy.ID, 'com.sayweee.weee:id/layout_posts_video'),
            'videos_count': (AppiumBy.ID, 'com.sayweee.weee:id/tv_add_post_num'),
            'see_all_videos': (AppiumBy.ID, 'com.sayweee.weee:id/tv_add_post'),

            # 视频分类
            'review_videos': (AppiumBy.XPATH, '//android.widget.TextView[@text="Review"]'),
            'recipes_videos': (AppiumBy.XPATH, '//android.widget.TextView[@text="Recipes"]'),
            'unboxing_videos': (AppiumBy.XPATH, '//android.widget.TextView[@text="Unboxing"]'),

            # 发布视频入口
            'publish_video_tip': (AppiumBy.XPATH, '//android.widget.TextView[contains(@text, "Share your experience")]'),
            'publish_video_button': (AppiumBy.ID, 'com.sayweee.weee:id/btn_publish_video'),

            # 返回按钮
            'back_button': (AppiumBy.XPATH, '//android.widget.ImageButton[@content-desc="Navigate up"]'),

            # Reviews栏相关元素
            'reviews_section': (AppiumBy.ID, 'com.sayweee.weee:id/layout_post'),
            'total_reviews_count': (AppiumBy.ID, 'com.sayweee.weee:id/tv_post_num'),
            'customers_say_section': (AppiumBy.XPATH, '//android.widget.TextView[@text="Customers say"]'),
            'ai_generated_text': (AppiumBy.XPATH, '//android.widget.TextView[contains(@text, "AI-generated from the text of customer reviews")]'),

            # 卖点关键词相关元素
            'selling_points_container': (AppiumBy.XPATH, '//android.widget.LinearLayout[contains(@resource-id, "selling_points")]'),
            'selling_point_keywords': (AppiumBy.XPATH, '//android.widget.TextView[contains(@resource-id, "keyword")]'),
            'selling_point_keyword_item': (AppiumBy.XPATH, '//android.widget.TextView[contains(@resource-id, "keyword_item")]'),
        },
        'iOS': {
            # 产品详情页元素
            'product_title': (AppiumBy.IOS_PREDICATE, 'type == "XCUIElementTypeStaticText" AND name CONTAINS "product_name"'),
            'product_price': (AppiumBy.IOS_PREDICATE, 'type == "XCUIElementTypeStaticText" AND name CONTAINS "product_price"'),
            'add_to_cart_button': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "CartAction add"`]'),

            # 视频相关元素
            'videos_section': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeImage[`name == "product_video_back"`]'),
            'videos_count': (AppiumBy.XPATH, '//XCUIElementTypeStaticText[contains(@name, "(")]'),
            'see_all_videos': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "See all"`]'),

            # 视频分类
            'review_videos': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Reviews"`][2]'),
            'recipes_videos': (AppiumBy.ACCESSIBILITY_ID, 'Recipes'),
            'unboxing_videos': (AppiumBy.ACCESSIBILITY_ID, 'Unboxing'),

            # 发布视频入口
            'publish_video_tip': (AppiumBy.IOS_PREDICATE, 'type == "XCUIElementTypeStaticText" AND name CONTAINS "Share your experience"'),
            'publish_video_button': (AppiumBy.ACCESSIBILITY_ID, 'Publish Video'),

            # 返回按钮
            'back_button': (AppiumBy.ACCESSIBILITY_ID, 'Back'),

            # Reviews栏相关元素 这些目前没有专门的accessibility id
            'reviews_section': (AppiumBy.XPATH, '//XCUIElementTypeButton//XCUIElementTypeStaticText[@name="Reviews"]'),
            'total_reviews_count': (AppiumBy.XPATH, '//XCUIElementTypeButton//XCUIElementTypeStaticText[contains(@name,"・")]'),
            'customers_say_section': (AppiumBy.ACCESSIBILITY_ID, 'Customers say'),
            'ai_generated_text': (AppiumBy.ACCESSIBILITY_ID, 'AI-generated from the text of customer reviews'),

            # 卖点关键词相关元素
            'selling_points_container': (AppiumBy.XPATH, '//XCUIElementTypeOther[contains(@name,"Affordable")]'),
            # 这个在inspector上能找到，但程序找不到，奇怪
            'review_list_back': (AppiumBy.XPATH, '//XCUIElementTypeStaticText[contains(@name,"Reviews")]//following-sibling::XCUIElementTypeButton[not(@name)]'),
            'review_list_table': (AppiumBy.XPATH, '//XCUIElementTypeTable')
            # 'selling_point_keywords': (AppiumBy.IOS_PREDICATE, 'type == "XCUIElementTypeStaticText" AND name CONTAINS "keyword"'),
            # 'selling_point_keyword_item': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name CONTAINS "keyword"`]'),
        }
    }

    def __init__(self, driver, platform):
        self.platform = platform
        self.driver = driver
        self.strategy = self.strategies.get(self.platform)

    def get_videos_count(self):
        """获取视频数量"""
        try:
            videos_count_element = self.find_element(self.driver, self.strategy.get('videos_count'))
            if videos_count_element:
                # 提取数字部分
                count_text = videos_count_element.text.strip('()')
                # 假设格式为 "(5)" 或 "5"
                return int(count_text) if count_text else 0
            return 0
        except Exception as e:
            log.error(f"Failed to get videos count: {str(e)}")
            return 0

    def check_videos_section_exists(self):
        """检查视频部分是否存在"""
        # 需要向下滑动，否则看不见视频
        self.swipe_screen(_driver=self.driver)
        try:
            videos_section = self.find_elements(self.driver, self.strategy.get('videos_section'))
            return videos_section is not None and len(videos_section) > 0
        except Exception as e:
            log.error(f"Failed to check videos section: {str(e)}")
            return False

    def click_see_all_videos(self):
        """点击查看所有视频"""
        try:
            self.click(self.driver, self.strategy.get('see_all_videos'))
            log.info("Clicked on See all videos")
            time.sleep(3)  # 等待页面加载
            return True
        except Exception as e:
            log.error(f"Failed to click see all videos: {str(e)}")
            return False

    def check_video_categories_order(self):
        """检查视频分类的顺序"""
        try:
            # 获取各分类的元素
            review = self.find_element(self.driver, self.strategy.get('review_videos'))
            recipes = self.find_element(self.driver, self.strategy.get('recipes_videos'))
            unboxing = self.find_element(self.driver, self.strategy.get('unboxing_videos'))

            if not (review and recipes and unboxing):
                log.error("One or more video categories not found")
                return False
            else:
                return True

        except Exception as e:
            log.error(f"Failed to check video categories order: {str(e)}")
            return False

    def get_total_reviews_count(self):
        """获取总review数量"""
        try:
            reviews_count_element = self.find_element(self.driver, self.strategy.get('total_reviews_count'))
            if reviews_count_element:
                # 提取数字部分，假设格式为 "Reviews (123)" 或 "(123)"
                count_text = reviews_count_element.text
                import re
                numbers = re.findall(r'\d+', count_text)
                return int(numbers[0]) if numbers else 0
            return 0
        except Exception as e:
            log.error(f"Failed to get total reviews count: {str(e)}")
            return 0

    def check_reviews_section_exists(self):
        """检查Reviews栏是否存在"""
        try:
            # IOS向下滑动以确保能看到Reviews栏，需要向下翻2屏, 安卓滑动1次就好
            if self.platform == 'Android':
                self.swipe_screen(_driver=self.driver, distance=0.6)
            else:
                self.swipe_screen(_driver=self.driver)
                self.swipe_screen(_driver=self.driver)

            reviews_section = self.find_element(self.driver, self.strategy.get('reviews_section'))
            return reviews_section is not None
        except Exception as e:
            log.error(f"Failed to check reviews section: {str(e)}")
            return False

    def check_customers_say_section_exists(self):
        """检查Customers say栏是否存在"""
        try:
            customers_say = self.find_element(self.driver, self.strategy.get('customers_say_section'))
            return customers_say is not None
        except Exception as e:
            log.error(f"Failed to check customers say section: {str(e)}")
            return False

    def check_ai_generated_text_style(self):
        """检查AI-generated文本样式是否比正文淡"""
        try:
            ai_text = self.find_element(self.driver, self.strategy.get('ai_generated_text'))
            if ai_text:
                # 这里需要根据实际情况检查文本颜色或透明度
                # 由于Appium限制，可能需要通过其他方式验证样式
                return True
            return False
        except Exception as e:
            log.error(f"Failed to check AI generated text style: {str(e)}")
            return False

    def get_selling_point_keywords(self):
        """获取页面上的卖点关键词列表"""
        try:
            # 向下滑动以确保能看到卖点关键词
            self.swipe_screen(_driver=self.driver)
            keyword_elements = self.find_elements(self.driver, self.strategy.get('selling_point_keywords'))
            if keyword_elements:
                keywords = [element.text.strip() for element in keyword_elements if element.text.strip()]
                return keywords
            return []
        except Exception as e:
            log.error(f"Failed to get selling point keywords: {str(e)}")
            return []

    def get_selling_points_count(self):
        """获取页面上卖点关键词的数量"""
        keywords = self.get_selling_point_keywords()
        return len(keywords)

    def click_selling_point_keyword(self, keyword_text):
        """点击指定的卖点关键词"""
        try:
            keyword_elements = self.find_elements(self.driver, self.strategy.get('selling_point_keyword_item'))
            for element in keyword_elements:
                if element.text.strip() == keyword_text:
                    element.click()
                    log.info(f"Clicked on selling point keyword: {keyword_text}")
                    return True
            log.error(f"Selling point keyword '{keyword_text}' not found")
            return False
        except Exception as e:
            log.error(f"Failed to click selling point keyword '{keyword_text}': {str(e)}")
            return False

    def check_selling_points_display_condition(self, reviews_count):
        """检查卖点关键词显示条件"""
        try:
            selling_points = self.get_selling_point_keywords()
            if reviews_count >= 50:
                # review数大于等于50时，应该显示卖点关键词
                return len(selling_points) > 0
            else:
                # review数小于50时，不应该显示卖点关键词
                return len(selling_points) == 0
        except Exception as e:
            log.error(f"Failed to check selling points display condition: {str(e)}")
            return False