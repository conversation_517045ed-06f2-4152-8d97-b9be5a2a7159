import time
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.page_objects.base_page import BasePage
from src.config.weee.log_help import log


class AccountPage(BasePage):
    strategies = {
        'Android': {
            # Account页面元素
            'account_tab': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Account")'),
            'my_orders': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("My orders")'),

            # Orders页面元素
            'pending_tab': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Pending")'),
            'unshipped_tab': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Unshipped")'),
            'shipped_tab': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Shipped")'),
            'to_review_tab': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("To Review")'),
            'cancelled_tab': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Cancelled")'),

            # 返回按钮
            'back_button': (AppiumBy.ACCESSIBILITY_ID, 'Weee!'),

            # 页面标题和内容元素 (用于断言)
            'pending_content': (AppiumBy.XPATH, '//XCUIElementTypeTable/XCUIElementTypeCell'),
            'unshipped_content': (AppiumBy.XPATH, '//XCUIElementTypeTable/XCUIElementTypeCell'),
            'shipped_content': (AppiumBy.XPATH, '//XCUIElementTypeTable/XCUIElementTypeCell'),
            'to_review_content': (AppiumBy.XPATH, '//XCUIElementTypeTable/XCUIElementTypeCell'),
            'cancelled_content': (AppiumBy.XPATH, '//XCUIElementTypeTable/XCUIElementTypeCell'),
        },
        'iOS': {
            # Account页面元素
            'account_tab': (AppiumBy.ACCESSIBILITY_ID, 'Account'),
            'my_orders': (AppiumBy.ACCESSIBILITY_ID, 'My orders'),

            # Orders页面元素
            'pending_tab': (AppiumBy.ACCESSIBILITY_ID, 'Pending'),
            'unshipped_tab': (AppiumBy.ACCESSIBILITY_ID, 'Unshipped'),
            'shipped_tab': (AppiumBy.ACCESSIBILITY_ID, 'Shipped'),
            'to_review_tab': (AppiumBy.ACCESSIBILITY_ID, 'To Review'),
            'cancelled_tab': (AppiumBy.ACCESSIBILITY_ID, 'Cancelled'),

            # 返回按钮
            'back_button': (AppiumBy.ACCESSIBILITY_ID, 'nav back'),

            # 页面标题和内容元素 (用于断言)
            'pending_content': (AppiumBy.XPATH, '//XCUIElementTypeTable/XCUIElementTypeCell'),
            'unshipped_content': (AppiumBy.XPATH, '//XCUIElementTypeTable/XCUIElementTypeCell'),
            'shipped_content': (AppiumBy.XPATH, '//XCUIElementTypeTable/XCUIElementTypeCell'),
            'to_review_content': (AppiumBy.XPATH, '//XCUIElementTypeTable/XCUIElementTypeCell'),
            'cancelled_content': (AppiumBy.XPATH, '//XCUIElementTypeTable/XCUIElementTypeCell')

        }
    }

    def __init__(self, driver, platform):
        self.driver = driver
        self.platform = platform
        self.strategy = self.strategies.get(self.platform)

    def navigate_to_account_tab(self):
        """导航到Account标签页"""
        self.click(self.driver, self.strategy.get('account_tab'))
        log.info("Navigated to Account tab")
        time.sleep(3)  # 等待页面加载

    def navigate_to_my_orders(self):
        """点击My Orders进入订单页面"""
        self.click(self.driver, self.strategy.get('my_orders'))
        log.info("Navigated to My Orders page")
        time.sleep(3)  # 等待页面加载

    def check_order_tabs(self):
        """依次点击订单状态标签页"""
        # 点击Pending标签
        self.click(self.driver, self.strategy.get('pending_tab'))
        log.info("Clicked on Pending tab")
        time.sleep(2)  # 等待内容加载

        # 点击Unshipped标签
        self.click(self.driver, self.strategy.get('unshipped_tab'))
        log.info("Clicked on Unshipped tab")
        time.sleep(2)  # 等待内容加载

        # 点击Shipped标签
        self.click(self.driver, self.strategy.get('shipped_tab'))
        log.info("Clicked on Shipped tab")
        time.sleep(2)  # 等待内容加载

        # 点击To Review标签
        self.click(self.driver, self.strategy.get('to_review_tab'))
        log.info("Clicked on To Review tab")
        time.sleep(2)  # 等待内容加载

        # 点击Cancelled标签
        self.click(self.driver, self.strategy.get('cancelled_tab'))
        log.info("Clicked on Cancelled tab")
        time.sleep(2)  # 等待内容加载

    def go_back(self):
        """返回上一页"""
        self.click(self.driver, self.strategy.get('back_button'))
        log.info("Navigated back")
        time.sleep(2)  # 等待页面加载

    def is_tab_selected(self, tab_name):
        """验证指定的标签页是否被选中"""
        try:
            tab_element = self.find_element(self.driver, self.strategy.get(f'{tab_name.lower()}_tab'))
            if tab_element:
                # 简化的检查：如果标签页和指示器都存在，我们假设标签页已选中
                return True
            return False
        except Exception as e:
            log.error(f"Failed to verify if tab {tab_name} is selected: {str(e)}")
            return False

    def is_content_displayed(self, tab_name):
        """验证指定标签页的内容是否显示"""
        try:
            content_element = self.find_element(self.driver, self.strategy.get(f'{tab_name.lower()}_content'))
            return content_element is not None and content_element.is_displayed()
        except Exception as e:
            log.error(f"Failed to verify content for tab {tab_name}: {str(e)}")
            return False
