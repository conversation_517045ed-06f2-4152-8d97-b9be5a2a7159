import time
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.page_objects.base_page import BasePage
from src.config.weee.log_help import log


class GiftCardPage(BasePage):
    strategies = {
        'Android': {
            'gift_card_entry': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Gift cards")'),
            # Gift Card主页面元素
            'welcome_popup': (AppiumBy.ID, 'com.sayweee.weee:id/web'),
            'welcome_popup_close': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Got it")'),
            'welcome_popup_dismiss': (AppiumBy.XPATH, '//android.widget.Button[@text="Close"]'),
            
            # 收件人邮箱输入
            'recipient_email_input': (AppiumBy.ID, 'com.sayweee.weee:id/et_email'),
            'recipient_email_label': (AppiumBy.ID, 'com.sayweee.weee:id/tv_email_title'),
            'done': (AppiumBy.ACCESSIBILITY_ID, 'Done'),
            
            # Gift Card金额选择
            'gift_card_amount': (AppiumBy.ID, 'com.sayweee.weee:id/tv_title'),
            'amount_20': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("$25")'),
            'amount_50': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("$50")'),
            'amount_75': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("$75")'),
            
            # Checkout按钮
            'checkout_button': (AppiumBy.ID, 'com.sayweee.weee:id/tv_checkout'),
            'checkout_button_text': (AppiumBy.ID, 'com.sayweee.weee:id/tv_checkout'),
            
            # Checkout页面元素
            'checkout_page_title': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Checkout")'),
            'payment_method_dropdown': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("payment category image")'),
            'payment_method_label': (AppiumBy.XPATH, '//android.widget.TextView[@text="Payment Method"]'),
            
            # 支付方式选项
            'paypal_option': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("weee-payment").instance(0)'),
            'credit_card_option': (AppiumBy.XPATH, '//android.widget.TextView[@text="Credit Card"]'),
            'selected_payment_method': (AppiumBy.ID, 'com.sayweee.weee:id/tv_selected_payment'),
            
            # Place Order按钮
            'place_order_button': (AppiumBy.CLASS_NAME, 'android.widget.Button'),
            'place_order_text': (AppiumBy.CLASS_NAME, 'android.widget.Button'),
            
            # PayPal第三方页面元素
            'paypal_page_indicator': (AppiumBy.XPATH, '//android.widget.TextView[contains(@text, "PayPal")]'),
            'paypal_login_page': (AppiumBy.ID, 'com.sayweee.weee:id/title'),
            'paypal_webview': (AppiumBy.CLASS_NAME, 'android.webkit.WebView'),
            
            # 返回按钮
            'back_button': (AppiumBy.ID, 'com.sayweee.weee:id/iv_title_left'),
            'back_to_account': (AppiumBy.ID, 'com.sayweee.weee:id/iv_back')
        },
        'iOS': {
            'gift_card_entry': (AppiumBy.ACCESSIBILITY_ID, 'Gift cards'),
            # Gift Card主页面元素
            'welcome_popup': (AppiumBy.ACCESSIBILITY_ID, 'Got it'),
            'welcome_popup_close': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Close"`]'),
            'welcome_popup_dismiss': (AppiumBy.ACCESSIBILITY_ID, 'Close'),
            'choose_a_language': (AppiumBy.ACCESSIBILITY_ID, 'Choose a language'),

            
            # 收件人邮箱输入
            'recipient_email_input': (AppiumBy.IOS_PREDICATE, 'value == "Recipient email" AND type == "XCUIElementTypeTextField"'),
            'recipient_email_label': (AppiumBy.ACCESSIBILITY_ID, 'Recipient email'),
            'done': (AppiumBy.ACCESSIBILITY_ID, 'Done'),
            
            # Gift Card金额选择
            'gift_card_amount': (AppiumBy.ACCESSIBILITY_ID, 'choose an amount'),
            'amount_25': (AppiumBy.ACCESSIBILITY_ID, '$25'),
            'amount_50': (AppiumBy.ACCESSIBILITY_ID, '$50'),
            'amount_75': (AppiumBy.ACCESSIBILITY_ID, '$75'),
            'amount_100': (AppiumBy.ACCESSIBILITY_ID, '$100'),

            # Checkout按钮
            'checkout_button': (AppiumBy.ACCESSIBILITY_ID, 'Checkout'),
            'checkout_button_text': (AppiumBy.ACCESSIBILITY_ID, 'Checkout'),
            
            # Checkout页面元素
            'checkout_page_title': (AppiumBy.ACCESSIBILITY_ID, 'Checkout'),
            'payment_method_dropdown': (AppiumBy.ACCESSIBILITY_ID, 'payment category image'),
            'payment_method_label': (AppiumBy.ACCESSIBILITY_ID, 'Payment Method'),
            
            # 支付方式选项, 没有accessibility id，只能使用索引
            'paypal_option': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeImage[`name == "weee-payment"`][2]'),
            'credit_card_option': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypePickerWheel[`value == "Credit Card"`]'),
            'selected_payment_method': (AppiumBy.IOS_PREDICATE, 'type == "XCUIElementTypeStaticText" AND name CONTAINS "selected"'),
            
            # Place Order按钮
            'place_order_button': (AppiumBy.ACCESSIBILITY_ID, 'Place order'),
            'place_order_text': (AppiumBy.ACCESSIBILITY_ID, 'Place order'),
            
            # PayPal第三方页面元素
            'paypal_page_indicator': (AppiumBy.IOS_PREDICATE, 'type == "XCUIElementTypeStaticText" AND name CONTAINS "PayPal"'),
            'paypal_login_page': (AppiumBy.ACCESSIBILITY_ID, 'PayPal Logo'),

            # 返回按钮
            'back_button': (AppiumBy.ACCESSIBILITY_ID, 'back'),
            'back_to_account': (AppiumBy.XPATH, '//XCUIElementTypeButton[@name="Redeem"]//following-sibling::*')
        }
    }

    def __init__(self, driver, platform):
        self.platform = platform
        self.driver = driver
        self.strategy = self.strategies.get(self.platform)
    
    def check_welcome_popup_exists(self):
        """检查欢迎弹窗是否存在"""
        time.sleep(3)
        try:
            welcome_popup = self.find_element(self.driver, self.strategy.get('welcome_popup'))
            return welcome_popup is not None
        except Exception as e:
            log.error(f"Failed to check welcome popup: {str(e)}")
            return False
    
    def close_welcome_popup(self):
        """关闭欢迎弹窗"""
        # 安卓的关闭元素不能定位，原因未知
        if self.platform == 'Android':
            self.driver.tap([(80, 888)])
            time.sleep(1)  # 等待弹窗关闭
            return True
        else:
            self.driver.tap([(30, 428)])
            time.sleep(1)  # 等待弹窗关闭
            return True
            # ios目前未发现弹窗
            # self.find_element(self.driver, self.strategy.get('welcome_popup_close')).click()

        # try:
        #     close_button = self.find_element(self.driver, self.strategy.get('welcome_popup_close'))
        #     if close_button:
        #         close_button.click()
        #         time.sleep(2)  # 等待弹窗关闭
        #         log.info("Welcome popup closed successfully")
        #         return True
        #     return False
        # except Exception as e:
        #     log.error(f"Failed to close welcome popup: {str(e)}")
        #     return False
    def check_page_elements(self):
        """检查页面元素是否存在"""
        try:
            amount_25 = self.find_element(self.driver, self.strategy.get('amount_25')).is_displayed()
            amount_50 = self.find_element(self.driver, self.strategy.get('amount_50')).is_displayed()
            amount_75 = self.find_element(self.driver, self.strategy.get('amount_75')).is_displayed()
            return amount_25 and amount_50 and amount_75
        except Exception as e:
            log.error(f"Failed to check page elements: {str(e)}")
            return False

    def enter_recipient_email(self, email):
        """填入收件人邮箱"""
        self.swipe_screen(_driver=self.driver, distance=0.8)
        try:
            email_input = self.find_element(self.driver, self.strategy.get('recipient_email_input'))
            if email_input:
                email_input.clear()
                email_input.send_keys(email)
                if self.platform == 'Android':
                    email_input.click()
                log.info(f"Entered recipient email: {email}")
                done = self.find_element(self.driver, self.strategy.get('done'))
                if done:
                    done.click()
                return True
            return False
        except Exception as e:
            log.error(f"Failed to enter recipient email: {str(e)}")
            return False
    
    def verify_email_input_success(self, expected_email):
        """验证邮箱输入成功"""
        try:
            email_input = self.find_element(self.driver, self.strategy.get('recipient_email_input'))
            if email_input:
                actual_email = email_input.text
                return expected_email in actual_email
            return False
        except Exception as e:
            log.error(f"Failed to verify email input: {str(e)}")
            return False
    
    def click_checkout_button(self):
        """点击Checkout按钮"""
        try:
            checkout_btn = self.find_element(self.driver, self.strategy.get('checkout_button'))
            if checkout_btn:
                checkout_btn.click()
                time.sleep(3)  # 等待页面跳转
                log.info("Clicked checkout button")
                return True
            return False
        except Exception as e:
            log.error(f"Failed to click checkout button: {str(e)}")
            return False
    
    def check_checkout_page_loaded(self):
        """检查是否进入Checkout页面"""
        try:
            checkout_title = self.find_element(self.driver, self.strategy.get('checkout_page_title'))
            payment_dropdown = self.find_element(self.driver, self.strategy.get('payment_method_dropdown'))
            
            return checkout_title is not None or payment_dropdown is not None
        except Exception as e:
            log.error(f"Failed to check checkout page: {str(e)}")
            return False
    
    def click_payment_method_dropdown(self):
        """点击支付方式下拉栏"""
        try:
            payment_dropdown = self.find_element(self.driver, self.strategy.get('payment_method_dropdown'))
            if payment_dropdown:
                payment_dropdown.click()
                time.sleep(3)  # 等待下拉选项显示
                log.info("Clicked payment method dropdown")
                return True
            return False
        except Exception as e:
            log.error(f"Failed to click payment method dropdown: {str(e)}")
            return False
    
    def select_paypal_payment(self):
        """选择PayPal支付方式"""
        try:
            paypal_option = self.find_element(self.driver, self.strategy.get('paypal_option'))
            if paypal_option:
                paypal_option.click()
                time.sleep(3)  # 等待选择生效
                log.info("Selected PayPal payment method")
                return True
            return False
        except Exception as e:
            log.error(f"Failed to select PayPal payment: {str(e)}")
            return False
    
    def click_place_order_button(self):
        """点击Place Order按钮"""
        try:
            place_order_btn = self.find_element(self.driver, self.strategy.get('place_order_button'))
            if place_order_btn:
                place_order_btn.click()
                time.sleep(5)  # 等待跳转到第三方支付页面
                log.info("Clicked place order button")
                return True
            return False
        except Exception as e:
            log.error(f"Failed to click place order button: {str(e)}")
            return False
    
    def check_paypal_payment_page(self):
        """检查是否跳转到PayPal第三方支付页面"""
        try:
            # 检查PayPal登录页面元素
            paypal_login = self.find_element(self.driver, self.strategy.get('paypal_login_page'))
            if paypal_login:
                log.info("PayPal login page found")
                return True
            return False
        except Exception as e:
            log.error(f"Failed to check PayPal payment page: {str(e)}")
            return False
    
    def navigate_back(self):
        """返回上一页"""
        self.find_element(self.driver, self.strategy.get('back_button')).click()
        time.sleep(3)
        self.find_element(self.driver, self.strategy.get('back_to_account')).click()



