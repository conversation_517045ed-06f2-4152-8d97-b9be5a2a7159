import time
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.page_objects.base_page import BasePage
from src.config.weee.log_help import log


class Orders(BasePage):
    strategies = {
        'Android': {
            # home page 元素
            'home': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Home")'),
            'popup_close':(AppiumBy.ACCESSIBILITY_ID, 'Close'),
            'home_add_to_cart': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().resourceId("com.sayweee.weee:id/iv_edit_right").instance(0)'),
            'cart_tab': (AppiumBy.ID, 'com.sayweee.weee:id/iv_cart'),
            'checkout_button': (AppiumBy.ID, 'com.sayweee.weee:id/tv_checkout'),
            'place_order': (AppiumBy.ID, 'com.sayweee.weee:id/tv_checkout'),

            # Account页面元素
            'account': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Account")'),
            'account_tab': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Account")'),
            'my_orders': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("My orders")'),

            'pending_tab': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Pending")'),
            'pending_tag': (AppiumBy.ID, 'com.sayweee.weee:id/tv_tag'),

            # todo 等待开发加id
            'pending_order_container': (AppiumBy.XPATH, '//XCUIElementTypeTable/XCUIElementTypeCell'),
            'delivery_date': (),
            'total': (AppiumBy.ID, 'com.sayweee.weee:id/tv_total'),
            'order_number': (AppiumBy.ID, 'com.sayweee.weee:id/tv_order_number'),
            'cancel_order': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Cancel order")'),
            'pay_now': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Pay now")'),
            'cancel_order_confirm': (AppiumBy.ID, 'com.sayweee.weee:id/tv_confirm'),
            'reorder_in_details': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Reorder")'),


            'back_in_paypal': (AppiumBy.ACCESSIBILITY_ID, 'Return to previous page'),
            'back_in_checkout': (AppiumBy.ACCESSIBILITY_ID, 'Return to previous page'),
            'back_in_order_details': (AppiumBy.ACCESSIBILITY_ID, 'weee page back button'),
            'back_in_cart': (AppiumBy.ACCESSIBILITY_ID, 'Return to previous page'),

            'back_in_my_orders': (AppiumBy.ID, 'com.sayweee.weee:id/iv_back')

        },
        'iOS': {
            # home page 元素
            'home': (AppiumBy.ACCESSIBILITY_ID, 'enki-home'),
            "popup_close": (AppiumBy.ACCESSIBILITY_ID, 'popUp close'),
            'home_add_to_cart': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Add item to cart"`]'),
            'cart_tab': (AppiumBy.ACCESSIBILITY_ID, 'layout_cart'),
            'checkout_button': (AppiumBy.ACCESSIBILITY_ID, 'tv_checkout'),
            'place_order': (AppiumBy.ACCESSIBILITY_ID, 'tv_checkout'),

            # Account页面元素
            'account': (AppiumBy.ACCESSIBILITY_ID, 'Account'),
            'account_tab': (AppiumBy.ACCESSIBILITY_ID, 'enki-account'),
            'my_orders': (AppiumBy.ACCESSIBILITY_ID, 'My orders'),

            'pending_tab': (AppiumBy.ACCESSIBILITY_ID, 'Pending'),
            'pending_tag': (AppiumBy.ACCESSIBILITY_ID, 'PENDING'),

            # todo 等待开发加id
            'pending_order_container': (AppiumBy.XPATH, '//XCUIElementTypeTable/XCUIElementTypeCell'),
            'delivery_date': (),
            'total':(),
            'order_number':(),
            'cancel_order':(AppiumBy.ACCESSIBILITY_ID, 'Cancel order'),
            'pay_now': (AppiumBy.ACCESSIBILITY_ID, 'Pay now'),
            'cancel_order_confirm': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Confirm"`]'),
            'reorder_in_details': (AppiumBy.ACCESSIBILITY_ID, 'Reorder'),

            'back_in_paypal': (AppiumBy.ACCESSIBILITY_ID, 'back'),
            'back_in_checkout': (AppiumBy.ACCESSIBILITY_ID, 'weee page back button'),
            'back_in_order_details': (AppiumBy.ACCESSIBILITY_ID, 'weee page back button'),
            # todo 目前没有id
            'back_in_cart': (),

            'back_in_my_orders': (AppiumBy.ACCESSIBILITY_ID, 'nav back')

        }
    }

    def __init__(self, driver, platform):
        self.driver = driver
        self.platform = platform
        self.strategy = self.strategies.get(self.platform)

    def add_product_from_home(self):
        self.find_element(self.driver, self.strategy.get('home')).click()
        adv = self.find_element(self.driver, self.strategy.get("popup_close"))
        if adv:
            adv.click()
        # 点击首页，下划0.4
        self.swipe_screen(_driver=self.driver, distance=0.4)
        # 点击add to cart
        add_to_cart_button = self.find_elements(self.driver, self.strategy.get("home_add_to_cart"))
        for index, i in enumerate(add_to_cart_button):
            # 这里安卓与ios有一些不同，向下滑动后安卓只有2个加购，ios有很多
            i.click()
            if index == 0:
                break
            time.sleep(5)

    def navigate_to_cart(self):
        """导航到购物车页面"""
        try:
            cart_tab = self.find_element(self.driver, self.strategy.get('cart_tab'))
            if cart_tab:
                cart_tab.click()
                time.sleep(3)  # 等待购物车页面加载
                log.info("Navigated to cart page")
                return True
            return False
        except Exception as e:
            log.error(f"Failed to navigate to cart: {str(e)}")
            return False

    def click_checkout_button(self):
        """点击结算按钮"""
        try:
            checkout_btn = self.find_element(self.driver, self.strategy.get('checkout_button'))
            if checkout_btn:
                checkout_btn.click()
                time.sleep(3)  # 等待页面跳转
                log.info("Clicked checkout button")
                return True
            return False
        except Exception as e:
            log.error(f"Failed to click checkout button: {str(e)}")
            return False

    def place_order(self):
        """点击Place Order按钮"""
        try:
            place_order_btn = self.find_element(self.driver, self.strategy.get('place_order'))
            if place_order_btn:
                place_order_btn.click()
                time.sleep(3)  # 等待页面跳转
                log.info("Clicked place order button")
                return True
            return False
        except Exception as e:
            log.error(f"Failed to click place order button: {str(e)}")
            return False

    def go_back_from_paypal_to_home(self):
        """从PayPal页面返回到首页"""
        self.click(self.driver, self.strategy.get('back_in_paypal'))
        time.sleep(5)
        self.click(self.driver, self.strategy.get('back_in_checkout'))
        time.sleep(5)
        if self.platform == 'Android':
            try:
                self.find_element(self.driver, self.strategy.get('back_in_cart')).click()
            except Exception as e:
                log.error(f"Failed to click back in cart: {str(e)}")
        else:
            self.driver.tap([(13, 55)])
        log.info("Navigated back from PayPal to home page")
        time.sleep(3)  # 等待页面加载

        # 有可能出现广告
        adv = self.find_element(self.driver, self.strategy.get("popup_close"))
        if adv:
            adv.click()


    def navigate_to_account_tab(self):
        """导航到Account标签页"""
        self.click(self.driver, self.strategy.get('account_tab'))
        log.info("Navigated to Account tab")
        time.sleep(3)  # 等待页面加载

    def navigate_to_my_orders(self):
        """点击My Orders进入订单页面"""
        self.click(self.driver, self.strategy.get('my_orders'))
        log.info("Navigated to My Orders page")
        time.sleep(3)  # 等待页面加载


    def go_to_unpaid_orders_tab(self):
        """点击Unpaid进入未支付订单页面"""
        self.click(self.driver, self.strategy.get('pending_tab'))
        log.info("Navigated to Unpaid Orders page")
        time.sleep(3)  # 等待页面加载

    def check_unpaid_order_ui(self):
        """检查未支付订单页面元素"""
        a = self.find_elements(self.driver, self.strategy.get('pending_tag'))[0].is_displayed()
        # todo 等待开发加id
        # b = self.find_element(self.driver, self.strategy.get('delivery_date'))
        # c = self.find_element(self.driver, self.strategy.get('total'))
        # d = self.find_element(self.driver, self.strategy.get('order_number'))
        e = self.find_elements(self.driver, self.strategy.get('cancel_order'))[0].is_displayed()
        f = self.find_elements(self.driver, self.strategy.get('pay_now'))[0].is_displayed()

        return a and e and f


    def cancel_unpaid_orders(self):
        """取消未支付订单"""
        self.find_elements(self.driver, self.strategy.get('cancel_order'))[0].click()
        self.find_element(self.driver, self.strategy.get('cancel_order_confirm')).click()
        time.sleep(3)


    def go_back_from_my_orders_to_account(self):
        if self.platform == 'Android':
            self.driver.tap([(55, 110)])
            time.sleep(2)
        else:
            self.click(self.driver, self.strategy.get('back_in_order_details'))

        time.sleep(2)
        self.click(self.driver, self.strategy.get('back_in_my_orders'))
        time.sleep(3)
