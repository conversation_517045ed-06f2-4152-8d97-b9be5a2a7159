import time

from appium.webdriver import WebElement
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.page_objects.base_page import BasePage
from src.config.weee.log_help import log


class HomePage(BasePage):
    strategies = {
        'Android': {
            "home": (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Home")'),
            'explore': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Explore")'),
            "first_product": (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().resourceId("com.sayweee.weee:id/layout_product")'),
            "popup_close": (AppiumBy.ACCESSIBILITY_ID, 'Close'),

            "store_select": (AppiumBy.ID, 'com.sayweee.weee:id/iv_logo_arrow'),
            "chinese_store": (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Chinese")'),
            "japanese_store": (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Japanese")'),
            "korean_store": (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Korean")'),
            "vietnamese_store": (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Vietnamese")'),
            "filipino_store": (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Filipino")'),
            'thailand_store': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Thai")'),
            'explorer_store': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Explorer")'),
            # 需要使用数组
            'products_on_home': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().resourceId("com.sayweee.weee:id/layout_product")'),

            # category on home
            'category_container_on_home': (AppiumBy.ID, 'com.sayweee.weee:id/recyclerView'),
            'categories': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().resourceId("com.sayweee.weee:id/layout_category")'),
            'category_title': (AppiumBy.XPATH, '//android.widget.TextView[@resource-id="com.sayweee.weee:id/tv_category"]'),
            'category_image': (AppiumBy.XPATH, '//android.widget.ImageView[@resource-id="com.sayweee.weee:id/iv_category"]')

        },
        'iOS': {
            'explore': (AppiumBy.ACCESSIBILITY_ID, 'enki-category'),
            "home": (AppiumBy.ACCESSIBILITY_ID, 'enki-home'),
            "first_product": (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeCell[`name == "item"`]'),
            "popup_close": (AppiumBy.ACCESSIBILITY_ID, 'popUp close'),

            "store_select": (AppiumBy.ACCESSIBILITY_ID, 'layout_logo'),
            "chinese_store": (AppiumBy.ACCESSIBILITY_ID, 'Chinese'),
            "japanese_store": (AppiumBy.ACCESSIBILITY_ID, 'Japanese'),
            "korean_store": (AppiumBy.ACCESSIBILITY_ID, 'Korean'),
            "vietnamese_store": (AppiumBy.ACCESSIBILITY_ID, 'Vietnamese'),
            "filipino_store": (AppiumBy.ACCESSIBILITY_ID, 'Filipino'),
            'thailand_store': (AppiumBy.ACCESSIBILITY_ID, 'Thai'),
            'explorer_store': (AppiumBy.ACCESSIBILITY_ID, 'Explorer'),
            # 需要使用数组
            'products_on_home': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeCell[`name == "item"`]'),

            # category on home
            'category_container_on_home': (AppiumBy.ACCESSIBILITY_ID, 'cm_categories_4416926'),
            'categories': (AppiumBy.XPATH,
                           '//XCUIElementTypeCell[@name]'),
            'category_title': (AppiumBy.XPATH,
                               '//XCUIElementTypeStaticText[@name]'),
            'category_image': (AppiumBy.XPATH,
                               '//XCUIElementTypeOther/XCUIElementTypeImage'),

            # cms组件
            'editors_pick_container': (AppiumBy.ACCESSIBILITY_ID, 'cm_item_editors_pick'),
            'everyday_deals_container': (AppiumBy.ACCESSIBILITY_ID, 'cm_item_sale'),
            'freshly_made_container': (AppiumBy.ACCESSIBILITY_ID, 'cm_product_line_tabs_fresh_daily'),
            'new_arrivals_container': (AppiumBy.ACCESSIBILITY_ID, 'cm_item_new'),
            'lighting_deals_container': (AppiumBy.ACCESSIBILITY_ID, 'cm_lightning_deals'),
            'weekly_specials_container': (AppiumBy.ACCESSIBILITY_ID, 'cm_collection_v2_manual_rank1'),
            'best_sellers_container': (AppiumBy.ACCESSIBILITY_ID, 'cm_item_trending'),
            'global_lighting_deals': (AppiumBy.ACCESSIBILITY_ID, 'cm_mkpl_lightning'),
            'home_recommendation': (AppiumBy.ACCESSIBILITY_ID, 'cm_content_feed'),

            'home_products': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeCell[`name == "layout_product"`]'),
            'home_add_to_cart': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Add item to cart"`]')


        }
    }

    def __init__(self, driver, platform):
        self.platform = platform
        self.driver = driver
        self.strategy = self.strategies.get(self.platform)

    def get_home_cms_collection(self, collection_name):
        coll = self.find_element(self.driver, self.strategy.get(collection_name))
        return coll

    def get_home_products_in_cms(self, collection_ele: WebElement):
        products = collection_ele.find_elements(*self.strategy.get('home_products'))
        return products


    def switch_store(self, store_locator):
        self.find_element(self.driver, self.strategy.get('store_select')).click()
        time.sleep(2)
        self.find_element(self.driver, store_locator).click()
        time.sleep(5)
        # 切换store后可能会弹出广告，关闭广告
        adv = self.find_element(self.driver, self.strategy.get("popup_close"))
        if adv:
            adv.click()

    def check_home_product_after_switch_store(self):
        products = self.find_elements(self.driver, self.strategy.get('products_on_home'))
        return products

    def check_categories_on_home(self):
        categories_container = self.find_element(self.driver, self.strategy.get('category_container_on_home'))
        assert categories_container, f"Failed to find categories container"
        categories = self.find_elements(self.driver, self.strategy.get('categories'))

        return categories

        # 由于首页的category是滚动展示的，以下代码会报 androidx.test.uiautomator.StaleObjectException
        # category_titles = []
        # if categories:
        #     for index, category in enumerate(categories):
        #         category_title = category.find_element(*self.strategy.get('category_title')).get_attribute('text')
        #         log.info(f"Category {index + 1}: {category_title}")
        #         category_titles.append(category_title)
        #         category.find_element(*self.strategy.get('category_image')).is_displayed()
        #         return category_titles
        # else:
        #     raise Exception("No categories found on home page")










