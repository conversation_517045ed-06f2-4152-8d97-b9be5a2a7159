import time
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.page_objects.base_page import BasePage
from src.config.weee.log_help import log


class SearchPage(BasePage):
    strategies = {
        'Android': {
            "home": (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Home")'),
            'explore': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Explore")'),
            "popup_close": (AppiumBy.XPATH, '//*[@resource-id="com.sayweee.weee:id/popUp close"]'),
            'search_hint': (AppiumBy.ID, 'com.sayweee.weee:id/tv_search_hint'),
            'search_input': (AppiumBy.ID, 'com.sayweee.weee:id/et_input'),
            'search_list_container': (AppiumBy.ID, 'com.sayweee.weee:id/fragment_search_suggest_container'),
            'search_list_items': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().resourceId("com.sayweee.weee:id/layout")'),
            'search_result_container': (AppiumBy.ID, 'com.sayweee.weee:id/recycler_view'),
            'search_result_items': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().resourceId("com.sayweee.weee:id/search_result_product_container")'),
            'result_product_image': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().resourceId("com.sayweee.weee:id/search_result_product_image")'),
            'result_product_title': (AppiumBy.XPATH, '//android.widget.TextView[@resource-id="com.sayweee.weee:id/search_result_product_title"]'),
            'result_product_price': (AppiumBy.XPATH, '//android.widget.TextView[@resource-id="com.sayweee.weee:id/search_result_product_price"]'),
            'result_product_add_to_cart': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().resourceId("com.sayweee.weee:id/iv_edit_right")'),
            'filter_in_result_page': (AppiumBy.ID, 'com.sayweee.weee:id/search_v2_section_tabs_filters_icon_view'),
            'filter_local_delivery': (AppiumBy.ACCESSIBILITY_ID, 'Local Delivery Unselected'),
            'filter_apply': (AppiumBy.ID, 'com.sayweee.weee:id/tv_apply'),
            'back_to_home': (AppiumBy.ACCESSIBILITY_ID, 'Return to previous page')
        },
        'iOS': {
            'explore': (AppiumBy.ACCESSIBILITY_ID, 'enki-category'),
            "home": (AppiumBy.ACCESSIBILITY_ID, 'enki-home'),
            "popup_close": (AppiumBy.ACCESSIBILITY_ID, 'popUp close')

        }
    }


    def __init__(self, driver, platform):
        self.platform = platform
        self.driver = driver
        self.strategy = self.strategies.get(self.platform)


    def search_on_home_page(self, keyword):
        self.find_element(self.driver, self.strategy.get('search_hint')).click()
        self.find_element(self.driver, self.strategy.get('search_input')).send_keys(keyword)
        time.sleep(3)
        if self.platform == 'iOS':
            self.driver.keyevent(66)
        else:
            self.driver.press_keycode(66)

    def check_search_list_displayed(self):
        a = self.find_element(self.driver, self.strategy.get('search_list_container')).is_displayed()
        search_list = self.find_elements(self.driver, self.strategy.get('search_list_items'))
        if search_list:
            search_list[0].click()
            time.sleep(3)
        return a and search_list

    def check_search_result_displayed(self):
        assert self.find_element(self.driver, self.strategy.get('search_result_container')).is_displayed()
        result_list = self.find_elements(self.driver, self.strategy.get('search_result_items'))
        if result_list:
            for index, item in enumerate(result_list):
                if index == 3:
                    break
                assert item.find_element(*self.strategy.get('result_product_image')).is_displayed()
                assert item.find_element(*self.strategy.get('result_product_title')).is_displayed()
                assert item.find_element(*self.strategy.get('result_product_price')).is_displayed()
                item.find_element(*self.strategy.get('result_product_add_to_cart')).click()
        else:
            raise Exception("No search results found")

    def switch_filter(self, filter_name='filter_local_delivery'):
        self.find_element(self.driver, self.strategy.get('filter_in_result_page')).click()
        time.sleep(3)
        self.find_element(self.driver, self.strategy.get(filter_name)).click()
        self.find_element(self.driver, self.strategy.get('filter_apply')).click()

    def check_result_after_switch_filter(self):
        result_list = self.find_elements(self.driver, self.strategy.get('search_result_items'))
        if result_list:
            for index, item in enumerate(result_list):
                if index == 3:
                    break
                assert item.find_element(*self.strategy.get('result_product_image')).is_displayed()
                assert item.find_element(*self.strategy.get('result_product_title')).is_displayed()
                assert item.find_element(*self.strategy.get('result_product_price')).is_displayed()
        else:
            raise Exception("No search results found")

    def back_to_home_page(self):
        self.find_element(self.driver, self.strategy.get('back_to_home')).click()















