import time
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.page_objects.base_page import BasePage
from src.config.weee.log_help import log


class HomePage(BasePage):
    strategies = {
        'Android': {
            "home": (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Home")'),
            'explore': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Explore")'),
            "first_product": (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().resourceId("com.sayweee.weee:id/layout_product")'),
            "popup_close": (AppiumBy.XPATH, '//*[@resource-id="com.sayweee.weee:id/popUp close"]')

        },
        'iOS': {
            'explore': (AppiumBy.ACCESSIBILITY_ID, 'Explore'),
            "home": (AppiumBy.ACCESSIBILITY_ID, 'Home'),
            "first_product": (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeCell[`name == "item"`]'),
            "popup_close": (AppiumBy.ACCESSIBILITY_ID, 'popUp close')

        }
    }


    def __init__(self, driver, platform):
        self.platform = platform
        self.driver = driver
        self.strategy = self.strategies.get(self.platform)

        adv = self.find_element(_driver=self.driver, ele=self.strategy.get("popup_close"))
        if adv:
            adv.click()








