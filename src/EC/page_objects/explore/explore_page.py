import time
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.page_objects.base_page import BasePage
from src.config.weee.log_help import log


class ExplorePage(BasePage):
    strategies = {
        'Android': {
            'explore': (AppiumBy.XPATH, '//android.widget.TextView[@resource-id="com.sayweee.weee:id/txt" and @text="Explore"]'),
            "home": (AppiumBy.XPATH, '//android.widget.TextView[@resource-id="com.sayweee.weee:id/txt" and @text="Home"]'),
            'zipcode': (AppiumBy.ID, 'com.sayweee.weee:id/tv_location_enki'),
            'deals': (AppiumBy.XPATH, '//android.widget.TextView[@resource-id="com.sayweee.weee:id/tv_category" and @text="Deals"]'),
            'new_arrivals': (AppiumBy.XPATH, '//android.widget.TextView[@resource-id="com.sayweee.weee:id/tv_category" and @text="New Arrivals"]'),
            'Bestsellers': (AppiumBy.XPATH, '//android.widget.TextView[@resource-id="com.sayweee.weee:id/tv_category" and @text="Bestsellers"]'),
            'filter': (AppiumBy.ID, 'com.sayweee.weee:id/iv_sort'),
            # sort 列表形式返回
            'sorts': (AppiumBy.XPATH, '(//android.widget.ImageView[@resource-id="com.sayweee.weee:id/iv_sort"])'),
            # filter 列表形式返回 与product_type一起，安卓组件不区分
            'filter_inner': (AppiumBy.XPATH, '(//android.widget.ImageView[@resource-id="com.sayweee.weee:id/iv_filter"])'),
            'apply_button': (AppiumBy.ID, 'com.sayweee.weee:id/layout_apply'),
            'filter_main': (AppiumBy.ID, 'com.sayweee.weee:id/rv'),
            # filter之前的页面加购按钮，以列表形式返回
            'filter_main_add_cart': (AppiumBy.XPATH, '(//android.widget.ImageView[@resource-id="com.sayweee.weee:id/iv_edit_right"])'),
        },
        'iOS': {
            'explore': (AppiumBy.ACCESSIBILITY_ID, 'Explore'),
            "home": (AppiumBy.ACCESSIBILITY_ID, 'Home'),
            'zipcode': (AppiumBy.ACCESSIBILITY_ID, 'layout_location'),
            'deals': (AppiumBy.IOS_PREDICATE, 'name == "Deals" AND label == "Deals" AND value == "Deals"'),
            'new_arrivals': (AppiumBy.IOS_PREDICATE, 'New Arrivals'),
            'Bestsellers': (AppiumBy.ACCESSIBILITY_ID, 'Bestsellers'),
            'filter': (AppiumBy.ACCESSIBILITY_ID, 'enki Filter'),
            # sort 列表形式返回
            'sorts_feature': (AppiumBy.XPATH, '(//android.widget.ImageView[@resource-id="com.sayweee.weee:id/iv_sort"])'),
            'sorts_bestselling': (AppiumBy.XPATH, '(//android.widget.ImageView[@resource-id="com.sayweee.weee:id/iv_sort"])'),
            'sorts_price_low_to_high': (AppiumBy.XPATH, '(//android.widget.ImageView[@resource-id="com.sayweee.weee:id/iv_sort"])'),
            'sorts_price_high_to_low': (AppiumBy.XPATH, '(//android.widget.ImageView[@resource-id="com.sayweee.weee:id/iv_sort"])'),
            # filter 列表形式返回 与product_type一起，安卓组件不区分
            'filter_inner': (
            AppiumBy.XPATH, '(//android.widget.ImageView[@resource-id="com.sayweee.weee:id/iv_filter"])'),
            'apply_button': (AppiumBy.ID, 'com.sayweee.weee:id/layout_apply'),
            'filter_main': (AppiumBy.ID, 'com.sayweee.weee:id/rv'),
            # filter之前的页面加购按钮，以列表形式返回
            'filter_main_add_cart': (AppiumBy)
        }
    }


    def __init__(self, driver, platform):
        self.platform = platform
        self.driver = driver
        self.strategy = self.strategies.get(self.platform)

    def search_product_by_sort(self, _sort):
        _sort.click()
        self.click(self.driver, self.strategy.get("apply_button"))
        time.sleep(5)


    def search_product_by_filter(self, _filter):
        pass

    def search_product_by_product_type(self, product_type: str):
        pass


