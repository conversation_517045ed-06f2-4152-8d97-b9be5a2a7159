import time
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.page_objects.base_page import BasePage
from src.config.weee.log_help import log


class SocialSearchPage(BasePage):
    strategies = {
        'Android': {
            # 搜索相关元素
            'inspiration': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Inspiration")'),
            'search_input': (AppiumBy.ID, 'com.sayweee.weee:id/et_input'),
            'search_button': (AppiumBy.ID, 'com.sayweee.weee:id/iv_search'),
            'search_clear': (AppiumBy.ID, 'com.sayweee.weee:id/iv_clear'),
            
            # 搜索结果页面标签
            'posts_tab': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Posts")'),
            'accounts_tab': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Accounts")'),
            'videos_tab': (AppiumBy.XPATH, '//android.widget.TextView[@text="Videos"]'),
            
            # 用户列表相关元素
            'user_list_container': (AppiumBy.ID, 'com.sayweee.weee:id/cl_layout'),
            'user_item': (AppiumBy.ID, 'com.sayweee.weee:id/tv_name'),
            'user_avatar': (AppiumBy.ID, 'com.sayweee.weee:id/iv_header'),
            'user_name': (AppiumBy.ID, 'com.sayweee.weee:id/tv_name'),
            'user_display_name': (AppiumBy.ID, 'com.sayweee.weee:id/tv_display_name'),
            # 安卓这2个按钮的id一样
            'follow_button': (AppiumBy.ID, 'com.sayweee.weee:id/tv_follow'),
            'following_button': (AppiumBy.ID, 'com.sayweee.weee:id/tv_follow'),
            
            # 空状态页面
            'empty_state_container': (AppiumBy.ID, 'com.sayweee.weee:id/layout_empty'),
            'empty_state_text': (AppiumBy.ID, 'com.sayweee.weee:id/tv_empty_message'),
            'no_results_text': (AppiumBy.XPATH, '//android.widget.TextView[contains(@text, "No results found")]'),
            
            # 用户profile页面元素
            'profile_username': (AppiumBy.ID, 'com.sayweee.weee:id/tv_name'),
            'profile_display_name': (AppiumBy.ID, 'com.sayweee.weee:id/tv_profile_display_name'),
            'profile_follow_button': (AppiumBy.ID, 'com.sayweee.weee:id/iv_profile_edit'),
            'profile_unfollow_button': (AppiumBy.ID, 'com.sayweee.weee:id/tv_edit'),
            'profile_yes_unfollow': (AppiumBy.ID, 'com.sayweee.weee:id/tv_confirm'),
            'profile_back_button': (AppiumBy.XPATH, '//android.widget.ImageButton[@content-desc="Navigate up"]'),
            
            # 返回按钮- back to inspiration界面
            'back_button': (AppiumBy.ID, 'com.sayweee.weee:id/iv_close'),
            # todo 安卓需要修改这个标识符
            'back_to_post': (AppiumBy.ACCESSIBILITY_ID, 'None')
        },
        'iOS': {
            # 搜索相关元素
            'inspiration': (AppiumBy.ACCESSIBILITY_ID, 'enki-inspiration'),
            'search_input': (AppiumBy.ACCESSIBILITY_ID, 'layout_search'),
            'search_button': (AppiumBy.ACCESSIBILITY_ID, 'Click to enter the search page'),

            # 搜索结果页面标签
            'posts_tab': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Posts"`]'),
            'accounts_tab': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Accounts"`]'),
            'videos_tab': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Videos"`]'),
            
            # 用户列表相关元素
            'user_list_container': (AppiumBy.CLASS_NAME, 'XCUIElementTypeCell'),
            'user_item': (AppiumBy.ACCESSIBILITY_ID, 'autotest'),
            # avatar这个元素在ios上的不到，用user_item代替
            'user_avatar': (AppiumBy.ACCESSIBILITY_ID, 'autotest'),
            'user_name': (AppiumBy.ACCESSIBILITY_ID, 'autotest'),
            'user_display_name': (AppiumBy.ACCESSIBILITY_ID, 'autotest'),
            'follow_button': (AppiumBy.ACCESSIBILITY_ID, 'Follow'),
            'following_button': (AppiumBy.ACCESSIBILITY_ID, 'Following'),
            'unfollow_button': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Yes, unfollow"`]'),
            
            # 空状态页面
            #'empty_state_container': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeView[`name CONTAINS "empty"`]'),
            'empty_state_text': (AppiumBy.ACCESSIBILITY_ID, 'Check the spelling or try searching for something more general.'),
            'no_results_text': (AppiumBy.ACCESSIBILITY_ID, 'Check the spelling or try searching for something more general.'),
            
            # 用户profile页面元素
            'profile_username': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "autotest"`][1]'),
            'profile_display_name': (AppiumBy.IOS_PREDICATE, 'type == "XCUIElementTypeStaticText" AND name CONTAINS "profile_display_name"'),
            'profile_follow_button': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Follow"`]'),
            'profile_unfollow_button': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Following"`]'),
            'profile_yes_unfollow': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Yes, unfollow"`]'),
            'profile_back_button': (AppiumBy.ACCESSIBILITY_ID, 'Back'),
            
            # 返回按钮- back to inspiration界面
            'back_button': (AppiumBy.ACCESSIBILITY_ID, 'Return to previous page'),
            # ios的这个按钮无法定位
            'back_to_post': (AppiumBy.ACCESSIBILITY_ID, 'None')
        }
    }

    def __init__(self, driver, platform):
        self.platform = platform
        self.driver = driver
        self.strategy = self.strategies.get(self.platform)
    
    def search_user(self, username):
        """搜索用户"""
        try:
            # 点击搜索按钮和输入框
            self.find_element(self.driver, self.strategy.get('search_button')).click()
            search_input = self.find_element(self.driver, self.strategy.get('search_input'))
            if search_input:
                search_input.clear()
                search_input.send_keys(username)
                log.info(f"Entered search term: {username}")
                
                # 点击搜索按钮或按回车
                if self.platform == 'iOS':
                    search_input.send_keys('\n')
                else:
                    # self.driver.press_keycode(66)
                    self.driver.keyevent(66)
                time.sleep(3)  # 等待搜索结果加载
                return True
            return False
        except Exception as e:
            log.error(f"Failed to search user '{username}': {str(e)}")
            return False
    
    def switch_to_accounts_tab(self):
        """切换到Accounts标签"""
        try:
            accounts_tab = self.find_element(self.driver, self.strategy.get('accounts_tab'))
            if accounts_tab:
                accounts_tab.click()
                time.sleep(2)  # 等待标签切换
                log.info("Switched to Accounts tab")
                return True
            return False
        except Exception as e:
            log.error(f"Failed to switch to accounts tab: {str(e)}")
            return False
    
    def check_posts_tab_active(self):
        """检查Photos标签是否为默认激活状态"""
        try:
            photos_tab = self.find_element(self.driver, self.strategy.get('posts_tab'))
            if photos_tab:
                # 这里可以检查标签的选中状态，具体实现取决于UI设计
                return True
            return False
        except Exception as e:
            log.error(f"Failed to check photos tab status: {str(e)}")
            return False
    
    def get_user_list(self):
        """获取用户列表"""
        try:
            user_items = self.find_elements(self.driver, self.strategy.get('user_item'))

            if user_items:
                return user_items
            return []

        except Exception as e:
            log.error(f"Failed to get user list: {str(e)}")
            return []
    
    def click_user_avatar(self, username):
        """点击指定用户的头像"""
        try:
            # 在用户项中查找头像,头像元素在页面找不到，用用户名代替
            avatar = self.find_element(self.driver, self.strategy.get('user_avatar'))
            if avatar:
                avatar.click()
                time.sleep(3)  # 等待页面跳转
                log.info(f"Clicked avatar for user: {username}")
                return True

            log.error(f"User '{username}' not found in search results")
            return False
        except Exception as e:
            log.error(f"Failed to click user avatar for '{username}': {str(e)}")
            return False
    
    def click_follow_button(self, username):
        """点击指定用户的关注按钮"""
        try:
            follow_btn = self.find_element(self.driver, self.strategy.get('profile_follow_button'))
            following_btn = self.find_element(self.driver, self.strategy.get('profile_unfollow_button'))
            if follow_btn:
                self.find_element(self.driver, self.strategy.get('profile_follow_button')).click()
                time.sleep(2)  # 等待状态更新
                self.find_element(self.driver, self.strategy.get('profile_unfollow_button')).click()
                self.find_element(self.driver, self.strategy.get('profile_yes_unfollow')).click()
                log.info(f"Clicked follow button for user: {username}")
                return True

            # 如果是已关注状态，查找取消关注按钮

            elif following_btn:
                self.find_element(self.driver, self.strategy.get('profile_unfollow_button')).click()
                self.find_element(self.driver, self.strategy.get('profile_yes_unfollow')).click()
                time.sleep(2)  # 等待状态更新
                # self.find_element(self.driver, self.strategy.get('profile_follow_button')).click()
                log.info(f"Clicked unfollow button for user: {username}")
                return True
            
            log.error(f"Follow button not found for user '{username}'")
            return False
        except Exception as e:
            log.error(f"Failed to click follow button for '{username}': {str(e)}")
            return False
    
    def check_user_profile_page(self, expected_username):
        """检查是否成功跳转到用户profile页面"""
        try:
            profile_username = self.find_element(self.driver, self.strategy.get('profile_username'))
            if profile_username:
                actual_username = profile_username.text
                if expected_username in actual_username:
                    log.info(f"Successfully navigated to profile page for user: {expected_username}")
                    return True
                else:
                    log.error(f"Profile page username mismatch: expected '{expected_username}', got '{actual_username}'")
                    return False
            return False
        except Exception as e:
            log.error(f"Failed to check user profile page: {str(e)}")
            return False
    
    def check_empty_state(self):
        """检查是否显示空状态页面"""
        try:
            empty_container = self.find_element(self.driver, self.strategy.get('empty_state_container'))
            no_results_text = self.find_element(self.driver, self.strategy.get('no_results_text'))
            
            if empty_container or no_results_text:
                log.info("Empty state page detected")
                return True
            return False
        except Exception as e:
            log.error(f"Failed to check empty state: {str(e)}")
            return False
    
    def get_follow_button_status(self, username):
        """获取指定用户的关注按钮状态"""
        try:
            users = self.get_user_list()
            for user in users:
                if user['username'] == username:
                    # 检查是否为关注状态
                    follow_btn = user['element'].find_element(*self.strategy.get('follow_button'))
                    if follow_btn:
                        return 'follow'
                    
                    following_btn = user['element'].find_element(*self.strategy.get('following_button'))
                    if following_btn:
                        return 'following'
            
            return 'unknown'
        except Exception as e:
            log.error(f"Failed to get follow button status for '{username}': {str(e)}")
            return 'error'
    
    def navigate_back(self):
        """返回上一页"""
        try:
            back_button = self.find_element(self.driver, self.strategy.get('back_button'))
            if back_button:
                back_button.click()
                time.sleep(2)
                log.info("Navigated back")
                return True
            return False
        except Exception as e:
            log.error(f"Failed to navigate back: {str(e)}")
            return False
