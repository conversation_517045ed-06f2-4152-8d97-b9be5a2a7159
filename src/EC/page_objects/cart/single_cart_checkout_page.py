import time
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.page_objects.base_page import BasePage
from src.config.weee.log_help import log


class SingleCartCheckoutPage(BasePage):
    strategies = {
        'Android': {
            'home': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Home")'),
            'home_product_item': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().resourceId("com.sayweee.weee:id/layout_product")'),
            'home_add_to_cart': (AppiumBy.XPATH, '//android.widget.ImageView[@content-desc="Add item to cart"]'),
            'zipcode': (AppiumBy.ID, 'com.sayweee.weee:id/iv_location_arrow'),
            'zipcode_01504': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Leverett,MA 01054")'),
            'popup_close': (AppiumBy.ACCESSIBILITY_ID, 'popUp close'),
            'explore': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Explore")'),
            'filter': (AppiumBy.ACCESSIBILITY_ID, 'Filter'),
            'pantry_filter': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().resourceId("com.sayweee.weee:id/iv_filter").instance(1)'),
            'local_delivery_filter':(AppiumBy.ACCESSIBILITY_ID, 'Local Delivery Unselected'),
            'cold_pack_filter':(),
            'apply_button': (AppiumBy.ID, 'com.sayweee.weee:id/tv_apply'),
            'add_to_cart': (AppiumBy.XPATH, '//android.widget.ImageView[@content-desc="Add item to cart"]'),

            # pantry购物车页面元素
            'cart_tab': (AppiumBy.ID, 'com.sayweee.weee:id/iv_cart'),

            # 为你推荐
            'recommendations': (AppiumBy.XPATH, '//android.widget.TextView[@text="Recommendations"]'),
            'recommendations_products': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().resourceId("com.sayweee.weee:id/layout_product")'),
            'recommendations_product_image': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().resourceId("com.sayweee.weee:id/iv_icon")'),
            'recommendations_product_title': (AppiumBy.XPATH, '//android.widget.TextView[@resource-id="com.sayweee.weee:id/tv_product_name"]'),
            'recommendations_product_price': (AppiumBy.XPATH, '//android.widget.TextView[@resource-id="com.sayweee.weee:id/tv_price"]'),
            'recommendations_product_favorite':(AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().description("Favorite")'),

            'pantry_title': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Pantry+")'),
            'delivery_date': (AppiumBy.ID, 'com.sayweee.weee:id/tv_delivery_date_cant_change'),
            'shipping_fee': (AppiumBy.ID, 'com.sayweee.weee:id/tv_delivery_fee_title'),

            # grocery购物车页面元素
            'local_delivery_title': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Local delivery")'),
            'delivery_by_weee_truck': (AppiumBy.ID, 'com.sayweee.weee:id/tv_sub_title'),
            'normal_cart': (AppiumBy.ID, 'com.sayweee.weee:id/mRecyclerView'),
            'grocery_products': (AppiumBy.XPATH, '//android.view.ViewGroup[@resource-id="com.sayweee.weee:id/layout_product"]'),

            'remove': (AppiumBy.ID, 'com.sayweee.weee:id/tv_remove'),
            'save_for_later': (AppiumBy.ID, 'com.sayweee.weee:id/tv_save_for_later'),
            'checkout_button': (AppiumBy.ID, 'com.sayweee.weee:id/tv_checkout'),
            # 安卓没遇到弹出upsell
            'upsell_continue': (AppiumBy.ID, 'Continue'),

            # pantry place order page
            'checkout_pantry_title': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Pantry+")'),
            'checkout_delivery_date': (AppiumBy.ID, 'com.sayweee.weee:id/tv_date'),
            'checkout_pantry_ship_info': (AppiumBy.ID, 'com.sayweee.weee:id/tv_content'),
            'next_week_3rd_day': (AppiumBy.XPATH, '((//androidx.recyclerview.widget.RecyclerView[@resource-id="com.sayweee.weee:id/rv_date"])[2]//android.widget.FrameLayout[@resource-id="com.sayweee.weee:id/layout"])[3]'),
            # 本周第一天
            'this_week_1st_day': (AppiumBy.XPATH, '((//androidx.recyclerview.widget.RecyclerView[@resource-id="com.sayweee.weee:id/rv_date"])[1]//(android.view.ViewGroup[@resource-id="com.sayweee.weee:id/layout_day"]))[1]'),

            # grocery place order page
            'checkout_grocery_title': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Local delivery")'),
            'checkout_grocery_delivery_date': (AppiumBy.ID, 'com.sayweee.weee:id/tv_support_change_date'),
            'guaranteed_delivery': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Guaranteed delivery")'),


            # checkout page
            'checkout_banner': (AppiumBy.XPATH, '//XCUIElementTypeStaticText[contains(@name, "more to your cart to get FREE delivery!")]'),

            # 底部结算按钮（灰色状态）
            'bottom_checkout_button': (AppiumBy.ID, 'com.sayweee.weee:id/tv_checkout'),
            'checkout_button_disabled': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "tv_checkout"`][2]'),
            'checkout_button_gray': (AppiumBy.IOS_PREDICATE, 'type == "XCUIElementTypeButton" AND name CONTAINS "Checkout"'),


            # 返回按钮
            'back_button': (AppiumBy.ACCESSIBILITY_ID, 'Return to previous page'),
            'checkout_back_to_cart_button': (AppiumBy.ACCESSIBILITY_ID, 'Return to previous page'),
            'upsell_back_button': (AppiumBy.ACCESSIBILITY_ID, 'Before you checkout')
        },
        'iOS': {
            'home': (AppiumBy.ACCESSIBILITY_ID, 'home'),
            'home_product_item': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeCell[`name == "item"`]'),
            'home_add_to_cart': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Add item to cart"`]'),
            'zipcode': (AppiumBy.ACCESSIBILITY_ID, 'layout_location'),
            'zipcode_01504': (AppiumBy.ACCESSIBILITY_ID, 'Leverett, MA 01054'),
            'popup_close': (AppiumBy.ACCESSIBILITY_ID, 'popUp close'),
            'explore': (AppiumBy.ACCESSIBILITY_ID, 'enki-category'),
            'filter': (AppiumBy.ACCESSIBILITY_ID, 'Sort Filter'),
            'pantry_filter': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "checkbox outline"`][1]'),
            'local_delivery_filter':(AppiumBy.ACCESSIBILITY_ID, 'Local Delivery'),
            'cold_pack_filter':(),
            'apply_button': (AppiumBy.XPATH, '//XCUIElementTypeButton[contains(@name,"Apply")]'),
            'add_to_cart': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Add item to cart"`]'),

            # pantry购物车页面元素
            'cart_tab': (AppiumBy.ACCESSIBILITY_ID, 'layout_cart'),

            # 为你推荐
            'recommendations': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Recommendations"`][1]'),
            'recommendations_products': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeCell[`name == "layout_product"`]'),
            'recommendations_product_image': (AppiumBy.XPATH, '//XCUIElementTypeImage[@name]'),
            'recommendations_product_title': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "tv_product_name"`]'),
            'recommendations_product_price': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "tv_price"`]'),
            'recommendations_product_favorite': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "iv_collect"`]'),

            'pantry_title': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Pantry+"`]'),
            'delivery_date': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "layout_delivery_date"`]'),
            'shipping_fee': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Shipping fee"`]'),

            # grocery购物车页面元素
            'local_delivery_title': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Local delivery"`]'),
            'delivery_by_weee_truck': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Delivered by Weee! Truck"`]'),
            'normal_cart': (AppiumBy.XPATH, '//XCUIElementTypeCell[@name="normal"]/XCUIElementTypeTable'),
            'grocery_products': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeCell[`name == "layout_product"`]'),

            'remove': (AppiumBy.ACCESSIBILITY_ID, 'Remove'),
            'save_for_later': (AppiumBy.ACCESSIBILITY_ID, 'Save for later'),
            'checkout_button': (AppiumBy.ACCESSIBILITY_ID, 'tv_checkout'),
            'upsell_continue': (AppiumBy.ACCESSIBILITY_ID, 'Continue'),

            # pantry place order page
            'checkout_pantry_title': (AppiumBy.ACCESSIBILITY_ID, 'Pantry+'),
            'checkout_delivery_date': (AppiumBy.ACCESSIBILITY_ID, 'layout_delivery_date'),
            # XCUIElementTypeCell[2] 取的是下周的容器
            'next_week_3rd_day': (AppiumBy.XPATH, '(//XCUIElementTypeCell[2]//XCUIElementTypeButton[@name])[2]'),
            # 本周第一天
            'this_week_1st_day': (AppiumBy.XPATH, '(//XCUIElementTypeTable/XCUIElementTypeCell[1]//XCUIElementTypeButton[@name])[1]'),
            'checkout_pantry_ship_info': (AppiumBy.ID, 'Shipping via FedEx, UPS, etc.'),

            # grocery place order page
            'checkout_grocery_title': (AppiumBy.ACCESSIBILITY_ID, 'Local delivery'),
            'checkout_grocery_delivery_date':(AppiumBy.ACCESSIBILITY_ID, 'layout_delivery_date'),
            'guaranteed_delivery': (AppiumBy.ACCESSIBILITY_ID, 'Guaranteed delivery'),

            
            # checkout page
            'checkout_banner': (AppiumBy.XPATH, '//XCUIElementTypeStaticText[contains(@name, "more to your cart to get FREE delivery!")]'),

            # 底部结算按钮（灰色状态）
            'bottom_checkout_button': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "tv_checkout"`][1]'),
            'checkout_button_disabled': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "tv_checkout"`][2]'),
            'checkout_button_gray': (AppiumBy.IOS_PREDICATE, 'type == "XCUIElementTypeButton" AND name CONTAINS "Checkout"'),
            

            # 返回按钮
            'back_button': (AppiumBy.ACCESSIBILITY_ID, 'Back'),
            'upsell_back_button': (AppiumBy.ACCESSIBILITY_ID, 'Before you checkout')
        }
    }

    def __init__(self, driver, platform):
        self.platform = platform
        self.driver = driver
        self.strategy = self.strategies.get(self.platform)

    def switch_zipcode(self):
        self.find_element(self.driver, self.strategy.get('zipcode')).click()
        zipcode_01504 = self.find_element(self.driver, self.strategy.get('zipcode_01504'))
        if zipcode_01504:
            zipcode_01504.click()
        else:
            log.info('没有找到zipcode_01504')
        time.sleep(5)

    def add_filter_products_to_cart(self, filter_type='pantry_filter'):
        self.swipe_screen(_driver=self.driver, distance=0.5)
        # 1. 先进入explore page，再点击filter,选择pantry
        self.find_element(self.driver, self.strategy.get('explore')).click()
        if self.platform == 'Android':
            time.sleep(10)
        # 点击filter铵钮并选择filter
        self.find_element(self.driver, self.strategy.get('filter')).click()
        # 安卓比较慢
        if self.platform == 'Android':
            time.sleep(3)
        _filter = self.find_element(self.driver, self.strategy.get(filter_type))
        if _filter:
            _filter.click()
        else:
            log.warning('没有找到filter或filter已勾选')
        time.sleep(2)
        self.find_element(self.driver, self.strategy.get('apply_button')).click()
        time.sleep(5)
        # 2. 添加商品到购物车
        add_pantry_to_cart_buttons = self.find_elements(self.driver, self.strategy.get('add_to_cart'))
        if add_pantry_to_cart_buttons:
            add_pantry_to_cart_buttons[0].click()
        else:
            raise Exception('没有找到pantry商品')

    def navigate_to_cart(self):
        """导航到购物车页面"""
        try:
            cart_tab = self.find_element(self.driver, self.strategy.get('cart_tab'))
            if cart_tab:
                cart_tab.click()
                time.sleep(3)  # 等待购物车页面加载
                log.info("Navigated to cart page")
                return True
            return False
        except Exception as e:
            log.error(f"Failed to navigate to cart: {str(e)}")
            return False


    def check_pantry_cart_exists(self):
        a = self.find_element(self.driver, self.strategy.get('pantry_title')).is_enabled()
        b = self.find_element(self.driver, self.strategy.get('delivery_date')).is_displayed()
        c = self.find_element(self.driver, self.strategy.get('shipping_fee')).is_displayed()
        d = self.find_element(self.driver, self.strategy.get('remove')).is_displayed()
        e = self.find_element(self.driver, self.strategy.get('save_for_later')).is_displayed()
        f = self.find_element(self.driver, self.strategy.get('checkout_button')).is_displayed()

        return a and b and c and d and e and f

    def check_grocery_cart_exists(self):
        a = self.find_element(self.driver, self.strategy.get('local_delivery_title')).is_enabled()
        b = self.find_element(self.driver, self.strategy.get('delivery_by_weee_truck'))
        _b = b.is_enabled()
        c = self.find_element(self.driver, self.strategy.get('normal_cart')).is_displayed()
        d = self.find_element(self.driver, self.strategy.get('grocery_products')).is_displayed()
        g = self.find_element(self.driver, self.strategy.get('remove')).is_displayed()
        e = self.find_element(self.driver, self.strategy.get('save_for_later')).is_displayed()
        f = self.find_element(self.driver, self.strategy.get('checkout_button')).is_displayed()
        return a and _b and c and d and e and f and g

    def click_checkout_button(self):
        """点击结算按钮"""
        try:
            checkout_btn = self.find_element(self.driver, self.strategy.get('checkout_button'))
            if checkout_btn:
                checkout_btn.click()
                time.sleep(3)  # 等待页面跳转
                if self.find_element(self.driver, self.strategy.get('upsell_continue')):
                    self.find_element(self.driver, self.strategy.get('upsell_continue')).click()
                log.info("Clicked checkout button")
                return True
            return False
        except Exception as e:
            log.error(f"Failed to click checkout button: {str(e)}")
            return False

    def check_pantry_checkout_page(self):
        a = self.find_element(self.driver, self.strategy.get('checkout_pantry_title')).is_displayed()
        b = self.find_element(self.driver, self.strategy.get('checkout_delivery_date')).is_displayed()
        c = self.find_element(self.driver, self.strategy.get('checkout_pantry_ship_info')).is_displayed()
        return a and b and c

    def check_grocery_checkout_page(self):
        a = self.find_element(self.driver, self.strategy.get('checkout_grocery_title')).is_displayed()
        b = self.find_element(self.driver, self.strategy.get('checkout_grocery_delivery_date')).is_displayed()
        # 如果不往下划动，下面的元素可能被遮挡
        self.swipe_screen(self.driver, 0.4)
        c = self.find_element(self.driver, self.strategy.get('guaranteed_delivery')).is_displayed()
        return a and b and c

    def switch_delivery_date(self):
        if self.platform == 'iOS':
            self.find_element(self.driver, self.strategy.get('checkout_delivery_date')).click()
        else:
            self.find_element(self.driver, self.strategy.get('checkout_grocery_delivery_date')).click()
        time.sleep(3)
        self.find_element(self.driver, self.strategy.get('next_week_3rd_day')).click()
        time.sleep(3)


    def check_delivery_date_switched(self):
        _a = self.find_element(self.driver, self.strategy.get('home'))
        _a.click()
        a = self.find_elements(self.driver, self.strategy.get('home_product_item'))
        b = self.find_element(self.driver, self.strategy.get('home_add_to_cart'))
        c = self.find_element(self.driver, self.strategy.get('cart_tab'))
        return a and b and c

    def switch_original_delivery_date(self):
        # 首页需要等待较长时间
        time.sleep(5)
        self.find_element(self.driver, self.strategy.get('zipcode')).click()
        time.sleep(3)
        self.find_element(self.driver, self.strategy.get('this_week_1st_day')).click()
        time.sleep(3)

    def cart_recommendations(self):
        self.find_element(self.driver, self.strategy.get('cart_tab')).click()
        time.sleep(3)
        # 往下稍微翻一下，否则title, price, favorite可能被遮挡
        self.swipe_screen(self.driver, 0.5)
        self.swipe_screen_until(self.driver, self.strategy.get('recommendations'), 0.4)
        # 寻找推荐商品
        recommendations_products = self.find_elements(self.driver, self.strategy.get('recommendations_products'))
        self.check_recommendations_product(recommendations_products)

        self.swipe_screen(self.driver, 0.4)
        recommendations_products = self.find_elements(self.driver, self.strategy.get('recommendations_products'))
        self.check_recommendations_product(recommendations_products)



    def check_recommendations_product(self, _recommendations_products):
        if _recommendations_products:
            for index, item in enumerate(_recommendations_products):
                if index == 1:
                    break
                a = item.find_element(*self.strategy.get('recommendations_product_image'))
                b =  item.find_element(*self.strategy.get('recommendations_product_title'))
                c = item.find_element(*self.strategy.get('recommendations_product_price'))
                d = item.find_element(*self.strategy.get('recommendations_product_favorite'))

                assert item.find_element(*self.strategy.get('recommendations_product_image')).is_displayed()
                assert item.find_element(*self.strategy.get('recommendations_product_title')).is_displayed()
                assert item.find_element(*self.strategy.get('recommendations_product_price')).is_displayed()
                assert item.find_element(*self.strategy.get('recommendations_product_favorite')).is_displayed()

    def go_back(self):
        """返回上一页"""
        if self.platform == 'iOS':
            self.driver.tap([(13, 55)])
            time.sleep(2)
            self.driver.tap([(13, 55)])
            time.sleep(2)
        else:
            self.find_element(self.driver, self.strategy.get('checkout_back_to_cart_button')).click()
            time.sleep(2)
            self.find_element(self.driver, self.strategy.get('back_button')).click()

    def go_back_home(self):
        if self.platform == 'Android':
            self.find_element(self.driver, self.strategy.get('back_button')).click()
        else:
            self.driver.tap([(13, 55)])





