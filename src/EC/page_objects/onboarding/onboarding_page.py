import time
from appium.webdriver.common.appiumby import AppiumBy

from src.EC.page_objects.base_page import BasePage
from src.config.weee.log_help import log


class Onboarding(BasePage):
    strategies = {
        'Android': {
            'switch_language': (AppiumBy.ID, 'com.sayweee.weee:id/tv_language'),
            'english': (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("English")'),
            'shop_now': (AppiumBy.ID, 'com.sayweee.weee:id/tv_commit'),
            'skip': (AppiumBy.ID, 'com.sayweee.weee:id/tv_skip'),
            "enter_zipcode": (AppiumBy.ID, 'com.sayweee.weee:id/et_input'),
            "zipcode_next": (AppiumBy.ID, 'com.sayweee.weee:id/tv_go'),
            "chinsese": (AppiumBy.ANDROID_UIAUTOMATOR, 'new UiSelector().text("Chinese")'),
            "home_add_to_cart": (AppiumBy.XPATH, '(//android.widget.ImageView[@resource-id="com.sayweee.weee:id/iv_edit_right"])'),
            "cart_button": (AppiumBy.ID,"com.sayweee.weee:id/iv_cart"),
            "checkout_button": (AppiumBy.ID, "com.sayweee.weee:id/tv_checkout"),
            # 选择购物车界面的checkout按钮
            "checkout_button_1": (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "tv_checkout"`][1]'),
            "upsell_continue": (AppiumBy.ACCESSIBILITY_ID, 'Continue'),
            "enter_your_email": (AppiumBy.ID, "com.sayweee.weee:id/et_account"),
            "email_next": (AppiumBy.ID, "com.sayweee.weee:id/tv_account_next"),
            "enter_your_password": (AppiumBy.ID, "com.sayweee.weee:id/et_password"),
            "password_next": (AppiumBy.ID, "com.sayweee.weee:id/tv_next"),
            "place_order": (AppiumBy.ID, "com.sayweee.weee:id/tv_checkout"),
            "home_account": (AppiumBy.XPATH, '//android.widget.TextView[@resource-id="com.sayweee.weee:id/txt" and @text="Account"]'),
            "login_button": (AppiumBy.ID, "com.sayweee.weee:id/btn_login"),
            "pick_price_skip": (AppiumBy.ACCESSIBILITY_ID, "Skip"),
            # 首页home button
            "home": (AppiumBy.XPATH, '//android.widget.TextView[@resource-id="com.sayweee.weee:id/txt" and @text="Home"]'),
            "popup_close": (None,)

        },
        'iOS': {
            'switch_language': (AppiumBy.XPATH,
                                '//XCUIElementTypeWindow/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther/XCUIElementTypeOther[3]/XCUIElementTypeImage[2]'),
            'english': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "English"`]'),
            'shop_now': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Shop now"`]'),
            'skip': (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Skip"`]'),
            "enter_zipcode": (AppiumBy.IOS_PREDICATE, 'value == "Enter your zip code"'),
            "zipcode_next": (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeStaticText[`name == "Next"`]'),
            "chinsese": (AppiumBy.ID, 'Chinese'),
            # 此为ios专有
            "personalize_your_journey": (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "Continue"`]'),
            "not_now": (AppiumBy.ACCESSIBILITY_ID, 'Not now'),
            "home_add_to_cart": (AppiumBy.XPATH, '//XCUIElementTypeButton[@name="Add item to cart"]'),
            # ios cart没有标准的访问符
            "cart_button": (AppiumBy.ACCESSIBILITY_ID, 'layout_cart'),
            "checkout_button": (AppiumBy.ACCESSIBILITY_ID, 'tv_checkout'),
            # 选择全部购物车按钮
            "select_all": (AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeButton[`name == "iv_check"`][1]'),
            "enter_your_email": (AppiumBy.ACCESSIBILITY_ID, 'et_account'),
            "email_next": (AppiumBy.ACCESSIBILITY_ID, 'tv_account_next'),
            "enter_your_password": (AppiumBy.ACCESSIBILITY_ID, 'et_password'),
            "password_next": (AppiumBy.ACCESSIBILITY_ID, 'tv_next'),
            # 这个名字叫得有点奇怪
            "place_order": (AppiumBy.ACCESSIBILITY_ID, 'tv_checkout'),
            "home_account": (AppiumBy.ACCESSIBILITY_ID, 'me'),
            "login_button": (AppiumBy.ACCESSIBILITY_ID, 'Log In or Sign Up'),
            # ios没有这个页面
            "pick_price_skip": (AppiumBy.ID, ''),
            # 首页home button
            "home": (AppiumBy.ACCESSIBILITY_ID, 'home'),
            "popup_close": (AppiumBy.ACCESSIBILITY_ID, 'popUp close')
        }
    }


    def __init__(self, driver, platform):
        self.platform = platform
        self.driver = driver
        self.strategy = self.strategies.get(self.platform)

    def onboarding(self, lang="english"):
        if self.platform == "Android":
            self.click(self.driver, self.strategy.get('switch_language'))
            self.click(self.driver, self.strategy.get(lang))
        self.click(self.driver, self.strategy.get('shop_now'))
        self.click(self.driver, self.strategy.get('skip'))
        self.click(self.driver, self.strategy.get('enter_zipcode'))
        self.send_keys(self.driver, self.strategy.get('enter_zipcode'), '98011')
        self.click(self.driver, self.strategy.get('zipcode_next'))
        self.click(self.driver, self.strategy.get("chinsese"))
        time.sleep(5)
        if self.platform == 'iOS':
            personalize_journey = self.find_element(self.driver, self.strategy.get("personalize_your_journey"))
            if personalize_journey:
                personalize_journey.click()
        time.sleep(15)
        not_now = self.find_element(self.driver, self.strategy.get("not_now"))
        if not_now:
            not_now.click()
        # 安卓加载相当慢，可能与手机相关
        time.sleep(20)
        log.info("Login acti-on completed")


    def add_to_cart_on_home(self):
        self.driver.swipe(500, 500, 500, 350)
        add_to_cart_button = self.find_elements(self.driver, self.strategy.get("home_add_to_cart"))
        log.info(f"add_to_cart_button===> {len(add_to_cart_button)}")
        for index, i in enumerate(add_to_cart_button):
            # 这里安卓与ios有一些不同，向下滑动后安卓只有2个端口，ios有很多
            i.click()
            if index == 2:
                break
            time.sleep(5)

    def goto_cart_and_checkout(self):
        self.click(self.driver, self.strategy.get("cart_button"))
        self.click(self.driver, self.strategy.get("checkout_button"))
        # multi cart
        select_all = self.find_element(self.driver, self.strategy.get("select_all"))
        if select_all:
            select_all_location = self.find_element(self.driver, self.strategy.get("select_all")).location
            self.driver.tap([(select_all_location.get("x"), select_all_location.get("y"))])
            self.find_element(self.driver, self.strategy.get("checkout_button_1")).click()

        # upsell
        time.sleep(3)
        upsell_continue = self.find_element(self.driver, self.strategy.get("upsell_continue"))
        if upsell_continue:
            upsell_continue.click()
            self.click(self.driver, self.strategy.get("checkout_button"))

        time.sleep(3)



    def login_with_email(self, email, password):
        self.driver.tap([(500, 500)])
        self.click(self.driver, self.strategy.get("enter_your_email"))
        self.send_keys(self.driver, self.strategy.get("enter_your_email"), email)
        self.click(self.driver, self.strategy.get("email_next"))
        time.sleep(3)
        self.driver.tap([(500, 500)])
        self.driver.tap([(500, 500)])
        self.send_keys(self.driver, self.strategy.get("enter_your_password"), password)
        self.click(self.driver, self.strategy.get("password_next"))
        time.sleep(5)

    def checkout_after_login(self):
        self.click(self.driver, self.strategy.get("checkout_button"))
        time.sleep(5)
        self.click(self.driver, self.strategy.get("place_order"))


