from typing import List, Union

from src.config.weee.log_help import log
from appium.webdriver.webelement import WebElement as MobileWebElement
from selenium.webdriver.support.wait import WebDriverWait

class Base:

    def find_element(self, _driver, ele, timeout=10, p_frequency=1) -> Union[MobileWebElement, List[MobileWebElement], None]:
        try:
            return WebDriverWait(_driver, timeout, p_frequency).until(
                lambda d: _driver.find_element(*ele)
            )
        except Exception as e:
            log.warning(f"Failed to find element: {ele}")
            return None

    def find_elements(self, _driver, ele, timeout=10, p_frequency=1) -> List[MobileWebElement] | None:
        try:
            return WebDriverWait(_driver, timeout, p_frequency).until(
                lambda d: _driver.find_elements(*ele)
            )
        except Exception as e:
            log.warning(f"Failed to find element: {ele}")
            return None

    def click(self, _driver, ele):
        element = self.find_element(_driver, ele)
        element.click()
        log.info(f"Clicked element: {ele}")

    def send_keys(self, _driver, ele, text):
        element = self.find_element(_driver, ele)
        element.clear()
        element.send_keys(text)
        log.debug(f"Entered text '{text}' into {ele}")

    def swipe_screen(self, _driver, distance=0.8, direction='up', duration=1000):
        size = _driver.get_window_size()
        width, height = size['width'], size['height']
        x_center = width * 0.5
        if direction.lower() == 'up':
            # 向上滑动一个屏幕高度
            _driver.swipe(x_center, height * distance, x_center, height * 0.2, duration)
        elif direction.lower() == 'down':
            # 向下滑动一个屏幕高度
            _driver.swipe(x_center, height * 0.2, x_center, height * 0.8, duration)
        else:
            raise ValueError("Unsupported direction. Use 'up' or 'down'.")

    def swipe_screen_until(self, _driver, ele, distance=0.8, direction='up', duration=1000):
        size = _driver.get_window_size()
        width, height = size['width'], size['height']
        x_center = width * 0.5
        if direction.lower() == 'up':
            # 向上滑动一个屏幕高度
            for i in range(10):
                if self.find_element(_driver, ele):
                    break
                _driver.swipe(x_center, height * distance, x_center, height * 0.2, duration)
        elif direction.lower() == 'down':
            # 向下滑动一个屏幕高度
            for i in range(10):
                if self.find_element(_driver, ele):
                    break
                _driver.swipe(x_center, height * 0.2, x_center, height * 0.8, duration)
        else:
            raise ValueError("Unsupported direction. Use 'up' or 'down'.")

    def scroll_to_element(self, _driver, source, to, duration=1000):
        if source and to:
            _driver.scroll(source, to, duration)
        else:
            raise ValueError("Source and destination elements are required for scrolling.")

