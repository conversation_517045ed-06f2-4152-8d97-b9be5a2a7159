import datetime

from src.config.base_config import BASE_URL
from src.utils.HttpRequest import HttpRequest as HR


def query_preorder_v5(headers, cart_domain: str = "grocery"):
    """# query_preorder_v5"""
    res = HR.request({
        "method": "get",
        "path": BASE_URL + "/ec/so/porder/v5",
        "headers": headers,
        "param": {"cart_domain": cart_domain}
    })

    return res.json()


def remove_cart_v3(headers, product_id, biz_type: str = "normal",
                   date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
                   refer_type: str = "normal",
                   source: str = "portal-recommend"):
    """#购物车页面删除购物车商品 v3"""
    data = [{"product_id": product_id,
             "biz_type": biz_type,
             "delivery_date": date,
             "product_key": product_id,
             "new_source": "",  # 埋点
             "source": source,  # 加购来源
             "quantity": 0,
             "refer_type": refer_type
             }]
    res = HR.request({
        "method": "put",
        "path": BASE_URL + "/ec/so/porder/items/v3",
        "headers": headers,
        "request_body": data
    })

    return res.json()


def porder_items_v3(
        headers, product_id, biz_type: str = "normal",
        date: str = (datetime.date.today() + datetime.timedelta(days=1)).strftime('%Y-%m-%d'),
        refer_type: str = "normal", is_pantry: bool = False, is_alcohol: bool = False,
        item_type: str = "", min_order_quantity: int = 1,
        is_mkpl: bool = False, source_store: str = "cn",
        source: str = "portal-recommend", quantity: int = 1, refer_value: str = None,
        vender_id: int = None,
        volume_price_support: bool = False
):
    """# 非购物车页面加购使用，加购接口v3"""
    data = [{"product_id": product_id,
             "biz_type": biz_type,
             "delivery_date": date,
             "is_pantry": is_pantry,
             "is_alcohol": is_alcohol,
             "is_mkpl": is_mkpl,
             "item_type": item_type,
             "min_order_quantity": min_order_quantity,
             "new_source": "",  # 埋点
             "positionInfoT2": "",  # 埋点
             "source": source,  # 加购来源
             "source_store": source_store,
             "quantity": quantity,
             "refer_type": refer_type,
             "refer_value": refer_value,
             "vender_id": vender_id,  # mkpl 商品传
             "volume_price_support": volume_price_support
             }]

    res = HR.request({
        "method": "put",
        "path": BASE_URL + "/ec/so/porder/items/v3",
        "headers": headers,
        "request_body": data
    })

    return res.json()

def remove_save_for_later(headers, product_keys):
    """#购物车页面save4later商品"""
    data = {
        "product_keys": [product_keys]
    }

    res = HR.request({
        "method": "delete",
        "path": BASE_URL + "/ec/so/save4later",
        "headers": headers,
        "request_body": data
    })
    return res.json()
