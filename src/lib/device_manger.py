from appium import webdriver
from appium.options.ios import XCUITestOptions
from selenium.common.exceptions import WebDriverException

from src.config.devices import *
from src.lib.utils.logger import Logger


class DeviceManager:
    def __init__(self, platform):
        self.logger = Logger(__name__)
        self.platform = platform
        self.driver = None



    def initialize_driver(self, is_installed=False):
        try:
            # 平台特有配置
            if is_installed:
                if self.platform == 'Android':
                    caps = caps_weee_android
                else:
                    caps = caps_weee_ios
            else:
                if self.platform == 'Android':
                    caps = caps_android_onboarding
                else:
                    caps = caps_ios_onboarding


            #根据不同的平台启用不同的driver
            self.driver = webdriver.Remote(
                command_executor='http://127.0.0.1:4723',
                options=XCUITestOptions().load_capabilities(caps)
            )
            return self.driver
        except WebDriverException as e:
            self.logger.error(f"Failed to initialize driver: {str(e)}")
            raise

