# QA UI Android iOS Automation Project - Augment Memories

## Project Overview
This is a comprehensive mobile UI automation testing framework for the **Weee!** e-commerce application, supporting both Android and iOS platforms. The project uses Python with Appium for mobile automation testing and includes API testing capabilities.

## Project Structure

### Core Directories
- **`src/`** - Main source code directory
  - **`EC/`** - E-commerce test modules
    - **`tests/`** - Test cases organized by functionality
    - **`page_objects/`** - Page Object Model implementation
    - **`conftest.py`** - Pytest configuration and fixtures
  - **`api/`** - API testing modules
  - **`config/`** - Configuration files and settings
  - **`common/`** - Common utilities and base classes
  - **`lib/`** - Library modules for device management
  - **`utils/`** - Utility classes (HTTP requests, etc.)

### Key Configuration Files
- **`run.py`** - Main test runner script
- **`pytest.ini`** - Pytest configuration with custom markers
- **`requirement.txt`** - Python dependencies
- **`.env`** - Environment variables
- **`src/config/global_settings.json`** - Global test settings
- **`src/config/devices.py`** - Device capabilities configuration

## Test Categories

### 1. Onboarding Tests (`src/EC/tests/onboarding/`)
- User registration and login flows
- First-time user experience
- Cart and checkout without login
- Login during checkout process

### 2. Product Tests (`src/EC/tests/product/`)
- Product detail page functionality
- Video features on product pages
- Product recommendation filters

### 3. Explore Tests (`src/EC/tests/explore/`)
- Product recommendation filter verification
- Deals, new arrivals, best sellers sections
- Search and filtering functionality

### 4. Account Tests (`src/EC/tests/account/`)
- Order management
- Account settings
- Order status verification

## Page Object Model

### Base Classes
- **`src/common/base.py`** - Base class with common element operations
- **`src/EC/page_objects/base_page.py`** - Base page object class
- **`src/EC/tests/base_case.py`** - Base test case class

### Page Objects
- **`home_page.py`** - Home page elements and actions
- **`product_detail_page.py`** - Product detail page
- **`explore_page.py`** - Explore/search page
- **`account_page.py`** - Account management page
- **`onboarding_page.py`** - Onboarding flow pages
- **`login_page.py`** - Login/signup pages

## API Testing Framework

### API Modules
- **`src/api/cart.py`** - Shopping cart operations
- **`src/api/order.py`** - Order management APIs
- **`src/api/porder.py`** - Pre-order operations
- **`src/api/zipcode.py`** - Location/zipcode management
- **`src/api/commonapi/`** - Common API functions
  - **`get_header.py`** - Authentication and headers
  - **`commfunc.py`** - Common utility functions

### HTTP Request Handling
- **`src/utils/HttpRequest.py`** - HTTP request wrapper class
- Supports GET, POST, PUT, DELETE methods
- JSON, form, and multipart data handling

## Device Management

### Supported Platforms
- **Android** - UiAutomator2 automation
- **iOS** - XCUITest automation

### Device Configuration
- **`src/lib/device_manger.py`** - Device initialization and management
- **`src/config/devices.py`** - Platform-specific capabilities
- **`src/config/devices.yml`** - Device configuration templates

### Appium Integration
- Local Appium server on `http://127.0.0.1:4723`
- Automatic driver initialization and cleanup
- Platform-specific element strategies

## Test Execution

### Pytest Markers
- **`smoke`** - Smoke test cases
- **`android_onboarding`** - Android onboarding tests
- **`h5cart`** - H5 cart functionality
- **`h5smoke`** - H5 smoke tests
- **`explore`** - Explore page tests
- **`filter`** - Filter functionality tests
- **`orders`** - Order management tests

### Test Runner
```python
# Default execution
python run.py  # Runs android_onboarding or smoke tests

# Custom marker execution
mark=smoke python run.py  # Runs specific marker tests
```

## Reporting and Logging

### Test Reporting
- **Allure** integration for detailed test reports
- **`src/config/weee/send_report.py`** - Report generation and distribution
- Video recording for test failures
- Screenshot capture on errors

### Logging
- **`src/config/weee/log_help.py`** - Logging configuration
- **Loguru** library for structured logging
- Test execution tracking and debugging

## Database Integration
- **MySQL** connection for test data management
- **`src/config/weee/mysqlUtil.py`** - Database utilities
- **`src/config/weee/mysqlconnection.py`** - Connection management

## Environment Configuration

### Base URLs
- **Production**: `https://api.sayweee.net`
- **Test**: `https://www.sayweee.com/en`
- Environment-specific configuration via environment variables

### Authentication
- Test user accounts for automation
- Token-based authentication
- Session management for API calls

## Key Features

### Cross-Platform Support
- Unified test framework for Android and iOS
- Platform-specific element strategies
- Consistent API across platforms

### Hybrid Testing
- UI automation with Appium
- API testing for backend verification
- Combined UI + API test scenarios

### CI/CD Integration
- Jenkins integration with build numbers
- Environment variable configuration
- Automated test execution and reporting

## Dependencies

### Core Libraries
- **pytest** - Test framework
- **Appium-Python-Client** - Mobile automation
- **allure-pytest** - Test reporting
- **requests** - HTTP client
- **loguru** - Logging
- **selenium** - WebDriver support

### Additional Tools
- **boto3** - AWS integration
- **paramiko** - SSH connections
- **PyMySQL** - Database connectivity
- **jmespath** - JSON path queries

## Best Practices

### Test Organization
- Page Object Model for maintainability
- Separation of UI and API tests
- Modular test design with fixtures

### Error Handling
- Comprehensive exception handling
- Automatic screenshot/video capture
- Detailed error logging and reporting

### Code Quality
- Type hints for better code documentation
- Consistent naming conventions
- Modular and reusable components
