{"name": "PDP页面Reviews栏基础展示测试", "status": "passed", "description": "\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证Reviews栏是否展示\n        3. 验证总review数是否正确显示\n        4. 验证Customers say栏是否展示\n        5. 验证AI-generated文本样式\n        ", "parameters": [{"name": "setup", "value": "('test_reviews_section_display',)"}], "start": 1749171818052, "stop": 1749171872745, "uuid": "6d7ef3c8-6d23-4a7b-a827-9a04ff64704e", "historyId": "f687fa249e9502191962abf9beab7816", "testCaseId": "ebd254721491e4204b01ca8c9db8c493", "fullName": "src.EC.tests.product.test_112777_pdp_reviews_selling_points.TestPDPReviewsSellingPoints#test_112777_reviews_section_display", "labels": [{"name": "story", "value": "PDP页面Reviews栏和卖点关键词功能测试"}, {"name": "tag", "value": "pdp_reviews"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.product"}, {"name": "suite", "value": "test_112777_pdp_reviews_selling_points"}, {"name": "subSuite", "value": "TestPDPReviewsSellingPoints"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "53333-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.product.test_112777_pdp_reviews_selling_points"}]}