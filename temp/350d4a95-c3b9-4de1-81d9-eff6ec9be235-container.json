{"uuid": "57f09613-85ce-4758-ac9d-c89dce756f29", "children": ["7d2abece-22d3-4b4a-8c4b-b29200d7b1cf"], "befores": [{"name": "get_onboarding_driver", "status": "broken", "statusDetails": {"message": "KeyboardInterrupt\n", "trace": "  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/pluggy/_callers.py\", line 77, in _multicall\n    res = hook_impl.function(*args)\n          ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/_pytest/fixtures.py\", line 1123, in pytest_fixture_setup\n    result = call_fixture_func(fixturefunc, request, kwargs)\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/_pytest/fixtures.py\", line 895, in call_fixture_func\n    fixture_result = next(generator)\n                     ^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/src/EC/conftest.py\", line 104, in get_onboarding_driver\n    driver = DeviceManager(os.getenv(\"platform\", \"Android\")).initialize_driver()\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/src/lib/device_manger.py\", line 33, in initialize_driver\n    self.driver = webdriver.Remote(\n                  ^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/webdriver.py\", line 229, in __init__\n    super().__init__(\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py\", line 209, in __init__\n    self.start_session(capabilities)\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/webdriver.py\", line 321, in start_session\n    response = self.execute(RemoteCommand.NEW_SESSION, w3c_caps)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py\", line 346, in execute\n    response = self.command_executor.execute(driver_command, params)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/remote_connection.py\", line 300, in execute\n    return self._request(command_info[0], url, body=data)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/remote_connection.py\", line 321, in _request\n    response = self._conn.request(method, url, body=body, headers=headers)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/urllib3/request.py\", line 81, in request\n    return self.request_encode_body(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/urllib3/request.py\", line 173, in request_encode_body\n    return self.urlopen(method, url, **extra_kw)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/urllib3/poolmanager.py\", line 376, in urlopen\n    response = conn.urlopen(method, u.request_uri, **kw)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 715, in urlopen\n    httplib_response = self._make_request(\n                       ^^^^^^^^^^^^^^^^^^^\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 467, in _make_request\n    six.raise_from(e, None)\n  File \"<string>\", line 3, in raise_from\n  File \"/Users/<USER>/qa-ui-android-ios/qa-ui-android1/lib/python3.12/site-packages/urllib3/connectionpool.py\", line 462, in _make_request\n    httplib_response = conn.getresponse()\n                       ^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.12/3.12.3/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/client.py\", line 1428, in getresponse\n    response.begin()\n  File \"/opt/homebrew/Cellar/python@3.12/3.12.3/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/client.py\", line 331, in begin\n    version, status, reason = self._read_status()\n                              ^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.12/3.12.3/Frameworks/Python.framework/Versions/3.12/lib/python3.12/http/client.py\", line 292, in _read_status\n    line = str(self.fp.readline(_MAXLINE + 1), \"iso-8859-1\")\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File \"/opt/homebrew/Cellar/python@3.12/3.12.3/Frameworks/Python.framework/Versions/3.12/lib/python3.12/socket.py\", line 707, in readinto\n    return self._sock.recv_into(b)\n           ^^^^^^^^^^^^^^^^^^^^^^^\n"}, "start": 1750815668488, "stop": 1750815669288}], "start": 1750815668488, "stop": 1750815669560}