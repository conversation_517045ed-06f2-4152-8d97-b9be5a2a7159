{"name": "onboarding-先购买checkout时登陆", "status": "skipped", "description": "先加购，checkout时再登陆", "parameters": [{"name": "setup", "value": "('test_000_onboarding_without_login',)"}], "start": 1750815665082, "stop": 1750815665082, "uuid": "7d2abece-22d3-4b4a-8c4b-b29200d7b1cf", "testCaseId": "97107ccb9079c24e48c6229d6e599e2b", "fullName": "src.EC.tests.onboarding.test_000_onboarding.TestOnboarding#test_000_onboarding_without_login"}