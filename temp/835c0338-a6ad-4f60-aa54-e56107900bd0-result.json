{"name": "视频栏分类排序测试", "status": "passed", "description": "\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证视频栏分类排序是否为 review > recipes > unboxing\n        ", "parameters": [{"name": "setup", "value": "('test_002_video_categories_order',)"}], "start": 1749191446667, "stop": 1749191448915, "uuid": "eb148740-6850-4f9c-b1bb-4ec161497554", "historyId": "e9284ec231e0ae8b188f5d8955b37b5f", "testCaseId": "7560e7e9e450793ca2476a1e1106327a", "fullName": "src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures#test_112812_video_categories_order", "labels": [{"name": "story", "value": "iOS端PDP视频功能测试"}, {"name": "tag", "value": "pdp_video"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.product"}, {"name": "suite", "value": "test_112812_pdp_video_features"}, {"name": "subSuite", "value": "TestPDPVideoFeatures"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "59442-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.product.test_112812_pdp_video_features"}]}