{"name": "PDP页面发布视频入口测试", "status": "broken", "statusDetails": {"message": "AttributeError: 'ProductDetailPage' object has no attribute 'check_publish_video_tip_exists'", "trace": "self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x108ee3560>\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"308b8727-6e4d-4094-853f-d4887fe48555\")>\nget_platform = 'iOS', setup = None\n\n    @allure.title(\"PDP页面发布视频入口测试\")\n    @pytest.mark.parametrize('setup', [('test_004_publish_video_entry', )], indirect=True)\n    def test_004_publish_video_entry(self, get_driver, get_platform, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证PDP下方是否展示发布视频tip词\n        3. 验证发布视频按钮是否可点击\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n    \n        # 准备测试环境\n    \n        # 导航到产品详情页\n        navigation_success = self.navigate_to_product_detail(d, platform)\n        assert navigation_success, \"Failed to navigate to product detail page\"\n    \n        # 初始化产品详情页对象\n        pdp_page = ProductDetailPage(d, platform)\n    \n        # 向下滑动以显示页面底部\n        d.swipe(500, 500, 500, 200)\n        time.sleep(2)\n    \n        # 验证发布视频提示是否存在\n>       assert pdp_page.check_publish_video_tip_exists(), \"Publish video tip is not displayed on PDP\"\nE       AttributeError: 'ProductDetailPage' object has no attribute 'check_publish_video_tip_exists'\n\nsrc/EC/tests/product/test_112812_pdp_video_features.py:185: AttributeError"}, "description": "\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证PDP下方是否展示发布视频tip词\n        3. 验证发布视频按钮是否可点击\n        ", "parameters": [{"name": "setup", "value": "('test_004_publish_video_entry',)"}], "start": 1749177432329, "stop": 1749177489063, "uuid": "416951b5-64d3-4dff-ae9f-b7b9c76277cf", "historyId": "c66a4bed09c5f620002de3effaee53f3", "testCaseId": "442c0f286da583eedcb33b44c8fa4352", "fullName": "src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures#test_004_publish_video_entry", "labels": [{"name": "story", "value": "iOS端PDP视频功能测试"}, {"name": "tag", "value": "pdp_video"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.product"}, {"name": "suite", "value": "test_112812_pdp_video_features"}, {"name": "subSuite", "value": "TestPDPVideoFeatures"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "55270-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.product.test_112812_pdp_video_features"}]}