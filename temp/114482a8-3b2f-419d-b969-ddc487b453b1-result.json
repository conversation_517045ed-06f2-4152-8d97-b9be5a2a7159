{"name": "搜索对应用户名并切换到Accounts栏测试", "status": "passed", "description": "\n        测试步骤：\n        1. 进入搜索页面\n        2. 搜索指定用户名\n        3. 验证默认在Posts栏\n        4. 切换到Accounts栏\n        5. 验证出现对应的用户\n        ", "parameters": [{"name": "setup", "value": "('test_search_user_and_switch_accounts',)"}], "start": *************, "stop": *************, "uuid": "27a0493a-9fef-41ae-bf08-0e9f0e2e28b6", "historyId": "0eff00649e85b9db5fce04814e5dc3ce", "testCaseId": "3d231379db7e98b6acc9d7366e38c97d", "fullName": "src.EC.tests.social.test_112741_social_search_accounts_verify.TestSocialSearchAccountsVerify#test_112741_search_user_and_switch_accounts", "labels": [{"name": "story", "value": "Social搜索账号功能验证"}, {"name": "tag", "value": "ios"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.social"}, {"name": "suite", "value": "test_112741_social_search_accounts_verify"}, {"name": "subSuite", "value": "TestSocialSearchAccountsVerify"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "6210-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.social.test_112741_social_search_accounts_verify"}]}