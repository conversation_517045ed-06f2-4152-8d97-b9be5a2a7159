{"name": "多种类型购物车结算中间页面验证测试, ios独有", "status": "broken", "statusDetails": {"message": "TypeError: 'NoneType' object is not iterable", "trace": "self = <src.EC.tests.cart.test_112223_multi_cart_checkout_flow.TestMultiCartCheckoutFlow object at 0x107b25370>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"24516049-e30c-4ecd-bfdb-098d5eaa9eba\")>\nsetup = None\n\n    @allure.title(\"多种类型购物车结算中间页面验证测试, ios独有\")\n    @pytest.mark.parametrize('setup', [('test_112223_multi_cart_checkout_intermediate_page', )], indirect=True)\n    @pytest.mark.ios\n    def test_112223_multi_cart_checkout_intermediate_page(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1. 用户购物车有多种类型购物车，点击结算\n        2. 验证弹出中间页面\n        3. 验证购物车默认是不勾选状态\n        4. 验证提示用户没有勾选购物车的提示\n        5. 验证底部结算按钮显示灰色样式\n        6. 验证顶部展示Select all carts（0/3）默认页面\n        7. 验证底部展示蓝色文案\"You can select multiple carts for checkout.\"\n        8. 验证蓝色文案一直展示，除非用户点击了底部的灰色结算按钮\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n    \n        # 初始化多购物车结算页面对象\n        multi_cart_page = MultiCartCheckoutPage(d, platform)\n        # 0. 添加商品到购物车, 否则购物车可能为空\n>       multi_cart_page.add_products_to_cart()\n\nsrc/EC/tests/cart/test_112223_multi_cart_checkout_flow.py:54: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <src.EC.page_objects.cart.multi_cart_checkout_page.MultiCartCheckoutPage object at 0x1082fa480>\n\n    def add_products_to_cart(self):\n        self.swipe_screen(_driver=self.driver, distance=0.5)\n        # 3. editor's pick\n        editor_pick_add = self.find_elements(self.driver, self.strategy.get('editors_pick_add'))\n>       for i in editor_pick_add:\nE       TypeError: 'NoneType' object is not iterable\n\nsrc/EC/page_objects/cart/multi_cart_checkout_page.py:142: TypeError"}, "description": "\n        测试步骤：\n        1. 用户购物车有多种类型购物车，点击结算\n        2. 验证弹出中间页面\n        3. 验证购物车默认是不勾选状态\n        4. 验证提示用户没有勾选购物车的提示\n        5. 验证底部结算按钮显示灰色样式\n        6. 验证顶部展示Select all carts（0/3）默认页面\n        7. 验证底部展示蓝色文案\"You can select multiple carts for checkout.\"\n        8. 验证蓝色文案一直展示，除非用户点击了底部的灰色结算按钮\n        ", "parameters": [{"name": "setup", "value": "('test_112223_multi_cart_checkout_intermediate_page',)"}], "start": 1750839784859, "stop": 1750839797610, "uuid": "0cd71758-1951-4105-8086-66e35c8bd180", "historyId": "8b4c8c97a87bcb74a3554fc980dffcf1", "testCaseId": "4d5c2f84f0a06df8af639977ab9b10d3", "fullName": "src.EC.tests.cart.test_112223_multi_cart_checkout_flow.TestMultiCartCheckoutFlow#test_112223_multi_cart_checkout_intermediate_page", "labels": [{"name": "story", "value": "多种类型购物车结算流程测试"}, {"name": "tag", "value": "ios"}, {"name": "tag", "value": "multi_cart"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.cart"}, {"name": "suite", "value": "test_112223_multi_cart_checkout_flow"}, {"name": "subSuite", "value": "TestMultiCartCheckoutFlow"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "65817-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.cart.test_112223_multi_cart_checkout_flow"}]}