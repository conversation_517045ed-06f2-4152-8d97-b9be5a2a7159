{"name": "PDP页面发布视频入口测试", "status": "failed", "statusDetails": {"message": "AssertionError: Failed to navigate to product detail page\nassert False", "trace": "self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1085c94f0>\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"69f180a8-765a-4bf6-9510-6a7d0b7cf734\")>\nget_platform = 'iOS', setup = None\n\n    @allure.title(\"PDP页面发布视频入口测试\")\n    @pytest.mark.parametrize('setup', [('test_004_publish_video_entry', )], indirect=True)\n    def test_004_publish_video_entry(self, get_driver, get_platform, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证PDP下方是否展示发布视频tip词\n        3. 验证发布视频按钮是否可点击\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n    \n        # 准备测试环境\n    \n        # 导航到产品详情页\n        navigation_success = self.navigate_to_product_detail(d, platform)\n>       assert navigation_success, \"Failed to navigate to product detail page\"\nE       AssertionError: Failed to navigate to product detail page\nE       assert False\n\nsrc/EC/tests/product/test_112812_pdp_video_features.py:175: AssertionError"}, "description": "\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证PDP下方是否展示发布视频tip词\n        3. 验证发布视频按钮是否可点击\n        ", "parameters": [{"name": "setup", "value": "('test_004_publish_video_entry',)"}], "start": 1749121664419, "stop": 1749121702784, "uuid": "db30516c-edb3-4316-88a2-20b7cb76282a", "historyId": "c66a4bed09c5f620002de3effaee53f3", "testCaseId": "442c0f286da583eedcb33b44c8fa4352", "fullName": "src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures#test_004_publish_video_entry", "labels": [{"name": "story", "value": "iOS端PDP视频功能测试"}, {"name": "tag", "value": "pdp_video"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.product"}, {"name": "suite", "value": "test_112812_pdp_video_features"}, {"name": "subSuite", "value": "TestPDPVideoFeatures"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "45275-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.product.test_112812_pdp_video_features"}]}