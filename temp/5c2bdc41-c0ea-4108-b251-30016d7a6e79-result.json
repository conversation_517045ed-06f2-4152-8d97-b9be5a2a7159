{"name": "查看订单页面各状态标签", "status": "passed", "description": "\n        测试步骤：\n        1. 进入account页面\n        2. 点击my orders,进入orders页面\n        3. 依次点击\"pending, unshipped, shipped, to review, cancelled\"查看订单\n        ", "parameters": [{"name": "setup", "value": "('test_001_check_order_tabs',)"}], "start": *************, "stop": *************, "uuid": "1d832ab2-37c0-49af-a2a1-d4b751f79e51", "historyId": "f45987b666f9249d3a834b070fe1c59f", "testCaseId": "8d92ff0f7c4862dd2ca541fd8a3dfc5e", "fullName": "src.EC.tests.account.test_001_orders.TestOrders#test_001_check_order_tabs", "labels": [{"name": "story", "value": "查看订单页面各状态标签"}, {"name": "tag", "value": "orders"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.account"}, {"name": "suite", "value": "test_001_orders"}, {"name": "subSuite", "value": "TestOrders"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "58364-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.account.test_001_orders"}]}