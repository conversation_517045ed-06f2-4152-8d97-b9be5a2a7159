{"name": "视频栏分类排序测试", "status": "failed", "statusDetails": {"message": "AssertionError: Video categories are not in the expected order (review > recipes > unboxing)\nassert False\n +  where False = <bound method ProductDetailPage.check_video_categories_order of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x110873050>>()\n +    where <bound method ProductDetailPage.check_video_categories_order of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x110873050>> = <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x110873050>.check_video_categories_order", "trace": "self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1107df920>\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"9f0944ed-d7b3-4be2-8a88-ec6dcf48c8e1\")>\nget_platform = 'iOS', setup = None\n\n    @allure.title(\"视频栏分类排序测试\")\n    @pytest.mark.parametrize('setup', [('test_002_video_categories_order', )], indirect=True)\n    def test_112812_video_categories_order(self, get_driver, get_platform, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证视频栏分类排序是否为 review > recipes > unboxing\n        \"\"\"\n        # 此用例仅适用于iOS端,安卓滑这三个标签\n        d = get_driver\n        platform = get_platform\n    \n    \n        # 初始化产品详情页对象\n        pdp_page = ProductDetailPage(d, platform)\n    \n        # 验证视频分类排序\n>       assert pdp_page.check_video_categories_order(), \"Video categories are not in the expected order (review > recipes > unboxing)\"\nE       AssertionError: Video categories are not in the expected order (review > recipes > unboxing)\nE       assert False\nE        +  where False = <bound method ProductDetailPage.check_video_categories_order of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x110873050>>()\nE        +    where <bound method ProductDetailPage.check_video_categories_order of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x110873050>> = <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x110873050>.check_video_categories_order\n\nsrc/EC/tests/product/test_112812_pdp_video_features.py:115: AssertionError"}, "description": "\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证视频栏分类排序是否为 review > recipes > unboxing\n        ", "parameters": [{"name": "setup", "value": "('test_002_video_categories_order',)"}], "start": 1749181426161, "stop": 1749181440686, "uuid": "56de3b11-4782-4e23-a30e-62199e1db262", "historyId": "e9284ec231e0ae8b188f5d8955b37b5f", "testCaseId": "7560e7e9e450793ca2476a1e1106327a", "fullName": "src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures#test_112812_video_categories_order", "labels": [{"name": "story", "value": "iOS端PDP视频功能测试"}, {"name": "tag", "value": "pdp_video"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.product"}, {"name": "suite", "value": "test_112812_pdp_video_features"}, {"name": "subSuite", "value": "TestPDPVideoFeatures"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "56960-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.product.test_112812_pdp_video_features"}]}