{"name": "Gift Card完整购买流程测试", "status": "passed", "description": "\n        测试步骤：\n        1. 进入Gift card主页面，处理欢迎弹窗\n        2. 填入收件人邮箱，验证输入成功\n        3. 点击checkout，进入礼品卡checkout页面\n        4. 点击支付方式下拉栏，选择PayPal\n        5. 点击place order，跳转到PayPal第三方支付页面\n        ", "parameters": [{"name": "setup", "value": "('test_gift_card_purchase_flow_complete',)"}], "start": *************, "stop": *************, "uuid": "c2f8cf32-db7c-4ee7-84c0-75d3016e4fad", "historyId": "8c210d3ba6326eb98ba4bc72043c9e72", "testCaseId": "35964ddb6a6f21389640eda3abcb38f9", "fullName": "src.EC.tests.account.test_112237_gift_card_purchase_flow.TestGiftCardPurchaseFlow#test_112237_gift_card_purchase_flow_complete", "labels": [{"name": "story", "value": "Gift Card购买流程测试"}, {"name": "tag", "value": "gift_card"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.account"}, {"name": "suite", "value": "test_112237_gift_card_purchase_flow"}, {"name": "subSuite", "value": "TestGiftCardPurchaseFlow"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "56533-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.account.test_112237_gift_card_purchase_flow"}]}