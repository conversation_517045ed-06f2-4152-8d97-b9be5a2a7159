{"name": "Gift Card完整购买流程测试", "status": "failed", "statusDetails": {"message": "AssertionError: Failed to select PayPal payment method\nassert False", "trace": "self = <src.EC.tests.account.test_112237_gift_card_purchase_flow.TestGiftCardPurchaseFlow object at 0x104059640>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"33b5fa97-a35e-4af9-af72-0a6a9466b94c\")>\nsetup = None\n\n    @allure.title(\"Gift Card完整购买流程测试\")\n    @pytest.mark.parametrize('setup', [('test_gift_card_purchase_flow_complete', )], indirect=True)\n    def test_112237_gift_card_purchase_flow_complete(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入Gift card主页面，处理欢迎弹窗\n        2. 填入收件人邮箱，验证输入成功\n        3. 点击checkout，进入礼品卡checkout页面\n        4. 点击支付方式下拉栏，选择PayPal\n        5. 点击place order，跳转到PayPal第三方支付页面\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n    \n        # 步骤1: 进入Gift card主页面\n        navigation_success = self.navigate_to_gift_card_page(d, platform)\n        assert navigation_success, \"Failed to navigate to gift card page\"\n    \n        # 初始化Gift Card页面对象\n        gift_card_page = GiftCardPage(d, platform)\n    \n        # 处理欢迎弹窗\n        welcome_popup_exists = gift_card_page.check_welcome_popup_exists()\n        if welcome_popup_exists:\n            popup_closed = gift_card_page.close_welcome_popup()\n            assert popup_closed, \"Failed to close welcome popup\"\n            log.info(\"✓ Welcome popup closed successfully\")\n        else:\n            log.info(\"ℹ No welcome popup found\")\n    \n        # 校验页面元素\n        assert gift_card_page.check_page_elements, \"Welcome popup still exists\"\n    \n        # 步骤2: 填入收件人邮箱\n        test_email = \"<EMAIL>\"\n        email_entered = gift_card_page.enter_recipient_email(test_email)\n        assert email_entered, f\"Failed to enter recipient email: {test_email}\"\n    \n    \n        # 步骤3: 点击checkout\n        checkout_clicked = gift_card_page.click_checkout_button()\n        assert checkout_clicked, \"Failed to click checkout button\"\n    \n        # 验证进入checkout页面\n        checkout_page_loaded = gift_card_page.check_checkout_page_loaded()\n        assert checkout_page_loaded, \"Failed to load gift card checkout page\"\n        log.info(\"✓ Successfully navigated to gift card checkout page\")\n    \n        # 步骤4: 点击支付方式下拉栏，选择PayPal\n        dropdown_clicked = gift_card_page.click_payment_method_dropdown()\n        assert dropdown_clicked, \"Failed to click payment method dropdown\"\n        log.info(\"✓ Payment method dropdown opened\")\n    \n        paypal_selected = gift_card_page.select_paypal_payment()\n>       assert paypal_selected, \"Failed to select PayPal payment method\"\nE       AssertionError: Failed to select PayPal payment method\nE       assert False\n\nsrc/EC/tests/account/test_112237_gift_card_purchase_flow.py:117: AssertionError"}, "description": "\n        测试步骤：\n        1. 进入Gift card主页面，处理欢迎弹窗\n        2. 填入收件人邮箱，验证输入成功\n        3. 点击checkout，进入礼品卡checkout页面\n        4. 点击支付方式下拉栏，选择PayPal\n        5. 点击place order，跳转到PayPal第三方支付页面\n        ", "parameters": [{"name": "setup", "value": "('test_gift_card_purchase_flow_complete',)"}], "start": *************, "stop": *************, "uuid": "b47cce91-64f4-4c41-8e9a-99268912039d", "historyId": "8c210d3ba6326eb98ba4bc72043c9e72", "testCaseId": "35964ddb6a6f21389640eda3abcb38f9", "fullName": "src.EC.tests.account.test_112237_gift_card_purchase_flow.TestGiftCardPurchaseFlow#test_112237_gift_card_purchase_flow_complete", "labels": [{"name": "story", "value": "Gift Card购买流程测试"}, {"name": "tag", "value": "gift_card"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.account"}, {"name": "suite", "value": "test_112237_gift_card_purchase_flow"}, {"name": "subSuite", "value": "TestGiftCardPurchaseFlow"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "55998-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.account.test_112237_gift_card_purchase_flow"}]}