{"name": "点击See All跳转测试", "status": "failed", "statusDetails": {"message": "AssertionError: Failed to click See All button\nassert False\n +  where False = <bound method ProductDetailPage.click_see_all_videos of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x107da7680>>()\n +    where <bound method ProductDetailPage.click_see_all_videos of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x107da7680>> = <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x107da7680>.click_see_all_videos", "trace": "self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x107e3d430>\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"a0515a5d-f723-4fb6-9b9a-c27e2efef0d2\")>\nget_platform = 'iOS', setup = None\n\n    @allure.title(\"点击See All跳转测试\")\n    @pytest.mark.parametrize('setup', [('test_003_see_all_navigation', )], indirect=True)\n    def test_112812_see_all_navigation(self, get_driver, get_platform, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入产品详情页\n        2. 点击See All\n        3. 验证是否跳转到Explore页面并展示全部视频\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n    \n        # 准备测试环境\n    \n        # 导航到产品详情页\n        navigation_success = self.navigate_to_product_detail(d, platform)\n        assert navigation_success, \"Failed to navigate to product detail page\"\n    \n        # 初始化产品详情页对象\n        pdp_page = ProductDetailPage(d, platform)\n    \n        # 验证视频栏是否存在\n        assert pdp_page.check_videos_section_exists(), \"Videos section is not displayed on PDP\"\n    \n        # 点击See All按钮\n>       assert pdp_page.click_see_all_videos(), \"Failed to click See All button\"\nE       AssertionError: Failed to click See All button\nE       assert False\nE        +  where False = <bound method ProductDetailPage.click_see_all_videos of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x107da7680>>()\nE        +    where <bound method ProductDetailPage.click_see_all_videos of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x107da7680>> = <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x107da7680>.click_see_all_videos\n\nsrc/EC/tests/product/test_112812_pdp_video_features.py:144: AssertionError"}, "description": "\n        测试步骤：\n        1. 进入产品详情页\n        2. 点击See All\n        3. 验证是否跳转到Explore页面并展示全部视频\n        ", "parameters": [{"name": "setup", "value": "('test_003_see_all_navigation',)"}], "start": 1749440695048, "stop": 1749440749781, "uuid": "95b42103-bd90-4f55-a416-24c152b5a47b", "historyId": "6debfaa1ce2119d87b51aa5545367250", "testCaseId": "935bf7b52fd648fb8d939976c54fad41", "fullName": "src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures#test_112812_see_all_navigation", "labels": [{"name": "story", "value": "iOS端PDP视频功能测试"}, {"name": "tag", "value": "pdp_video"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.product"}, {"name": "suite", "value": "test_112812_pdp_video_features"}, {"name": "subSuite", "value": "TestPDPVideoFeatures"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "6210-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.product.test_112812_pdp_video_features"}]}