{"name": "PDP页面Reviews栏基础展示测试", "status": "broken", "statusDetails": {"message": "TypeError: 'int' object is not subscriptable", "trace": "self = <src.EC.tests.product.test_112777_pdp_reviews_selling_points.TestPDPReviewsSellingPoints object at 0x107009550>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"a1358b32-859a-4a69-8472-1e09ebab90b5\")>\nsetup = None\n\n    @allure.title(\"PDP页面Reviews栏基础展示测试\")\n    @pytest.mark.parametrize('setup', [('test_reviews_section_display', )], indirect=True)\n    def test_112777_reviews_section_display(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证Reviews栏是否展示\n        3. 验证总review数是否正确显示\n        4. 验证Customers say栏是否展示\n        5. 验证AI-generated文本样式\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n    \n        # 导航到产品详情页\n        navigation_success = self.navigate_to_product_detail(d, platform)\n        assert navigation_success, \"Failed to navigate to product detail page\"\n    \n        # 初始化产品详情页对象\n        pdp_page = ProductDetailPage(d, platform)\n    \n        # 1. 验证Reviews栏是否存在\n        assert pdp_page.check_reviews_section_exists(), \"Reviews section is not displayed on PDP\"\n        log.info(\"✓ Reviews section is displayed\")\n    \n        # 2. 验证总review数显示\n        reviews_count = pdp_page.get_total_reviews_count()\n        assert reviews_count >= 0, f\"Reviews count should be non-negative, got {reviews_count}\"\n        log.info(f\"✓ Total reviews count: {reviews_count}\")\n    \n        # 以下内容为IOS独有，安卓没有\n        if platform == 'iOS':\n            # 3. 验证Customers say栏是否存在\n            customers_say_exists = pdp_page.check_customers_say_section_exists()\n            if reviews_count > 50:\n                assert customers_say_exists, \"Customers say section should be displayed when reviews exist\"\n                log.info(\"✓ Customers say section is displayed\")\n            else:\n                log.info(\"ℹ No reviews found, customers say section may not be displayed\")\n    \n            # 4. 验证AI-generated文本样式\n            if customers_say_exists:\n                # 目前无法做太复杂的文本分析，先跳过此步骤\n                pass\n                # ai_text_style_correct = pdp_page.check_ai_generated_text_style()\n                # assert ai_text_style_correct, \"AI-generated text style should be lighter than normal text\"\n                # log.info(\"✓ AI-generated text style is correct\")\n    \n            # 5. 跳转关键词review list界面\n            selling_points_container = self.find_element(_driver=d, ele=pdp_page.strategies.get(platform).get(\"selling_points_container\"))\n            if selling_points_container:\n                selling_points_container.click()\n                time.sleep(3)\n                assert self.find_element(_driver=d, ele=pdp_page.strategies.get(platform).get(\"review_list_table\")).is_displayed()\n                back_to_pdp = self.find_element(_driver=d, ele=pdp_page.strategies.get(platform).get(\"review_list_back\"))\n                d.tap([(back_to_pdp.location.get(\"x\"), back_to_pdp.location.get(\"y\"))])\n                # back.click()\n            # 返回首页供其它用例使用\n>           d.tap([31, 66])\n\nsrc/EC/tests/product/test_112777_pdp_reviews_selling_points.py:131: \n_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ \n\nself = <appium.webdriver.webdriver.WebDriver (session=\"a1358b32-859a-4a69-8472-1e09ebab90b5\")>\npositions = [31, 66], duration = None\n\n    def tap(self, positions: List[Tuple[int, int]], duration: Optional[int] = None) -> 'WebDriver':\n        \"\"\"Taps on an particular place with up to five fingers, holding for a\n        certain time\n    \n        Args:\n            positions: an array of tuples representing the x/y coordinates of\n                the fingers to tap. Length can be up to five.\n            duration: length of time to tap, in ms\n    \n        Usage:\n            driver.tap([(100, 20), (100, 60), (100, 100)], 500)\n    \n        Returns:\n            Union['WebDriver', 'ActionHelpers']: Self instance\n        \"\"\"\n        if len(positions) == 1:\n            actions = ActionChains(self)\n            actions.w3c_actions = ActionBuilder(self, mouse=PointerInput(interaction.POINTER_TOUCH, 'touch'))\n            x = positions[0][0]\n            y = positions[0][1]\n            actions.w3c_actions.pointer_action.move_to_location(x, y)\n            actions.w3c_actions.pointer_action.pointer_down()\n            if duration:\n                actions.w3c_actions.pointer_action.pause(duration / 1000)\n            else:\n                actions.w3c_actions.pointer_action.pause(0.1)\n            actions.w3c_actions.pointer_action.release()\n            actions.perform()\n        else:\n            finger = 0\n            actions = ActionChains(self)\n            actions.w3c_actions.devices = []\n    \n            for position in positions:\n                finger += 1\n>               x = position[0]\nE               TypeError: 'int' object is not subscriptable\n\nqa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/action_helpers.py:117: TypeError"}, "description": "\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证Reviews栏是否展示\n        3. 验证总review数是否正确显示\n        4. 验证Customers say栏是否展示\n        5. 验证AI-generated文本样式\n        ", "parameters": [{"name": "setup", "value": "('test_reviews_section_display',)"}], "start": 1749180248616, "stop": 1749180321424, "uuid": "7acee803-c0b2-4eeb-ab5a-ddc87b251e05", "historyId": "f687fa249e9502191962abf9beab7816", "testCaseId": "ebd254721491e4204b01ca8c9db8c493", "fullName": "src.EC.tests.product.test_112777_pdp_reviews_selling_points.TestPDPReviewsSellingPoints#test_112777_reviews_section_display", "labels": [{"name": "story", "value": "PDP页面Reviews栏和卖点关键词功能测试"}, {"name": "tag", "value": "pdp_reviews"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.product"}, {"name": "suite", "value": "test_112777_pdp_reviews_selling_points"}, {"name": "subSuite", "value": "TestPDPReviewsSellingPoints"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "56533-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.product.test_112777_pdp_reviews_selling_points"}]}