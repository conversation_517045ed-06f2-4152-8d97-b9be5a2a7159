{"name": "未支付订单UI检查", "status": "passed", "description": "\n        测试步骤：\n        1. 用户购物车有1种类型购物车，点击结算\n        2. 进入orders页面，验证未支付订单UI\n        ", "parameters": [{"name": "setup", "value": "('test_108221_unpaid_orders_check',)"}], "start": *************, "stop": *************, "uuid": "c5b7a458-3989-44ad-baaf-6e915b0af8d6", "historyId": "d99a03e0b3c112d1ce0166c2b3157249", "testCaseId": "4759fd6ce44ea1ef5c87b82d991960d3", "fullName": "src.EC.tests.account.test_108221_unpaid_orders_check.TestUnpaidOrdersCheck#test_108221_unpaid_orders_check", "labels": [{"name": "story", "value": "多种类型购物车结算流程测试"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.account"}, {"name": "suite", "value": "test_108221_unpaid_orders_check"}, {"name": "subSuite", "value": "TestUnpaidOrdersCheck"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "65817-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.account.test_108221_unpaid_orders_check"}]}