{"name": "iOS端PDP视频UI展示测试", "status": "failed", "statusDetails": {"message": "AssertionError: Failed to navigate to product detail page\nassert False", "trace": "self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1085c90a0>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"69f180a8-765a-4bf6-9510-6a7d0b7cf734\")>\nsetup = None\n\n    @allure.title(\"iOS端PDP视频UI展示测试\")\n    @pytest.mark.parametrize('setup', [('test_112812_pdp_video_ui_display', )], indirect=True)\n    def test_112812_pdp_video_ui_display(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证视频栏展示及视频个数显示\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n    \n        # 准备测试环境\n    \n        # 导航到产品详情页\n        navigation_success = self.navigate_to_product_detail(d, platform)\n>       assert navigation_success, \"Failed to navigate to product detail page\"\nE       AssertionError: Failed to navigate to product detail page\nE       assert False\n\nsrc/EC/tests/product/test_112812_pdp_video_features.py:80: AssertionError"}, "description": "\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证视频栏展示及视频个数显示\n        ", "parameters": [{"name": "setup", "value": "('test_112812_pdp_video_ui_display',)"}], "start": 1749121707386, "stop": 1749121744762, "uuid": "3834f61f-8083-45c8-954a-0e1ddee973e1", "historyId": "60923f2b1aba9c657800c71283729ef9", "testCaseId": "7d6cd075fbe0ebaf7d2a64add28a49fb", "fullName": "src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures#test_112812_pdp_video_ui_display", "labels": [{"name": "story", "value": "iOS端PDP视频功能测试"}, {"name": "tag", "value": "pdp_video"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.product"}, {"name": "suite", "value": "test_112812_pdp_video_features"}, {"name": "subSuite", "value": "TestPDPVideoFeatures"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "45275-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.product.test_112812_pdp_video_features"}]}