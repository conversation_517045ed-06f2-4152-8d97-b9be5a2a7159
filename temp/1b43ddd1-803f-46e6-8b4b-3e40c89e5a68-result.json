{"name": "onboarding-先登陆后购买", "status": "passed", "description": "先登陆后加购", "parameters": [{"name": "setup", "value": "('test_001_onboarding_with_login',)"}], "start": 1750839038785, "stop": 1750839202137, "uuid": "0e6e7e9b-8416-48fc-8777-20a2a57a60b2", "historyId": "b82b595df7953a8e2d6b32afd2b9cd8d", "testCaseId": "4b341cc0684a40d458b178cec8c8d2f5", "fullName": "src.EC.tests.onboarding.test_000_onboarding.TestOnboarding#test_000_onboarding_with_login", "labels": [{"name": "story", "value": "android-onboarding,先购买和后登陆和先登陆后购买2个流程"}, {"name": "tag", "value": "android_onboarding"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.onboarding"}, {"name": "suite", "value": "test_000_onboarding"}, {"name": "subSuite", "value": "TestOnboarding"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "65817-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.onboarding.test_000_onboarding"}]}