{"name": "点击See All跳转测试", "status": "failed", "statusDetails": {"message": "AssertionError: Failed to navigate to Explore page after clicking See All\nassert None is not None", "trace": "self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1054ea270>\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"a3424479-1fca-4a03-a810-93185ac271c9\")>\nget_platform = 'iOS', setup = None\n\n    @allure.title(\"点击See All跳转测试\")\n    @pytest.mark.parametrize('setup', [('test_003_see_all_navigation', )], indirect=True)\n    def test_003_see_all_navigation(self, get_driver, get_platform, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入产品详情页\n        2. 点击See All\n        3. 验证是否跳转到Explore页面并展示全部视频\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n    \n        # 准备测试环境\n    \n        # 导航到产品详情页\n        navigation_success = self.navigate_to_product_detail(d, platform)\n        assert navigation_success, \"Failed to navigate to product detail page\"\n    \n        # 初始化产品详情页对象\n        pdp_page = ProductDetailPage(d, platform)\n    \n        # 验证视频栏是否存在\n        assert pdp_page.check_videos_section_exists(), \"Videos section is not displayed on PDP\"\n    \n        # 点击See All按钮\n        assert pdp_page.click_see_all_videos(), \"Failed to click See All button\"\n    \n        # 验证是否跳转到Explore页面\n        # 这里需要根据实际情况调整验证逻辑\n        if platform == 'Android':\n            explore_title = self.find_element(_driver=d, ele=(AppiumBy.XPATH, '//android.widget.TextView[@text=\"Explore\"]'))\n        else:  # iOS\n            explore_title = self.find_element(_driver=d, ele=(AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeNavigationBar[`name == \"Explore\"`]'))\n    \n>       assert explore_title is not None, \"Failed to navigate to Explore page after clicking See All\"\nE       AssertionError: Failed to navigate to Explore page after clicking See All\nE       assert None is not None\n\nsrc/EC/tests/product/test_112812_pdp_video_features.py:154: AssertionError"}, "description": "\n        测试步骤：\n        1. 进入产品详情页\n        2. 点击See All\n        3. 验证是否跳转到Explore页面并展示全部视频\n        ", "parameters": [{"name": "setup", "value": "('test_003_see_all_navigation',)"}], "start": 1749171877452, "stop": 1749171942267, "uuid": "5018f23e-e4ce-4865-ad9d-1142c75d31fa", "historyId": "4040569af417f0bcb560d251942f1b7e", "testCaseId": "fb804ecd1aa3e174ec87bd488453f77a", "fullName": "src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures#test_003_see_all_navigation", "labels": [{"name": "story", "value": "iOS端PDP视频功能测试"}, {"name": "tag", "value": "pdp_video"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.product"}, {"name": "suite", "value": "test_112812_pdp_video_features"}, {"name": "subSuite", "value": "TestPDPVideoFeatures"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "53333-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.product.test_112812_pdp_video_features"}]}