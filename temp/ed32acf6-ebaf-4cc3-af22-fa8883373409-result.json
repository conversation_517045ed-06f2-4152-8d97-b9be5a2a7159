{"name": "onboarding-先购买checkout时登陆", "status": "passed", "description": "先加购，checkout时再登陆", "parameters": [{"name": "setup", "value": "('test_000_onboarding_without_login',)"}], "start": 1749188186314, "stop": 1749188418716, "uuid": "e3154806-7624-4cf4-8916-1d06437cd038", "historyId": "af3e511ef39aea9764ba3e71dbd2763f", "testCaseId": "97107ccb9079c24e48c6229d6e599e2b", "fullName": "src.EC.tests.onboarding.test_000_onboarding.TestOnboarding#test_000_onboarding_without_login", "labels": [{"name": "story", "value": "android-onboarding,先购买和后登陆和先登陆后购买2个流程"}, {"name": "tag", "value": "android_onboarding"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.onboarding"}, {"name": "suite", "value": "test_000_onboarding"}, {"name": "subSuite", "value": "TestOnboarding"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "58364-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.onboarding.test_000_onboarding"}]}