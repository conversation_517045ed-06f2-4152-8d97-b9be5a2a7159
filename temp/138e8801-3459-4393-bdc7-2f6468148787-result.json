{"name": "点击See All跳转测试", "status": "failed", "statusDetails": {"message": "AssertionError: Failed to click See All button\nassert False\n +  where False = <bound method ProductDetailPage.click_see_all_videos of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x107022210>>()\n +    where <bound method ProductDetailPage.click_see_all_videos of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x107022210>> = <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x107022210>.click_see_all_videos", "trace": "self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x10700a1e0>\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"a1358b32-859a-4a69-8472-1e09ebab90b5\")>\nget_platform = 'iOS', setup = None\n\n    @allure.title(\"点击See All跳转测试\")\n    @pytest.mark.parametrize('setup', [('test_003_see_all_navigation', )], indirect=True)\n    def test_003_see_all_navigation(self, get_driver, get_platform, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入产品详情页\n        2. 点击See All\n        3. 验证是否跳转到Explore页面并展示全部视频\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n    \n        # 准备测试环境\n    \n        # 导航到产品详情页\n        navigation_success = self.navigate_to_product_detail(d, platform)\n        assert navigation_success, \"Failed to navigate to product detail page\"\n    \n        # 初始化产品详情页对象\n        pdp_page = ProductDetailPage(d, platform)\n    \n        # 验证视频栏是否存在\n        assert pdp_page.check_videos_section_exists(), \"Videos section is not displayed on PDP\"\n    \n        # 点击See All按钮\n>       assert pdp_page.click_see_all_videos(), \"Failed to click See All button\"\nE       AssertionError: Failed to click See All button\nE       assert False\nE        +  where False = <bound method ProductDetailPage.click_see_all_videos of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x107022210>>()\nE        +    where <bound method ProductDetailPage.click_see_all_videos of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x107022210>> = <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x107022210>.click_see_all_videos\n\nsrc/EC/tests/product/test_112812_pdp_video_features.py:145: AssertionError"}, "description": "\n        测试步骤：\n        1. 进入产品详情页\n        2. 点击See All\n        3. 验证是否跳转到Explore页面并展示全部视频\n        ", "parameters": [{"name": "setup", "value": "('test_003_see_all_navigation',)"}], "start": 1749180326737, "stop": 1749180375532, "uuid": "56bd690b-aa56-40d5-ab5d-734d77e62ec4", "historyId": "4040569af417f0bcb560d251942f1b7e", "testCaseId": "fb804ecd1aa3e174ec87bd488453f77a", "fullName": "src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures#test_003_see_all_navigation", "labels": [{"name": "story", "value": "iOS端PDP视频功能测试"}, {"name": "tag", "value": "pdp_video"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.product"}, {"name": "suite", "value": "test_112812_pdp_video_features"}, {"name": "subSuite", "value": "TestPDPVideoFeatures"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "56533-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.product.test_112812_pdp_video_features"}]}