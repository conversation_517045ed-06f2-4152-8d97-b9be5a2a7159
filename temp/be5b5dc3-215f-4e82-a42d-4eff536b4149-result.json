{"name": "未支付订单UI检查", "status": "skipped", "statusDetails": {"message": "Skipped: pending, 几个账号均没有这样的数据，等有数据后再编写", "trace": "('/Users/<USER>/qa-ui-android-ios/src/EC/tests/account/test_111415_rewards_total_saving_check.py', 32, 'Skipped: pending, 几个账号均没有这样的数据，等有数据后再编写')"}, "description": "\n        测试步骤：\n        1. 进入rewards portal，点击banner total saving横幅\n        2. 验证total saving弹窗数据\n        ", "parameters": [{"name": "setup", "value": "('test_111415_rewards_total_saving_check',)"}], "start": *************, "stop": *************, "uuid": "f20f0ed4-74d9-45ca-83dc-45cf857a1533", "historyId": "dfc6fc93400e6980bf8e99b06543954c", "testCaseId": "410b1ff58e8194ba6ec2111e3fe0604a", "fullName": "src.EC.tests.account.test_111415_rewards_total_saving_check.TestRewardsTotalSavingCheck#test_111415_rewards_total_saving_check", "labels": [{"name": "story", "value": "多种类型购物车结算流程测试"}, {"name": "tag", "value": "@pytest.mark.skip(reason='pending, 几个账号均没有这样的数据，等有数据后再编写')"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.account"}, {"name": "suite", "value": "test_111415_rewards_total_saving_check"}, {"name": "subSuite", "value": "TestRewardsTotalSavingCheck"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "65817-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.account.test_111415_rewards_total_saving_check"}]}