{"name": "搜索对应用户名并切换到Accounts栏测试", "status": "failed", "statusDetails": {"message": "AssertionError: Failed to find inspiration button!\nassert None", "trace": "self = <test_112741_social_search_accounts_verify.TestSocialSearchAccountsVerify object at 0x1085ca0c0>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"69f180a8-765a-4bf6-9510-6a7d0b7cf734\")>\nsetup = None\n\n    @allure.title(\"搜索对应用户名并切换到Accounts栏测试\")\n    @pytest.mark.parametrize('setup', [('test_search_user_and_switch_accounts',)], indirect=True)\n    @pytest.mark.ios\n    def test_112741_search_user_and_switch_accounts(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入搜索页面\n        2. 搜索指定用户名\n        3. 验证默认在Posts栏\n        4. 切换到Accounts栏\n        5. 验证出现对应的用户\n        \"\"\"\n        # 只能ios使用，安卓在输入用户名以后，发送回车键无效\n        d = get_driver\n        platform = get_platform\n    \n        # 初始化社交搜索页面对象\n        search_page = SocialSearchPage(d, platform)\n    \n        # 导航到搜索页面，点击inspiration按钮\n        inspiration = self.find_element(d, search_page.strategies.get(platform).get(\"inspiration\"))\n>       assert inspiration, \"Failed to find inspiration button!\"\nE       AssertionError: Failed to find inspiration button!\nE       assert None\n\nsrc/EC/tests/social/test_112741_social_search_accounts_verify.py:52: AssertionError"}, "description": "\n        测试步骤：\n        1. 进入搜索页面\n        2. 搜索指定用户名\n        3. 验证默认在Posts栏\n        4. 切换到Accounts栏\n        5. 验证出现对应的用户\n        ", "parameters": [{"name": "setup", "value": "('test_search_user_and_switch_accounts',)"}], "start": *************, "stop": *************, "uuid": "cbce18e2-0b2d-48fd-aea2-6aedf1b08a4e", "historyId": "0eff00649e85b9db5fce04814e5dc3ce", "testCaseId": "3d231379db7e98b6acc9d7366e38c97d", "fullName": "src.EC.tests.social.test_112741_social_search_accounts_verify.TestSocialSearchAccountsVerify#test_112741_search_user_and_switch_accounts", "labels": [{"name": "story", "value": "Social搜索账号功能验证"}, {"name": "tag", "value": "ios"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.social"}, {"name": "suite", "value": "test_112741_social_search_accounts_verify"}, {"name": "subSuite", "value": "TestSocialSearchAccountsVerify"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "45275-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.social.test_112741_social_search_accounts_verify"}]}