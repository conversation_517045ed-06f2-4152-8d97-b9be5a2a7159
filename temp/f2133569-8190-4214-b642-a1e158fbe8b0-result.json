{"name": "PDP页面Reviews栏基础展示测试", "status": "failed", "statusDetails": {"message": "AssertionError: Failed to navigate to product detail page\nassert False", "trace": "self = <src.EC.tests.product.test_112777_pdp_reviews_selling_points.TestPDPReviewsSellingPoints object at 0x111cf8290>\nget_platform = 'iOS'\nget_driver = <appium.webdriver.webdriver.WebDriver (session=\"da58834b-f3a7-4292-8b47-cd2600ae6502\")>\nsetup = None\n\n    @allure.title(\"PDP页面Reviews栏基础展示测试\")\n    @pytest.mark.parametrize('setup', [('test_reviews_section_display', )], indirect=True)\n    def test_112777_reviews_section_display(self, get_platform, get_driver, setup):\n        \"\"\"\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证Reviews栏是否展示\n        3. 验证总review数是否正确显示\n        4. 验证Customers say栏是否展示\n        5. 验证AI-generated文本样式\n        \"\"\"\n        d = get_driver\n        platform = get_platform\n    \n        # 导航到产品详情页\n        navigation_success = self.navigate_to_product_detail(d, platform)\n>       assert navigation_success, \"Failed to navigate to product detail page\"\nE       AssertionError: Failed to navigate to product detail page\nE       assert False\n\nsrc/EC/tests/product/test_112777_pdp_reviews_selling_points.py:89: AssertionError"}, "description": "\n        测试步骤：\n        1. 进入产品详情页\n        2. 验证Reviews栏是否展示\n        3. 验证总review数是否正确显示\n        4. 验证Customers say栏是否展示\n        5. 验证AI-generated文本样式\n        ", "parameters": [{"name": "setup", "value": "('test_reviews_section_display',)"}], "start": 1749174470195, "stop": 1749174504650, "uuid": "88b6d5da-aef8-47f4-9db7-8760652b3412", "historyId": "f687fa249e9502191962abf9beab7816", "testCaseId": "ebd254721491e4204b01ca8c9db8c493", "fullName": "src.EC.tests.product.test_112777_pdp_reviews_selling_points.TestPDPReviewsSellingPoints#test_112777_reviews_section_display", "labels": [{"name": "story", "value": "PDP页面Reviews栏和卖点关键词功能测试"}, {"name": "tag", "value": "pdp_reviews"}, {"name": "tag", "value": "regression"}, {"name": "parentSuite", "value": "src.EC.tests.product"}, {"name": "suite", "value": "test_112777_pdp_reviews_selling_points"}, {"name": "subSuite", "value": "TestPDPReviewsSellingPoints"}, {"name": "host", "value": "autotest.sayweee.net"}, {"name": "thread", "value": "54402-MainThread"}, {"name": "framework", "value": "pytest"}, {"name": "language", "value": "cpython3"}, {"name": "package", "value": "src.EC.tests.product.test_112777_pdp_reviews_selling_points"}]}