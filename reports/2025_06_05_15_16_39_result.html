<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <title> ios ui automation </title>
    <meta name="generator" content="weeeTest 0.2.10.4c"/>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <!--    <script src="http://libs.baidu.com/jquery/2.0.0/jquery.min.js"></script>-->
    <link rel="stylesheet" href="http://img.itest.info/seldom-main.css">
    <link rel="stylesheet" href="http://img.itest.info/seldom-utilities.css">

    <style type="text/css" media="screen">
    body {
        font-family: verdana, arial, helvetica, sans-serif;
        font-size: 80%;
    }

    table {
        font-size: 100%;
    }

    .table td {
        white-space: inherit !important;
    }

    /* -- heading ---------------------------------------------------------------------- */
    h1 {
        font-size: 16pt;
        color: gray;
    }

    pre {
        background-color: #eef2f7;
        padding-top: 10px;
        text-align: left;
        max-height: 600px;
        overflow: auto;
    }

    ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
        background-color: #F5F5F5;
    }

    ::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        background-color: rgba(114, 124, 245, .25);
    }

    ::-webkit-scrollbar-thumb {
        border-radius: 10px;
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
        background-color: #6c757d;
    }

    .heading {
        margin-top: 20px;
        margin-bottom: 1ex;
        margin-left: 10px;
        margin-right: 10px;
        width: 23%;
        float: left;
        padding-top: 10px;
        padding-left: 10px;
        padding-bottom: 10px;
        padding-right: 10px;
        box-shadow: 0px 0px 5px #000;
    }

    .heading .attribute {
        margin-top: 1ex;
        margin-bottom: 0;
    }

    .heading .description {
        margin-top: 4ex;
        margin-bottom: 6ex;
    }

    /* -- css div popup ------------------------------------------------------------------------ */

    a.popup_link:hover {
        color: red;
    }

    .log_window {
        max-width: 70%;
        max-height: 70%;
    }

    /* -- results ------------------------------------------------------------------------ */
    .show_detail_line {
        margin-left: 10px;
        margin-top: 30px;
        margin-bottom: 20px;
    }

    .show_detail_button {
        margin-top: 3ex;
        margin-bottom: 1ex;
        margin-left: 10px;
        text-align: right;
        margin-right: 15px;
    }

    .header_row {
        font-weight: bold;
        color: #606060;
        border-top-width: 10px;
        border-color: #d6e9c6;
        font-size: 15px;
    }

    .total_row {
        font-weight: bold;
        background-color: #dee2e6;
    }

    .passClass {
        background-color: #ccf5e7;
    }

    .skipClass {
        background-color: #cfd6df;
    }

    .failClass {
        background-color: #ffe8cc;
    }

    .errorClass {
        background-color: #ffd6e0;
    }

    .passCase {
        color: #00CC88;
        font-weight: bold;
    }

    .failCase {
        color: #FF8C00;
        font-weight: bold;
    }

    .errorCase {
        color: #FF3366;
        font-weight: bold;
    }

    .hiddenRow {
        display: none;
    }

    .caseStatistics {
        width: 46%
    }

    .none {
        color: #009900
    }

    .testcase {
        margin-left: 2em;
    }

    /* -- chars ---------------------------------------------------------------------- */
    .testChars {
        width: 900px;
        margin-left: 0px;
    }

    .error-color {
        color: #fff;
        background-color: #f44455;
        border-color: #f44455;
    }

    .pass-color {
        color: #fff;
        background-color: #5fc27e;
        border-color: #5fc27e;
    }

    .fail-color {
        color: #fff;
        background-color: #fcc100;
        border-color: #fcc100;
    }

    .skip-color {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;
    }

    /* -- screenshots ---------------------------------------------------------------------- */
    .img {
        border-collapse: collapse;
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        margin: auto;
    }

    .img-card {
        height: 600px;
        width: 800px;
        background-color: #e7eaf0;
    }

    .img-circle {
        height: 20px;
        border-radius: 12px;
        background-color: red;
        padding-left: 13px;
        margin: 0 auto;
        position: relative;
        top: -40px;
        background-color: rgba(1, 150, 0, 0.3);
    }

    .img-circle font {
        border: 1px solid white;
        width: 11px;
        height: 11px;
        border-radius: 50%;
        margin-right: 9px;
        margin-top: 4px;
        display: block;
        float: left;
        background-color: white;
    }

    .close-shots {
        position: absolute;
        top: 5px;
        right: 5px;
        z-index: 99;
    }

    .footer {
        height: 50px;
        width: 100%;
        position: fixed;
        bottom: 0;
        background-color: white;
        padding-bottom: 0.75rem !important;
        padding-top: 0.75rem !important;
        border: 0 solid #e7eaf0;
    }

    #headContainer {
        margin-top: 20px;
        margin-bottom: 20px;
        padding-left: 30px;
        padding-right: 30px;
    }

    .head-list {
        padding-top: 5px;
        padding-bottom: 5px;
    }

    #resultContainer {
        margin-left: 30px;
        margin-right: 30px;
    }


</style>
</head>

<body style="background-color: #f5f9fc;">
<script language="javascript" type="text/javascript">
    function showLog(id) {
        document.body.style.overflow = 'hidden'
        document.body.style.height = '100%'
        document.getElementById(id).style.display = 'block'
    }

    function hideLog(id) {
        document.body.style.overflow = 'auto';
        document.getElementById(id).style.display = 'none'
    }

    function showImg(obj) {
        document.body.style.overflow = 'hidden'
        document.body.style.height = '100%'
        var nextObj = obj.nextElementSibling
        nextObj.style.display = 'block'
        var index = 0;   //每张图片的下标，
        var len = nextObj.getElementsByTagName('img').length;
        var imgCircle = nextObj.getElementsByClassName('img-circle')[0]
        nextObj.onmouseover = function () {  //当鼠标光标停在图片上，则停止轮播
            clearInterval(start);
        }
        nextObj.onmouseout = function () {  //当鼠标光标停在图片上，则开始轮播
            start = setInterval(autoPlay, 1000);
        }
        for (var i = 0; i < len; i++) {
            var fontTag = document.createElement('font')
            imgCircle.appendChild(fontTag)
        }
        var fontTagList = nextObj.getElementsByTagName('font');  //得到所有圆圈
        changeImg(0)
        var funny = function (i) {
            fontTagList[i].onmouseover = function () {
                index = i
                changeImg(i)
            }
        }
        for (var i = 0; i < fontTagList.length; i++) {
            funny(i);
        }

        function autoPlay() {
            if (index > len - 1) {
                index = 0;
                clearInterval(start); //运行一轮后停止
            }
            changeImg(index++);
        }

        imgCircle.style.width = 30 * len + "px";

        // 对应圆圈和图片同步
        function changeImg(index) {
            var imgTags = nextObj.getElementsByTagName('img');
            var fontTags = nextObj.getElementsByTagName('font');
            for (i = 0; i < fontTags.length; i++) {
                imgTags[i].style.display = 'none';
                fontTags[i].style.backgroundColor = 'white';
            }
            imgTags[index].style.display = 'block';
            if (fontTags.length > 0) {
                fontTags[index].style.backgroundColor = 'red';
            }
        }
    }

    function hideImg(obj) {
        document.body.style.overflow = 'auto';
        obj.parentElement.parentElement.parentElement.parentElement.parentElement.style.display = "none";
        obj.parentElement.parentElement.parentElement.getElementsByClassName('img-circle')[0].innerHTML = "";
    }

    output_list = Array();
    /* level
    - 0:Summary
    - 1:Failed
    - 2:Skip
    - 3:All
    */
    // function showCase(level, channel) {
    //     trs = document.getElementsByTagName("tr");
    //     for (var i=0; i < trs.length; i++) {
    //         tr = trs[i];
    //         id = tr.id;
    //         if (["ft", "pt", "et", "st"].indexOf(id.substr(0, 2)) != -1) {
    //             if (level == 0 && id.substr(2, 1) == channel) {
    //                 tr.className = 'hiddenRow';
    //             }
    //         }
    //         if (id.substr(0, 3) == 'pt' + channel) {
    //             if (level == 1) {
    //                 tr.className = '';
    //             }
    //             else if (level > 4 && id.substr(2, 1) == channel) {
    //                 tr.className = '';
    //             }
    //             else {
    //                 tr.className = 'hiddenRow';
    //             }
    //         }
    //         if (id.substr(0, 3) == 'ft' + channel) {
    //             if (level == 2) {
    //                 tr.className = '';
    //             }
    //             else if (level > 4 && id.substr(2, 1) == channel) {
    //                 tr.className = '';
    //             }
    //             else {
    //                 tr.className = 'hiddenRow';
    //             }
    //         }
    //         if (id.substr(0, 3) == 'et' + channel) {
    //             if (level == 3) {
    //                 tr.className = '';
    //             }
    //             else if (level > 4 && id.substr(2, 1) == channel) {
    //                 tr.className = '';
    //             }
    //             else {
    //                 tr.className = 'hiddenRow';
    //             }
    //         }
    //         if (id.substr(0, 3) == 'st' + channel) {
    //             if (level == 4) {
    //                 tr.className = '';
    //             }
    //             else if (level > 4 && id.substr(2, 1) == channel) {
    //                 tr.className = '';
    //             }
    //             else {
    //                 tr.className = 'hiddenRow';
    //             }
    //         }
    //     }
    // }
    // function showClassDetail(cid, count) {
    //     var id_list = Array(count);
    //     var toHide = 1;
    //     for (var i=0; i < count; i++) {
    //         tid0 = 't' + cid.substr(2) + '.' + (i + 1);
    //         tid = 'f' + tid0;
    //         tr = document.getElementById(tid);
    //         if (!tr) {
    //             tid = 'p' + tid0;
    //             tr = document.getElementById(tid);
    //         }
    //         if (!tr) {
    //             tid = 'e' + tid0;
    //             tr = document.getElementById(tid);
    //         }
    //         if (!tr) {
    //             tid = 's' + tid0;
    //             tr = document.getElementById(tid);
    //         }
    //         id_list[i] = tid;
    //         if (tr.className) {
    //             toHide = 0;
    //         }
    //     }
    //     for (var i = 0; i < count; i++) {
    //         tid = id_list[i];
    //         if (toHide) {
    //             document.getElementById(tid).className = 'hiddenRow';
    //         }
    //         else {
    //             document.getElementById(tid).className = '';
    //         }
    //     }
    // }

    function showStep(level, channel) {
        trs = document.getElementsByTagName("tr");
        for (var i = 0; i < trs.length; i++) {
            tr = trs[i];
            id = tr.id;
            // debugger
            console.log("id::", id);
            console.log("id截取::", id.substr(0, 3));
            console.log("idAther::", id.substr(2, 1));
            console.log("channel::", channel)
            if (["ft", "pt", "et", "st"].indexOf(id.substr(0, 2)) != -1) {
                if (level == 0 && id.substr(2, 1) == channel) {
                    tr.className = 'hiddenRow';
                }
            }
            if (id.substr(0, 2) == 'pt') {
                if (level == 1) {
                    tr.className = '';
                } else if (level > 4) {
                    tr.className = '';
                } else {
                    tr.className = 'hiddenRow';
                }
            }
            if (id.substr(0, 2) == 'ft') {
                if (level == 2) {
                    tr.className = '';
                } else if (level > 4) {
                    tr.className = '';
                } else {
                    tr.className = 'hiddenRow';
                }
            }
            if (id.substr(0, 2) == 'et') {

                if (level == 3) {
                    tr.className = '';
                } else if (level > 4) {
                    tr.className = '';
                } else {
                    tr.className = 'hiddenRow';
                }
            }
            if (id.substr(0, 2) == 'st') {
                if (level == 4) {
                    tr.className = '';
                } else if (level > 4) {
                    tr.className = '';
                } else {
                    tr.className = 'hiddenRow';
                }
            }
        }
    }

    function showClassDetail(cid, count) {
        var id_list = Array(count);
        var toHide = 1;
        for (var i = 0; i < count; i++) {
            tid0 = 't' + cid.substr(2) + '.' + (i + 1);
            tid = 'f' + tid0;
            tr = document.getElementById(tid);
            if (!tr) {
                tid = 'p' + tid0;
                tr = document.getElementById(tid);
            }
            if (!tr) {
                tid = 'e' + tid0;
                tr = document.getElementById(tid);
            }
            if (!tr) {
                tid = 's' + tid0;
                tr = document.getElementById(tid);
            }
            id_list[i] = tid;
            if (tr.className) {
                toHide = 0;
            }
        }
        for (var i = 0; i < count; i++) {
            tid = id_list[i];
            if (toHide) {
                document.getElementById(tid).className = 'hiddenRow';
            } else {
                document.getElementById(tid).className = '';
            }
        }
    }

    function showTestDetail(div_id) {
        var detailsDiv = document.getElementById(div_id)
        var displayState = detailsDiv.style.display

        if (displayState != 'block') {
            displayState = 'block'
            detailsDiv.style.display = 'block'
        } else {
            detailsDiv.style.display = 'none'
        }
    }

    function html_escape(s) {
        s = s.replace(/&/g, '&amp;');
        s = s.replace(/</g, '&lt;');
        s = s.replace(/>/g, '&gt;');
        return s;
    }
</script>

<nav class="navbar navbar-light position-lg-sticky top-lg-0 d-none d-lg-block overlap-10 flex-none bg-white border-bottom px-0 py-3" id="topbar">
    <div class="container-fluid">
        <div class="hstack gap-2">
            <a href="">
                <img src="https://img01.weeecdn.net/static/www/_next/static/media/logo.c5d10ee5.svg" style="height: 2.25rem;">
            </a>
        </div>
        <div class="navbar-user d-none d-sm-block">
            <div class="hstack gap-3 ms-4">
                <h3 style="float: right;"> ios ui automation </h3>
            </div>
        </div>
    </div>
</nav>

<div id="headContainer" class="container-fluid mm-active">
    <div class="row">
        <div class="col-12 col-lg-5 col-xl-4 d-flex" style="float:left">
            <div class='card flex-fill'>
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <h5 class="mb-0">Overview</h5>
                    </div>
                </div>
                <div class="card-body py-0 position-relative scrollable-y" style="max-height:300px">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item px-0 position-relative hstack flex-wrap head-list">
                            <div class="flex-1">
                                <div class="d-flex align-items-center mb-1">Tester</div>
                                <div class="d-flex align-items-center">
                                    <div class="text-sm text-muted line-clamp-1 me-auto">weeeTest</div>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item px-0 position-relative hstack flex-wrap head-list">
                            <div class="flex-1">
                                <div class="d-flex align-items-center mb-1">Start time - End time</div>
                                <div class="d-flex align-items-center">
                                    <div class="text-sm text-muted line-clamp-1 me-auto">2025-06-05 15:16:41 - 2025-06-05 15:25:13</div>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item px-0 position-relative hstack flex-wrap head-list">
                            <div class="flex-1">
                                <div class="d-flex align-items-center mb-1">Duration</div>
                                <div class="d-flex align-items-center">
                                    <div class="text-sm text-muted line-clamp-1 me-auto">00:08:35</div>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item px-0 position-relative hstack flex-wrap head-list">
                            <div class="flex-1">
                                <div class="d-flex align-items-center mb-1">Status</div>
                                <div class="d-flex align-items-center">
                                    <div class="text-sm text-muted line-clamp-1 me-auto">
                                        <span class="badge badge-pill bg-soft-success text-success me-2">Passed:3</span>
                                        <span class="badge badge-pill bg-soft-warning text-warning me-2">Failed:4</span>
                                        <span class="badge badge-pill bg-soft-danger text-danger me-2">Errors:6</span>
                                        <span class="badge badge-pill bg-soft-secondary text-secondary me-2">Skipped:0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item px-0 position-relative hstack flex-wrap" style="padding-top: 5px;">
                            <div class="flex-1">
                                <div class="d-flex align-items-center mb-1">Description</div>
                                <div class="d-flex align-items-center">
                                    <div class="text-sm text-muted me-auto">weeeTest Test Report</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="width: 20%">
            <div class="card" style="height: 45%;">
                <div class="card-body">
                    <div class="row">
                        <div class="col"><span class="h6 font-semibold text-muted text-sm d-block mb-2">Passed</span>
                            <span class="h3 font-bold mb-0">3</span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-success text-white text-lg rounded-circle">P</div>
                        </div>
                    </div>
                    <div class="mt-2 mb-0 text-sm">
                        <span class="badge badge-pill bg-soft-success text-success me-2">23.08%</span>
                        <span class="text-nowrap text-xs text-muted">Pass rate</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="progress" style="width:70%; margin-top: 5px;">
                            <div class="progress-bar bg-success" role="progressbar" aria-valuenow="83" aria-valuemin="0"
                                aria-valuemax="100" style="width:23.08%"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card" style="height: 45%; top: 10%;">
                <div class="card-body">
                    <div class="row">
                        <div class="col"><span class="h6 font-semibold text-muted text-sm d-block mb-2">Failed</span>
                            <span class="h3 font-bold mb-0">4</span></div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-warning text-white text-lg rounded-circle">F</div>
                        </div>
                    </div>
                    <div class="mt-2 mb-0 text-sm">
                        <span class="badge badge-pill bg-soft-warning text-warning me-2">30.77%</span>
                        <span class="text-nowrap text-xs text-muted">Failure rate</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="progress" style="width:70%; margin-top: 5px;">
                            <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="83" aria-valuemin="0"
                                aria-valuemax="100" style="width:30.77%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="width: 20%">
            <div class="card" style="height: 45%;">
                <div class="card-body">
                    <div class="row">
                        <div class="col"><span class="h6 font-semibold text-muted text-sm d-block mb-2">Errors</span>
                            <span class="h3 font-bold mb-0">6</span></div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-danger text-white text-lg rounded-circle">E</div>
                        </div>
                    </div>
                    <div class="mt-2 mb-0 text-sm">
                        <span class="badge badge-pill bg-soft-danger text-danger me-2">46.15%</span>
                        <span class="text-nowrap text-xs text-muted">Error rate</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="progress" style="width:70%; margin-top: 5px;">
                            <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="83" aria-valuemin="0"
                                aria-valuemax="100" style="width:46.15%"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card" style="height: 45%; top: 10%;">
                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <span class="h6 font-semibold text-muted text-sm d-block mb-2">Skipped</span>
                            <span class="h3 font-bold mb-0">0</span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-secondary text-white text-lg rounded-circle">S</div>
                        </div>
                    </div>
                    <div class="mt-2 mb-0 text-sm">
                        <span class="badge badge-pill bg-soft-secondary text-secondary me-2">0.0%</span>
                        <span class="text-nowrap text-xs text-muted">Skip rate</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="progress" style="width:70%; margin-top: 5px;">
                            <div class="progress-bar bg-secondary" role="progressbar" aria-valuenow="83" aria-valuemin="0"
                                aria-valuemax="100" style="width:0.0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="resultContainer" class="card">
    <div class="card-header border-bottom">
        <span style="float: left;">
            <h5 class="mb-0">Result</h5>
        </span>
        <span style="float: right;">
            <a href='javascript:showStep(0, 0)' class="btn btn-dark btn-sm">Summary</a>
            <a href='javascript:showStep(1, 0)' class="btn btn-success btn-sm">Pass</a>
            <a href='javascript:showStep(2, 0)' class="btn btn-warning btn-sm">Failed</a>
            <a href='javascript:showStep(3, 0)' class="btn btn-danger btn-sm">Error</a>
            <a href='javascript:showStep(4, 0)' class="btn btn-secondary btn-sm">Skip</a>
            <a href='javascript:showStep(5, 0)' class="btn btn-info btn-sm">All</a>
        </span>
    </div>
    <div class="table-responsive">
        <table class="table table-hover table-nowrap">
            <thead class="table-light">
            <tr>
                <th scope="col">Case Description</th>
                <th scope="col">Test Group/Test Case</th>
                <th scope="col">case_type</th>
                <th scope="col">Duration</th>
                <!--                    <th scope="col">Count(times)</th>-->
                <!--                    <th scope="col">Pass(times)</th>-->
                <!--                    <th scope="col">Fail(times)</th>-->
                <!--                    <th scope="col">Error(times)</th>-->
                <th scope="col">status</th>
                <th scope="col">View</th>
                <!--                <th scope="col">Screenshots</th>-->
            </tr>
            </thead>
            <tbody>
            
    <tr class='passClass'>
        <td>
        测试步骤：
        1. 进入account页面
        2. 点击my orders,进入orders页面
        3. 依次点击"pending, unshipped, shipped, to review, cancelled"查看订单
        </td>
        <td>src/EC/tests/account/test_001_orders.py::TestOrders::test_001_check_order_tabs[setup0]</td>
        <td>scene</td>
         <td>38.5251  s</td>
        <td>pass</td>
        <td><a href="javascript:showClassDetail('c.1',3)">detail</a></td>
    </tr>
    
<tr id='pt1.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>10.1866 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt1.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>10.1866 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt1.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>27.8624 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt1.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>27.8624 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt1.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.4761 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt1.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.4761 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='passClass'>
        <td>
        测试步骤：
        1. 进入Gift card主页面，处理欢迎弹窗
        2. 填入收件人邮箱，验证输入成功
        3. 点击checkout，进入礼品卡checkout页面
        4. 点击支付方式下拉栏，选择PayPal
        5. 点击place order，跳转到PayPal第三方支付页面
        </td>
        <td>src/EC/tests/account/test_112237_gift_card_purchase_flow.py::TestGiftCardPurchaseFlow::test_112237_gift_card_purchase_flow_complete[setup0]</td>
        <td>scene</td>
         <td>57.2701  s</td>
        <td>pass</td>
        <td><a href="javascript:showClassDetail('c.2',3)">detail</a></td>
    </tr>
    
<tr id='pt2.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>4.2249 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt2.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>4.2249 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt2.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>52.219 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt2.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>52.219 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt2.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.8262 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt2.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.8262 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='failClass'>
        <td></td>
        <td>src/EC/tests/explore/test_112058_android_product_recommend_filter_verify.py::TestAndroidProductRecommendFilterVerify::test_112058_deals_android_product_recommend_filter_verify</td>
        <td>scene</td>
         <td>30.235  s</td>
        <td>fail</td>
        <td><a href="javascript:showClassDetail('c.3',3)">detail</a></td>
    </tr>
    
<tr id='pt3.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>3.3876 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt3.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>3.3876 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='ft3.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>26.8474 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft3.2')">fail</a>
        <div id='div_ft3.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft3.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:'NoneType' object has no attribute 'text'

error_message:self = <test_112058_android_product_recommend_filter_verify.TestAndroidProductRecommendFilterVerify object at 0x1043658b0>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>
android_header = {'Content-Type': 'application/json;charset=UTF-8', 'app-version': 'null', 'authorization': 'Bearer eyJraWQiOiJkZjBlZDI...m2CatFzGC658V3Gtu-olGGyEBB59kf_Y6Y-GfUpIAaAF5xnqP_fgY3otWT-cWTfvZzt1UtAFoSyRs1X38s_5XU', 'b-cookie': '1276994989', ...}
get_platform = 'iOS'

    @allure.title("Android-deals商品推荐规则filter 查询验证")
    def test_112058_deals_android_product_recommend_filter_verify(self, get_driver, android_header, get_platform):
        d = get_driver
        explore_page = ExplorePage(d, get_platform)
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("home")).click()
        time.sleep(15)
>       _text = self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("zipcode")).text
E       AttributeError: 'NoneType' object has no attribute 'text'

src/EC/tests/explore/test_112058_android_product_recommend_filter_verify.py:24: AttributeError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='ft3.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>26.8474 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft3.2')">fail</a>
        <div id='div_ft3.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft3.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:'NoneType' object has no attribute 'text'

error_message:self = <test_112058_android_product_recommend_filter_verify.TestAndroidProductRecommendFilterVerify object at 0x1043658b0>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>
android_header = {'Content-Type': 'application/json;charset=UTF-8', 'app-version': 'null', 'authorization': 'Bearer eyJraWQiOiJkZjBlZDI...m2CatFzGC658V3Gtu-olGGyEBB59kf_Y6Y-GfUpIAaAF5xnqP_fgY3otWT-cWTfvZzt1UtAFoSyRs1X38s_5XU', 'b-cookie': '1276994989', ...}
get_platform = 'iOS'

    @allure.title("Android-deals商品推荐规则filter 查询验证")
    def test_112058_deals_android_product_recommend_filter_verify(self, get_driver, android_header, get_platform):
        d = get_driver
        explore_page = ExplorePage(d, get_platform)
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("home")).click()
        time.sleep(15)
>       _text = self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("zipcode")).text
E       AttributeError: 'NoneType' object has no attribute 'text'

src/EC/tests/explore/test_112058_android_product_recommend_filter_verify.py:24: AttributeError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='pt3.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.001 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt3.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.001 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='failClass'>
        <td></td>
        <td>src/EC/tests/explore/test_112058_android_product_recommend_filter_verify.py::TestAndroidProductRecommendFilterVerify::test_112058_new_arrivals_android_product_recommend_filter_verify</td>
        <td>scene</td>
         <td>26.4229  s</td>
        <td>fail</td>
        <td><a href="javascript:showClassDetail('c.4',3)">detail</a></td>
    </tr>
    
<tr id='pt4.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0002 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt4.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0002 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='ft4.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>26.4227 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft4.2')">fail</a>
        <div id='div_ft4.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft4.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:'NoneType' object has no attribute 'text'

error_message:self = <test_112058_android_product_recommend_filter_verify.TestAndroidProductRecommendFilterVerify object at 0x104365880>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>
android_header = {'Content-Type': 'application/json;charset=UTF-8', 'app-version': 'null', 'authorization': 'Bearer eyJraWQiOiJkZjBlZDI...m2CatFzGC658V3Gtu-olGGyEBB59kf_Y6Y-GfUpIAaAF5xnqP_fgY3otWT-cWTfvZzt1UtAFoSyRs1X38s_5XU', 'b-cookie': '1276994989', ...}
get_platform = 'iOS'

    @allure.title("Android-new arrivals商品推荐规则filter 查询验证")
    def test_112058_new_arrivals_android_product_recommend_filter_verify(self, get_driver, android_header, get_platform):
        d = get_driver
        explore_page = ExplorePage(d, get_platform)
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("home")).click()
        time.sleep(15)
>       _text = self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("zipcode")).text
E       AttributeError: 'NoneType' object has no attribute 'text'

src/EC/tests/explore/test_112058_android_product_recommend_filter_verify.py:101: AttributeError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='ft4.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>26.4227 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft4.2')">fail</a>
        <div id='div_ft4.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft4.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:'NoneType' object has no attribute 'text'

error_message:self = <test_112058_android_product_recommend_filter_verify.TestAndroidProductRecommendFilterVerify object at 0x104365880>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>
android_header = {'Content-Type': 'application/json;charset=UTF-8', 'app-version': 'null', 'authorization': 'Bearer eyJraWQiOiJkZjBlZDI...m2CatFzGC658V3Gtu-olGGyEBB59kf_Y6Y-GfUpIAaAF5xnqP_fgY3otWT-cWTfvZzt1UtAFoSyRs1X38s_5XU', 'b-cookie': '1276994989', ...}
get_platform = 'iOS'

    @allure.title("Android-new arrivals商品推荐规则filter 查询验证")
    def test_112058_new_arrivals_android_product_recommend_filter_verify(self, get_driver, android_header, get_platform):
        d = get_driver
        explore_page = ExplorePage(d, get_platform)
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("home")).click()
        time.sleep(15)
>       _text = self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("zipcode")).text
E       AttributeError: 'NoneType' object has no attribute 'text'

src/EC/tests/explore/test_112058_android_product_recommend_filter_verify.py:101: AttributeError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='pt4.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0016 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt4.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0016 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='failClass'>
        <td></td>
        <td>src/EC/tests/explore/test_112058_android_product_recommend_filter_verify.py::TestAndroidProductRecommendFilterVerify::test_112058_best_sellers_android_product_recommend_filter_verify</td>
        <td>scene</td>
         <td>26.7325  s</td>
        <td>fail</td>
        <td><a href="javascript:showClassDetail('c.5',3)">detail</a></td>
    </tr>
    
<tr id='pt5.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0004 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt5.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0004 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='ft5.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>26.7321 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft5.2')">fail</a>
        <div id='div_ft5.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft5.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:'NoneType' object has no attribute 'text'

error_message:self = <test_112058_android_product_recommend_filter_verify.TestAndroidProductRecommendFilterVerify object at 0x1043651f0>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>
android_header = {'Content-Type': 'application/json;charset=UTF-8', 'app-version': 'null', 'authorization': 'Bearer eyJraWQiOiJkZjBlZDI...m2CatFzGC658V3Gtu-olGGyEBB59kf_Y6Y-GfUpIAaAF5xnqP_fgY3otWT-cWTfvZzt1UtAFoSyRs1X38s_5XU', 'b-cookie': '1276994989', ...}
get_platform = 'iOS'

    @allure.title("Android-Best sellers商品推荐规则filter 查询验证")
    def test_112058_best_sellers_android_product_recommend_filter_verify(self, get_driver, android_header, get_platform):
        d = get_driver
        explore_page = ExplorePage(d, get_platform)
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("home")).click()
        time.sleep(15)
>       _text = self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("zipcode")).text
E       AttributeError: 'NoneType' object has no attribute 'text'

src/EC/tests/explore/test_112058_android_product_recommend_filter_verify.py:182: AttributeError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='ft5.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>26.7321 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft5.2')">fail</a>
        <div id='div_ft5.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft5.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:'NoneType' object has no attribute 'text'

error_message:self = <test_112058_android_product_recommend_filter_verify.TestAndroidProductRecommendFilterVerify object at 0x1043651f0>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>
android_header = {'Content-Type': 'application/json;charset=UTF-8', 'app-version': 'null', 'authorization': 'Bearer eyJraWQiOiJkZjBlZDI...m2CatFzGC658V3Gtu-olGGyEBB59kf_Y6Y-GfUpIAaAF5xnqP_fgY3otWT-cWTfvZzt1UtAFoSyRs1X38s_5XU', 'b-cookie': '1276994989', ...}
get_platform = 'iOS'

    @allure.title("Android-Best sellers商品推荐规则filter 查询验证")
    def test_112058_best_sellers_android_product_recommend_filter_verify(self, get_driver, android_header, get_platform):
        d = get_driver
        explore_page = ExplorePage(d, get_platform)
        self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("home")).click()
        time.sleep(15)
>       _text = self.find_element(_driver=d, ele=explore_page.strategies.get(get_platform).get("zipcode")).text
E       AttributeError: 'NoneType' object has no attribute 'text'

src/EC/tests/explore/test_112058_android_product_recommend_filter_verify.py:182: AttributeError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='pt5.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0015 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt5.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0015 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='passClass'>
        <td>先加购，checkout时再登陆</td>
        <td>src/EC/tests/onboarding/test_000_onboarding.py::TestOnboarding::test_000_onboarding_without_login[setup0]</td>
        <td>scene</td>
         <td>182.1797  s</td>
        <td>pass</td>
        <td><a href="javascript:showClassDetail('c.6',3)">detail</a></td>
    </tr>
    
<tr id='pt6.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>68.9367 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt6.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>68.9367 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt6.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>110.4237 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt6.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>110.4237 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt6.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>2.8193 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt6.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>2.8193 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='failClass'>
        <td>先登陆后加购</td>
        <td>src/EC/tests/onboarding/test_000_onboarding.py::TestOnboarding::test_001_onboarding_with_login[setup0]</td>
        <td>scene</td>
         <td>147.5136  s</td>
        <td>fail</td>
        <td><a href="javascript:showClassDetail('c.7',3)">detail</a></td>
    </tr>
    
<tr id='pt7.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>50.3664 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt7.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>50.3664 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='ft7.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>97.1472 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft7.2')">fail</a>
        <div id='div_ft7.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft7.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:'NoneType' object has no attribute 'click'

error_message:self = <src.EC.tests.onboarding.test_000_onboarding.TestOnboarding object at 0x1043f6300>
get_onboarding_driver = <appium.webdriver.webdriver.WebDriver (session="807b8bc7-db47-4be5-88ad-8e32ce9e75ea")>
get_platform = 'iOS', setup = None

    @allure.title("onboarding-先登陆后购买")
    @pytest.mark.parametrize('setup', [('test_001_onboarding_with_login', )], indirect=True)
    def test_001_onboarding_with_login(self, get_onboarding_driver, get_platform, setup):
        """先登陆后加购"""
        d = get_onboarding_driver
        onboarding_page = Onboarding(d, get_platform)
        onboarding_page.onboarding()
        # 登陆
        self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get("home_account")).click()
        self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get("login_button")).click()
        onboarding_page.login_with_email(email='<EMAIL>', password='********')
        time.sleep(5)
        # ios no pick price page
        pick_price_skip = self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get("pick_price_skip"))
        # 这里是动态的
        if pick_price_skip:
            pick_price_skip.click()
        else:
>           self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get("home")).click()
E           AttributeError: 'NoneType' object has no attribute 'click'

src/EC/tests/onboarding/test_000_onboarding.py:61: AttributeError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='ft7.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>97.1472 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft7.2')">fail</a>
        <div id='div_ft7.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft7.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:'NoneType' object has no attribute 'click'

error_message:self = <src.EC.tests.onboarding.test_000_onboarding.TestOnboarding object at 0x1043f6300>
get_onboarding_driver = <appium.webdriver.webdriver.WebDriver (session="807b8bc7-db47-4be5-88ad-8e32ce9e75ea")>
get_platform = 'iOS', setup = None

    @allure.title("onboarding-先登陆后购买")
    @pytest.mark.parametrize('setup', [('test_001_onboarding_with_login', )], indirect=True)
    def test_001_onboarding_with_login(self, get_onboarding_driver, get_platform, setup):
        """先登陆后加购"""
        d = get_onboarding_driver
        onboarding_page = Onboarding(d, get_platform)
        onboarding_page.onboarding()
        # 登陆
        self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get("home_account")).click()
        self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get("login_button")).click()
        onboarding_page.login_with_email(email='<EMAIL>', password='********')
        time.sleep(5)
        # ios no pick price page
        pick_price_skip = self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get("pick_price_skip"))
        # 这里是动态的
        if pick_price_skip:
            pick_price_skip.click()
        else:
>           self.find_element(_driver=d, ele=onboarding_page.strategies.get(get_platform).get("home")).click()
E           AttributeError: 'NoneType' object has no attribute 'click'

src/EC/tests/onboarding/test_000_onboarding.py:61: AttributeError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='pt7.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>2.5186 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt7.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>2.5186 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='errorClass'>
        <td>
        测试步骤：
        1. 进入产品详情页
        2. 验证Reviews栏是否展示
        3. 验证总review数是否正确显示
        4. 验证Customers say栏是否展示
        5. 验证AI-generated文本样式
        </td>
        <td>src/EC/tests/product/test_112777_pdp_reviews_selling_points.py::TestPDPReviewsSellingPoints::test_112777_reviews_section_display[setup0]</td>
        <td>scene</td>
         <td>0.0854  s</td>
        <td>error</td>
        <td><a href="javascript:showClassDetail('c.8',2)">detail</a></td>
    </tr>
    
<tr id='et8.1' class='hiddenRow'>
    <td class='errorCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0854 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_et8.1')">error</a>
        <div id='div_et8.1' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">SetUp</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_et8.1')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
    at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
    at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

error_message:self = <src.EC.tests.product.test_112777_pdp_reviews_selling_points.TestPDPReviewsSellingPoints object at 0x10431f080>
request = <SubRequest 'setup' for <Function test_112777_reviews_section_display[setup0]>>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
>       get_driver.start_recording_screen()

src/EC/tests/product/test_112777_pdp_reviews_selling_points.py:21: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/screen_record.py:162: in start_recording_screen
    return self.execute(Command.START_RECORDING_SCREEN, {'options': options})['value']
qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute
    self.error_handler.check_response(response)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x1043f5940>
response = {'status': 500, 'value': '{"value":{"error":"unknown error","message":"An unknown server-side error occurred while pro...cordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)"}}'}

    def check_response(self, response: Dict[str, Any]) -> None:
        """
        https://www.w3.org/TR/webdriver/#errors
        """
        payload = response.get('value', '')
        if isinstance(payload, dict):
            payload_dict = payload
        else:
            try:
                payload_dict = json.loads(payload)
            except (json.JSONDecodeError, TypeError):
                return
            if not isinstance(payload_dict, dict):
                return
        value = payload_dict.get('value')
        if not isinstance(value, dict):
            return
        error = value.get('error')
        if not error:
            return
    
        message = value.get('message', error)
        stacktrace = value.get('stacktrace', '')
        # In theory, we should also be checking HTTP status codes.
        # Java client, for example, prints a warning if the actual `error`
        # value does not match to the response's HTTP status code.
        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(
            error, sel_exceptions.WebDriverException
        )
        if exception_class is sel_exceptions.WebDriverException and message:
            if message == 'No such context found.':
                exception_class = appium_exceptions.NoSuchContextException
            elif message == 'That command could not be executed in the current context.':
                exception_class = appium_exceptions.InvalidSwitchToTargetException
    
        if exception_class is sel_exceptions.UnexpectedAlertPresentException:
            raise sel_exceptions.UnexpectedAlertPresentException(
                msg=message,
                stacktrace=format_stacktrace(stacktrace),
                alert_text=value.get('data'),
            )
>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))
E       selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E       Stacktrace:
E       UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E           at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
E           at processTicksAndRejections (node:internal/process/task_queues:95:5)
E           at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
E           at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: WebDriverException</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='et8.1' class='hiddenRow'>
    <td class='errorCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0854 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_et8.1')">error</a>
        <div id='div_et8.1' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">SetUp</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_et8.1')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
    at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
    at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

error_message:self = <src.EC.tests.product.test_112777_pdp_reviews_selling_points.TestPDPReviewsSellingPoints object at 0x10431f080>
request = <SubRequest 'setup' for <Function test_112777_reviews_section_display[setup0]>>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
>       get_driver.start_recording_screen()

src/EC/tests/product/test_112777_pdp_reviews_selling_points.py:21: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/screen_record.py:162: in start_recording_screen
    return self.execute(Command.START_RECORDING_SCREEN, {'options': options})['value']
qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute
    self.error_handler.check_response(response)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x1043f5940>
response = {'status': 500, 'value': '{"value":{"error":"unknown error","message":"An unknown server-side error occurred while pro...cordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)"}}'}

    def check_response(self, response: Dict[str, Any]) -> None:
        """
        https://www.w3.org/TR/webdriver/#errors
        """
        payload = response.get('value', '')
        if isinstance(payload, dict):
            payload_dict = payload
        else:
            try:
                payload_dict = json.loads(payload)
            except (json.JSONDecodeError, TypeError):
                return
            if not isinstance(payload_dict, dict):
                return
        value = payload_dict.get('value')
        if not isinstance(value, dict):
            return
        error = value.get('error')
        if not error:
            return
    
        message = value.get('message', error)
        stacktrace = value.get('stacktrace', '')
        # In theory, we should also be checking HTTP status codes.
        # Java client, for example, prints a warning if the actual `error`
        # value does not match to the response's HTTP status code.
        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(
            error, sel_exceptions.WebDriverException
        )
        if exception_class is sel_exceptions.WebDriverException and message:
            if message == 'No such context found.':
                exception_class = appium_exceptions.NoSuchContextException
            elif message == 'That command could not be executed in the current context.':
                exception_class = appium_exceptions.InvalidSwitchToTargetException
    
        if exception_class is sel_exceptions.UnexpectedAlertPresentException:
            raise sel_exceptions.UnexpectedAlertPresentException(
                msg=message,
                stacktrace=format_stacktrace(stacktrace),
                alert_text=value.get('data'),
            )
>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))
E       selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E       Stacktrace:
E       UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E           at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
E           at processTicksAndRejections (node:internal/process/task_queues:95:5)
E           at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
E           at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: WebDriverException</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='pt8.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0019 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt8.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0019 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='errorClass'>
        <td>
        测试步骤：
        1. 进入产品详情页
        2. 验证视频栏展示及视频个数显示
        </td>
        <td>src/EC/tests/product/test_112812_pdp_video_features.py::TestPDPVideoFeatures::test_112812_pdp_video_ui_display[setup0]</td>
        <td>scene</td>
         <td>0.008  s</td>
        <td>error</td>
        <td><a href="javascript:showClassDetail('c.9',2)">detail</a></td>
    </tr>
    
<tr id='et9.1' class='hiddenRow'>
    <td class='errorCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.008 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_et9.1')">error</a>
        <div id='div_et9.1' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">SetUp</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_et9.1')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
    at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
    at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

error_message:self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1043f5eb0>
request = <SubRequest 'setup' for <Function test_112812_pdp_video_ui_display[setup0]>>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
>       get_driver.start_recording_screen()

src/EC/tests/product/test_112812_pdp_video_features.py:22: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/screen_record.py:162: in start_recording_screen
    return self.execute(Command.START_RECORDING_SCREEN, {'options': options})['value']
qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute
    self.error_handler.check_response(response)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x1043f5940>
response = {'status': 500, 'value': '{"value":{"error":"unknown error","message":"An unknown server-side error occurred while pro...cordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)"}}'}

    def check_response(self, response: Dict[str, Any]) -> None:
        """
        https://www.w3.org/TR/webdriver/#errors
        """
        payload = response.get('value', '')
        if isinstance(payload, dict):
            payload_dict = payload
        else:
            try:
                payload_dict = json.loads(payload)
            except (json.JSONDecodeError, TypeError):
                return
            if not isinstance(payload_dict, dict):
                return
        value = payload_dict.get('value')
        if not isinstance(value, dict):
            return
        error = value.get('error')
        if not error:
            return
    
        message = value.get('message', error)
        stacktrace = value.get('stacktrace', '')
        # In theory, we should also be checking HTTP status codes.
        # Java client, for example, prints a warning if the actual `error`
        # value does not match to the response's HTTP status code.
        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(
            error, sel_exceptions.WebDriverException
        )
        if exception_class is sel_exceptions.WebDriverException and message:
            if message == 'No such context found.':
                exception_class = appium_exceptions.NoSuchContextException
            elif message == 'That command could not be executed in the current context.':
                exception_class = appium_exceptions.InvalidSwitchToTargetException
    
        if exception_class is sel_exceptions.UnexpectedAlertPresentException:
            raise sel_exceptions.UnexpectedAlertPresentException(
                msg=message,
                stacktrace=format_stacktrace(stacktrace),
                alert_text=value.get('data'),
            )
>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))
E       selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E       Stacktrace:
E       UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E           at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
E           at processTicksAndRejections (node:internal/process/task_queues:95:5)
E           at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
E           at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: WebDriverException</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='et9.1' class='hiddenRow'>
    <td class='errorCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.008 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_et9.1')">error</a>
        <div id='div_et9.1' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">SetUp</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_et9.1')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
    at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
    at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

error_message:self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1043f5eb0>
request = <SubRequest 'setup' for <Function test_112812_pdp_video_ui_display[setup0]>>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
>       get_driver.start_recording_screen()

src/EC/tests/product/test_112812_pdp_video_features.py:22: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/screen_record.py:162: in start_recording_screen
    return self.execute(Command.START_RECORDING_SCREEN, {'options': options})['value']
qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute
    self.error_handler.check_response(response)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x1043f5940>
response = {'status': 500, 'value': '{"value":{"error":"unknown error","message":"An unknown server-side error occurred while pro...cordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)"}}'}

    def check_response(self, response: Dict[str, Any]) -> None:
        """
        https://www.w3.org/TR/webdriver/#errors
        """
        payload = response.get('value', '')
        if isinstance(payload, dict):
            payload_dict = payload
        else:
            try:
                payload_dict = json.loads(payload)
            except (json.JSONDecodeError, TypeError):
                return
            if not isinstance(payload_dict, dict):
                return
        value = payload_dict.get('value')
        if not isinstance(value, dict):
            return
        error = value.get('error')
        if not error:
            return
    
        message = value.get('message', error)
        stacktrace = value.get('stacktrace', '')
        # In theory, we should also be checking HTTP status codes.
        # Java client, for example, prints a warning if the actual `error`
        # value does not match to the response's HTTP status code.
        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(
            error, sel_exceptions.WebDriverException
        )
        if exception_class is sel_exceptions.WebDriverException and message:
            if message == 'No such context found.':
                exception_class = appium_exceptions.NoSuchContextException
            elif message == 'That command could not be executed in the current context.':
                exception_class = appium_exceptions.InvalidSwitchToTargetException
    
        if exception_class is sel_exceptions.UnexpectedAlertPresentException:
            raise sel_exceptions.UnexpectedAlertPresentException(
                msg=message,
                stacktrace=format_stacktrace(stacktrace),
                alert_text=value.get('data'),
            )
>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))
E       selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E       Stacktrace:
E       UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E           at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
E           at processTicksAndRejections (node:internal/process/task_queues:95:5)
E           at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
E           at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: WebDriverException</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='pt9.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0019 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt9.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0019 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='errorClass'>
        <td>
        测试步骤：
        1. 进入产品详情页
        2. 验证视频栏分类排序是否为 review > recipes > unboxing
        </td>
        <td>src/EC/tests/product/test_112812_pdp_video_features.py::TestPDPVideoFeatures::test_112812_video_categories_order[setup0]</td>
        <td>scene</td>
         <td>0.009  s</td>
        <td>error</td>
        <td><a href="javascript:showClassDetail('c.10',2)">detail</a></td>
    </tr>
    
<tr id='et10.1' class='hiddenRow'>
    <td class='errorCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.009 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_et10.1')">error</a>
        <div id='div_et10.1' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">SetUp</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_et10.1')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
    at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
    at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

error_message:self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1043f62a0>
request = <SubRequest 'setup' for <Function test_112812_video_categories_order[setup0]>>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
>       get_driver.start_recording_screen()

src/EC/tests/product/test_112812_pdp_video_features.py:22: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/screen_record.py:162: in start_recording_screen
    return self.execute(Command.START_RECORDING_SCREEN, {'options': options})['value']
qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute
    self.error_handler.check_response(response)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x1043f5940>
response = {'status': 500, 'value': '{"value":{"error":"unknown error","message":"An unknown server-side error occurred while pro...cordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)"}}'}

    def check_response(self, response: Dict[str, Any]) -> None:
        """
        https://www.w3.org/TR/webdriver/#errors
        """
        payload = response.get('value', '')
        if isinstance(payload, dict):
            payload_dict = payload
        else:
            try:
                payload_dict = json.loads(payload)
            except (json.JSONDecodeError, TypeError):
                return
            if not isinstance(payload_dict, dict):
                return
        value = payload_dict.get('value')
        if not isinstance(value, dict):
            return
        error = value.get('error')
        if not error:
            return
    
        message = value.get('message', error)
        stacktrace = value.get('stacktrace', '')
        # In theory, we should also be checking HTTP status codes.
        # Java client, for example, prints a warning if the actual `error`
        # value does not match to the response's HTTP status code.
        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(
            error, sel_exceptions.WebDriverException
        )
        if exception_class is sel_exceptions.WebDriverException and message:
            if message == 'No such context found.':
                exception_class = appium_exceptions.NoSuchContextException
            elif message == 'That command could not be executed in the current context.':
                exception_class = appium_exceptions.InvalidSwitchToTargetException
    
        if exception_class is sel_exceptions.UnexpectedAlertPresentException:
            raise sel_exceptions.UnexpectedAlertPresentException(
                msg=message,
                stacktrace=format_stacktrace(stacktrace),
                alert_text=value.get('data'),
            )
>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))
E       selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E       Stacktrace:
E       UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E           at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
E           at processTicksAndRejections (node:internal/process/task_queues:95:5)
E           at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
E           at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: WebDriverException</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='et10.1' class='hiddenRow'>
    <td class='errorCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.009 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_et10.1')">error</a>
        <div id='div_et10.1' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">SetUp</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_et10.1')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
    at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
    at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

error_message:self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1043f62a0>
request = <SubRequest 'setup' for <Function test_112812_video_categories_order[setup0]>>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
>       get_driver.start_recording_screen()

src/EC/tests/product/test_112812_pdp_video_features.py:22: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/screen_record.py:162: in start_recording_screen
    return self.execute(Command.START_RECORDING_SCREEN, {'options': options})['value']
qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute
    self.error_handler.check_response(response)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x1043f5940>
response = {'status': 500, 'value': '{"value":{"error":"unknown error","message":"An unknown server-side error occurred while pro...cordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)"}}'}

    def check_response(self, response: Dict[str, Any]) -> None:
        """
        https://www.w3.org/TR/webdriver/#errors
        """
        payload = response.get('value', '')
        if isinstance(payload, dict):
            payload_dict = payload
        else:
            try:
                payload_dict = json.loads(payload)
            except (json.JSONDecodeError, TypeError):
                return
            if not isinstance(payload_dict, dict):
                return
        value = payload_dict.get('value')
        if not isinstance(value, dict):
            return
        error = value.get('error')
        if not error:
            return
    
        message = value.get('message', error)
        stacktrace = value.get('stacktrace', '')
        # In theory, we should also be checking HTTP status codes.
        # Java client, for example, prints a warning if the actual `error`
        # value does not match to the response's HTTP status code.
        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(
            error, sel_exceptions.WebDriverException
        )
        if exception_class is sel_exceptions.WebDriverException and message:
            if message == 'No such context found.':
                exception_class = appium_exceptions.NoSuchContextException
            elif message == 'That command could not be executed in the current context.':
                exception_class = appium_exceptions.InvalidSwitchToTargetException
    
        if exception_class is sel_exceptions.UnexpectedAlertPresentException:
            raise sel_exceptions.UnexpectedAlertPresentException(
                msg=message,
                stacktrace=format_stacktrace(stacktrace),
                alert_text=value.get('data'),
            )
>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))
E       selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E       Stacktrace:
E       UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E           at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
E           at processTicksAndRejections (node:internal/process/task_queues:95:5)
E           at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
E           at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: WebDriverException</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='pt10.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0018 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt10.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0018 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='errorClass'>
        <td>
        测试步骤：
        1. 进入产品详情页
        2. 点击See All
        3. 验证是否跳转到Explore页面并展示全部视频
        </td>
        <td>src/EC/tests/product/test_112812_pdp_video_features.py::TestPDPVideoFeatures::test_003_see_all_navigation[setup0]</td>
        <td>scene</td>
         <td>0.0094  s</td>
        <td>error</td>
        <td><a href="javascript:showClassDetail('c.11',2)">detail</a></td>
    </tr>
    
<tr id='et11.1' class='hiddenRow'>
    <td class='errorCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0094 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_et11.1')">error</a>
        <div id='div_et11.1' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">SetUp</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_et11.1')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
    at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
    at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

error_message:self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1043f7110>
request = <SubRequest 'setup' for <Function test_003_see_all_navigation[setup0]>>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
>       get_driver.start_recording_screen()

src/EC/tests/product/test_112812_pdp_video_features.py:22: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/screen_record.py:162: in start_recording_screen
    return self.execute(Command.START_RECORDING_SCREEN, {'options': options})['value']
qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute
    self.error_handler.check_response(response)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x1043f5940>
response = {'status': 500, 'value': '{"value":{"error":"unknown error","message":"An unknown server-side error occurred while pro...cordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)"}}'}

    def check_response(self, response: Dict[str, Any]) -> None:
        """
        https://www.w3.org/TR/webdriver/#errors
        """
        payload = response.get('value', '')
        if isinstance(payload, dict):
            payload_dict = payload
        else:
            try:
                payload_dict = json.loads(payload)
            except (json.JSONDecodeError, TypeError):
                return
            if not isinstance(payload_dict, dict):
                return
        value = payload_dict.get('value')
        if not isinstance(value, dict):
            return
        error = value.get('error')
        if not error:
            return
    
        message = value.get('message', error)
        stacktrace = value.get('stacktrace', '')
        # In theory, we should also be checking HTTP status codes.
        # Java client, for example, prints a warning if the actual `error`
        # value does not match to the response's HTTP status code.
        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(
            error, sel_exceptions.WebDriverException
        )
        if exception_class is sel_exceptions.WebDriverException and message:
            if message == 'No such context found.':
                exception_class = appium_exceptions.NoSuchContextException
            elif message == 'That command could not be executed in the current context.':
                exception_class = appium_exceptions.InvalidSwitchToTargetException
    
        if exception_class is sel_exceptions.UnexpectedAlertPresentException:
            raise sel_exceptions.UnexpectedAlertPresentException(
                msg=message,
                stacktrace=format_stacktrace(stacktrace),
                alert_text=value.get('data'),
            )
>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))
E       selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E       Stacktrace:
E       UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E           at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
E           at processTicksAndRejections (node:internal/process/task_queues:95:5)
E           at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
E           at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: WebDriverException</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='et11.1' class='hiddenRow'>
    <td class='errorCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0094 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_et11.1')">error</a>
        <div id='div_et11.1' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">SetUp</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_et11.1')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
    at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
    at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

error_message:self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1043f7110>
request = <SubRequest 'setup' for <Function test_003_see_all_navigation[setup0]>>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
>       get_driver.start_recording_screen()

src/EC/tests/product/test_112812_pdp_video_features.py:22: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/screen_record.py:162: in start_recording_screen
    return self.execute(Command.START_RECORDING_SCREEN, {'options': options})['value']
qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute
    self.error_handler.check_response(response)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x1043f5940>
response = {'status': 500, 'value': '{"value":{"error":"unknown error","message":"An unknown server-side error occurred while pro...cordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)"}}'}

    def check_response(self, response: Dict[str, Any]) -> None:
        """
        https://www.w3.org/TR/webdriver/#errors
        """
        payload = response.get('value', '')
        if isinstance(payload, dict):
            payload_dict = payload
        else:
            try:
                payload_dict = json.loads(payload)
            except (json.JSONDecodeError, TypeError):
                return
            if not isinstance(payload_dict, dict):
                return
        value = payload_dict.get('value')
        if not isinstance(value, dict):
            return
        error = value.get('error')
        if not error:
            return
    
        message = value.get('message', error)
        stacktrace = value.get('stacktrace', '')
        # In theory, we should also be checking HTTP status codes.
        # Java client, for example, prints a warning if the actual `error`
        # value does not match to the response's HTTP status code.
        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(
            error, sel_exceptions.WebDriverException
        )
        if exception_class is sel_exceptions.WebDriverException and message:
            if message == 'No such context found.':
                exception_class = appium_exceptions.NoSuchContextException
            elif message == 'That command could not be executed in the current context.':
                exception_class = appium_exceptions.InvalidSwitchToTargetException
    
        if exception_class is sel_exceptions.UnexpectedAlertPresentException:
            raise sel_exceptions.UnexpectedAlertPresentException(
                msg=message,
                stacktrace=format_stacktrace(stacktrace),
                alert_text=value.get('data'),
            )
>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))
E       selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E       Stacktrace:
E       UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E           at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
E           at processTicksAndRejections (node:internal/process/task_queues:95:5)
E           at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
E           at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: WebDriverException</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='pt11.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0011 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt11.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0011 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='errorClass'>
        <td>
        测试步骤：
        1. 进入产品详情页
        2. 验证PDP下方是否展示发布视频tip词
        3. 验证发布视频按钮是否可点击
        </td>
        <td>src/EC/tests/product/test_112812_pdp_video_features.py::TestPDPVideoFeatures::test_004_publish_video_entry[setup0]</td>
        <td>scene</td>
         <td>0.0148  s</td>
        <td>error</td>
        <td><a href="javascript:showClassDetail('c.12',2)">detail</a></td>
    </tr>
    
<tr id='et12.1' class='hiddenRow'>
    <td class='errorCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0148 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_et12.1')">error</a>
        <div id='div_et12.1' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">SetUp</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_et12.1')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
    at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
    at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

error_message:self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1043f7320>
request = <SubRequest 'setup' for <Function test_004_publish_video_entry[setup0]>>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
>       get_driver.start_recording_screen()

src/EC/tests/product/test_112812_pdp_video_features.py:22: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/screen_record.py:162: in start_recording_screen
    return self.execute(Command.START_RECORDING_SCREEN, {'options': options})['value']
qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute
    self.error_handler.check_response(response)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x1043f5940>
response = {'status': 500, 'value': '{"value":{"error":"unknown error","message":"An unknown server-side error occurred while pro...cordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)"}}'}

    def check_response(self, response: Dict[str, Any]) -> None:
        """
        https://www.w3.org/TR/webdriver/#errors
        """
        payload = response.get('value', '')
        if isinstance(payload, dict):
            payload_dict = payload
        else:
            try:
                payload_dict = json.loads(payload)
            except (json.JSONDecodeError, TypeError):
                return
            if not isinstance(payload_dict, dict):
                return
        value = payload_dict.get('value')
        if not isinstance(value, dict):
            return
        error = value.get('error')
        if not error:
            return
    
        message = value.get('message', error)
        stacktrace = value.get('stacktrace', '')
        # In theory, we should also be checking HTTP status codes.
        # Java client, for example, prints a warning if the actual `error`
        # value does not match to the response's HTTP status code.
        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(
            error, sel_exceptions.WebDriverException
        )
        if exception_class is sel_exceptions.WebDriverException and message:
            if message == 'No such context found.':
                exception_class = appium_exceptions.NoSuchContextException
            elif message == 'That command could not be executed in the current context.':
                exception_class = appium_exceptions.InvalidSwitchToTargetException
    
        if exception_class is sel_exceptions.UnexpectedAlertPresentException:
            raise sel_exceptions.UnexpectedAlertPresentException(
                msg=message,
                stacktrace=format_stacktrace(stacktrace),
                alert_text=value.get('data'),
            )
>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))
E       selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E       Stacktrace:
E       UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E           at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
E           at processTicksAndRejections (node:internal/process/task_queues:95:5)
E           at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
E           at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: WebDriverException</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='et12.1' class='hiddenRow'>
    <td class='errorCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0148 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_et12.1')">error</a>
        <div id='div_et12.1' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">SetUp</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_et12.1')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
    at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
    at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

error_message:self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1043f7320>
request = <SubRequest 'setup' for <Function test_004_publish_video_entry[setup0]>>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
>       get_driver.start_recording_screen()

src/EC/tests/product/test_112812_pdp_video_features.py:22: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/screen_record.py:162: in start_recording_screen
    return self.execute(Command.START_RECORDING_SCREEN, {'options': options})['value']
qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute
    self.error_handler.check_response(response)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x1043f5940>
response = {'status': 500, 'value': '{"value":{"error":"unknown error","message":"An unknown server-side error occurred while pro...cordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)"}}'}

    def check_response(self, response: Dict[str, Any]) -> None:
        """
        https://www.w3.org/TR/webdriver/#errors
        """
        payload = response.get('value', '')
        if isinstance(payload, dict):
            payload_dict = payload
        else:
            try:
                payload_dict = json.loads(payload)
            except (json.JSONDecodeError, TypeError):
                return
            if not isinstance(payload_dict, dict):
                return
        value = payload_dict.get('value')
        if not isinstance(value, dict):
            return
        error = value.get('error')
        if not error:
            return
    
        message = value.get('message', error)
        stacktrace = value.get('stacktrace', '')
        # In theory, we should also be checking HTTP status codes.
        # Java client, for example, prints a warning if the actual `error`
        # value does not match to the response's HTTP status code.
        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(
            error, sel_exceptions.WebDriverException
        )
        if exception_class is sel_exceptions.WebDriverException and message:
            if message == 'No such context found.':
                exception_class = appium_exceptions.NoSuchContextException
            elif message == 'That command could not be executed in the current context.':
                exception_class = appium_exceptions.InvalidSwitchToTargetException
    
        if exception_class is sel_exceptions.UnexpectedAlertPresentException:
            raise sel_exceptions.UnexpectedAlertPresentException(
                msg=message,
                stacktrace=format_stacktrace(stacktrace),
                alert_text=value.get('data'),
            )
>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))
E       selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E       Stacktrace:
E       UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E           at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
E           at processTicksAndRejections (node:internal/process/task_queues:95:5)
E           at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
E           at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: WebDriverException</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='pt12.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.001 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt12.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.001 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='errorClass'>
        <td>
        测试步骤：
        1. 进入搜索页面
        2. 搜索指定用户名
        3. 验证默认在Posts栏
        4. 切换到Accounts栏
        5. 验证出现对应的用户
        </td>
        <td>src/EC/tests/social/test_112741_social_search_accounts_verify.py::TestSocialSearchAccountsVerify::test_112741_search_user_and_switch_accounts[setup0]</td>
        <td>scene</td>
         <td>0.0038  s</td>
        <td>error</td>
        <td><a href="javascript:showClassDetail('c.13',2)">detail</a></td>
    </tr>
    
<tr id='et13.1' class='hiddenRow'>
    <td class='errorCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0038 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_et13.1')">error</a>
        <div id='div_et13.1' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">SetUp</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_et13.1')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
    at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
    at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

error_message:self = <test_112741_social_search_accounts_verify.TestSocialSearchAccountsVerify object at 0x1043f79e0>
request = <SubRequest 'setup' for <Function test_112741_search_user_and_switch_accounts[setup0]>>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
>       get_driver.start_recording_screen()

src/EC/tests/social/test_112741_social_search_accounts_verify.py:22: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/screen_record.py:162: in start_recording_screen
    return self.execute(Command.START_RECORDING_SCREEN, {'options': options})['value']
qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute
    self.error_handler.check_response(response)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x1043f5940>
response = {'status': 500, 'value': '{"value":{"error":"unknown error","message":"An unknown server-side error occurred while pro...cordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)"}}'}

    def check_response(self, response: Dict[str, Any]) -> None:
        """
        https://www.w3.org/TR/webdriver/#errors
        """
        payload = response.get('value', '')
        if isinstance(payload, dict):
            payload_dict = payload
        else:
            try:
                payload_dict = json.loads(payload)
            except (json.JSONDecodeError, TypeError):
                return
            if not isinstance(payload_dict, dict):
                return
        value = payload_dict.get('value')
        if not isinstance(value, dict):
            return
        error = value.get('error')
        if not error:
            return
    
        message = value.get('message', error)
        stacktrace = value.get('stacktrace', '')
        # In theory, we should also be checking HTTP status codes.
        # Java client, for example, prints a warning if the actual `error`
        # value does not match to the response's HTTP status code.
        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(
            error, sel_exceptions.WebDriverException
        )
        if exception_class is sel_exceptions.WebDriverException and message:
            if message == 'No such context found.':
                exception_class = appium_exceptions.NoSuchContextException
            elif message == 'That command could not be executed in the current context.':
                exception_class = appium_exceptions.InvalidSwitchToTargetException
    
        if exception_class is sel_exceptions.UnexpectedAlertPresentException:
            raise sel_exceptions.UnexpectedAlertPresentException(
                msg=message,
                stacktrace=format_stacktrace(stacktrace),
                alert_text=value.get('data'),
            )
>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))
E       selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E       Stacktrace:
E       UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E           at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
E           at processTicksAndRejections (node:internal/process/task_queues:95:5)
E           at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
E           at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: WebDriverException</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='et13.1' class='hiddenRow'>
    <td class='errorCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0038 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_et13.1')">error</a>
        <div id='div_et13.1' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">SetUp</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_et13.1')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
Stacktrace:
UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
    at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
    at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

error_message:self = <test_112741_social_search_accounts_verify.TestSocialSearchAccountsVerify object at 0x1043f79e0>
request = <SubRequest 'setup' for <Function test_112741_search_user_and_switch_accounts[setup0]>>
get_driver = <appium.webdriver.webdriver.WebDriver (session="7de710ec-33b6-4172-8c3d-5d36da13a5b7")>

    @pytest.fixture(scope='function')
    def setup(self, request, get_driver):
>       get_driver.start_recording_screen()

src/EC/tests/social/test_112741_social_search_accounts_verify.py:22: 
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 
qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/extensions/screen_record.py:162: in start_recording_screen
    return self.execute(Command.START_RECORDING_SCREEN, {'options': options})['value']
qa-ui-android1/lib/python3.12/site-packages/selenium/webdriver/remote/webdriver.py:348: in execute
    self.error_handler.check_response(response)
_ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ _ 

self = <appium.webdriver.errorhandler.MobileErrorHandler object at 0x1043f5940>
response = {'status': 500, 'value': '{"value":{"error":"unknown error","message":"An unknown server-side error occurred while pro...cordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)"}}'}

    def check_response(self, response: Dict[str, Any]) -> None:
        """
        https://www.w3.org/TR/webdriver/#errors
        """
        payload = response.get('value', '')
        if isinstance(payload, dict):
            payload_dict = payload
        else:
            try:
                payload_dict = json.loads(payload)
            except (json.JSONDecodeError, TypeError):
                return
            if not isinstance(payload_dict, dict):
                return
        value = payload_dict.get('value')
        if not isinstance(value, dict):
            return
        error = value.get('error')
        if not error:
            return
    
        message = value.get('message', error)
        stacktrace = value.get('stacktrace', '')
        # In theory, we should also be checking HTTP status codes.
        # Java client, for example, prints a warning if the actual `error`
        # value does not match to the response's HTTP status code.
        exception_class: Type[sel_exceptions.WebDriverException] = ERROR_TO_EXC_MAPPING.get(
            error, sel_exceptions.WebDriverException
        )
        if exception_class is sel_exceptions.WebDriverException and message:
            if message == 'No such context found.':
                exception_class = appium_exceptions.NoSuchContextException
            elif message == 'That command could not be executed in the current context.':
                exception_class = appium_exceptions.InvalidSwitchToTargetException
    
        if exception_class is sel_exceptions.UnexpectedAlertPresentException:
            raise sel_exceptions.UnexpectedAlertPresentException(
                msg=message,
                stacktrace=format_stacktrace(stacktrace),
                alert_text=value.get('data'),
            )
>       raise exception_class(msg=message, stacktrace=format_stacktrace(stacktrace))
E       selenium.common.exceptions.WebDriverException: Message: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E       Stacktrace:
E       UnknownError: An unknown server-side error occurred while processing the command. Original error: Could not proxy command to the remote server. Original error: connect ECONNREFUSED 127.0.0.1:8100
E           at JWProxy.command (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/node_modules/@appium/base-driver/lib/jsonwp-proxy/proxy.js:355:13)
E           at processTicksAndRejections (node:internal/process/task_queues:95:5)
E           at XCUITestDriver.proxyCommand (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/proxy-helper.js:109:35)
E           at XCUITestDriver.startRecordingScreen (/Users/<USER>/.appium/node_modules/appium-xcuitest-driver/lib/commands/recordscreen.js:257:9)

qa-ui-android1/lib/python3.12/site-packages/appium/webdriver/errorhandler.py:125: WebDriverException</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='pt13.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0023 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt13.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.0023 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

            </tbody>
        </table>
    </div>
    <div class="card-footer border-0 py-5">
        <span class="text-muted text-sm">Cases Total:
            <button type="button"
                    class="btn btn-sm bg-dark bg-opacity-20 bg-opacity-100-hover text-dark text-white-hover">13</button> =
            <button type="button"
                    class="btn btn-sm bg-success bg-opacity-20 bg-opacity-100-hover text-success text-white-hover">3</button> +
            <button type="button"
                    class="btn btn-sm bg-warning bg-opacity-20 bg-opacity-100-hover text-warning text-white-hover">4</button> +
            <button type="button"
                    class="btn btn-sm bg-danger bg-opacity-20 bg-opacity-100-hover text-danger text-white-hover">6</button> +
            <button type="button"
                    class="btn btn-sm bg-secondary bg-opacity-20 bg-opacity-100-hover text-secondary text-white-hover">0</button>
        </span>
    </div>
</div>
<div style="height:120px"></div>



<footer class="footer" style="height: 50px; position: fixed; width: 100%">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-6">
                weeeTest 0.2.10.4c; 2023 © Weee! 版权所有
            </div>
        </div>
    </div>
</footer>

</body>

</html>