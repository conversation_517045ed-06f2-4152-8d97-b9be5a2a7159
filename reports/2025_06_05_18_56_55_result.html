<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
    <title> ios ui automation </title>
    <meta name="generator" content="weeeTest 0.2.10.4c"/>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
    <!--    <script src="http://libs.baidu.com/jquery/2.0.0/jquery.min.js"></script>-->
    <link rel="stylesheet" href="http://img.itest.info/seldom-main.css">
    <link rel="stylesheet" href="http://img.itest.info/seldom-utilities.css">

    <style type="text/css" media="screen">
    body {
        font-family: verdana, arial, helvetica, sans-serif;
        font-size: 80%;
    }

    table {
        font-size: 100%;
    }

    .table td {
        white-space: inherit !important;
    }

    /* -- heading ---------------------------------------------------------------------- */
    h1 {
        font-size: 16pt;
        color: gray;
    }

    pre {
        background-color: #eef2f7;
        padding-top: 10px;
        text-align: left;
        max-height: 600px;
        overflow: auto;
    }

    ::-webkit-scrollbar {
        width: 6px;
        height: 6px;
        background-color: #F5F5F5;
    }

    ::-webkit-scrollbar-track {
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
        border-radius: 10px;
        background-color: rgba(114, 124, 245, .25);
    }

    ::-webkit-scrollbar-thumb {
        border-radius: 10px;
        -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, .3);
        background-color: #6c757d;
    }

    .heading {
        margin-top: 20px;
        margin-bottom: 1ex;
        margin-left: 10px;
        margin-right: 10px;
        width: 23%;
        float: left;
        padding-top: 10px;
        padding-left: 10px;
        padding-bottom: 10px;
        padding-right: 10px;
        box-shadow: 0px 0px 5px #000;
    }

    .heading .attribute {
        margin-top: 1ex;
        margin-bottom: 0;
    }

    .heading .description {
        margin-top: 4ex;
        margin-bottom: 6ex;
    }

    /* -- css div popup ------------------------------------------------------------------------ */

    a.popup_link:hover {
        color: red;
    }

    .log_window {
        max-width: 70%;
        max-height: 70%;
    }

    /* -- results ------------------------------------------------------------------------ */
    .show_detail_line {
        margin-left: 10px;
        margin-top: 30px;
        margin-bottom: 20px;
    }

    .show_detail_button {
        margin-top: 3ex;
        margin-bottom: 1ex;
        margin-left: 10px;
        text-align: right;
        margin-right: 15px;
    }

    .header_row {
        font-weight: bold;
        color: #606060;
        border-top-width: 10px;
        border-color: #d6e9c6;
        font-size: 15px;
    }

    .total_row {
        font-weight: bold;
        background-color: #dee2e6;
    }

    .passClass {
        background-color: #ccf5e7;
    }

    .skipClass {
        background-color: #cfd6df;
    }

    .failClass {
        background-color: #ffe8cc;
    }

    .errorClass {
        background-color: #ffd6e0;
    }

    .passCase {
        color: #00CC88;
        font-weight: bold;
    }

    .failCase {
        color: #FF8C00;
        font-weight: bold;
    }

    .errorCase {
        color: #FF3366;
        font-weight: bold;
    }

    .hiddenRow {
        display: none;
    }

    .caseStatistics {
        width: 46%
    }

    .none {
        color: #009900
    }

    .testcase {
        margin-left: 2em;
    }

    /* -- chars ---------------------------------------------------------------------- */
    .testChars {
        width: 900px;
        margin-left: 0px;
    }

    .error-color {
        color: #fff;
        background-color: #f44455;
        border-color: #f44455;
    }

    .pass-color {
        color: #fff;
        background-color: #5fc27e;
        border-color: #5fc27e;
    }

    .fail-color {
        color: #fff;
        background-color: #fcc100;
        border-color: #fcc100;
    }

    .skip-color {
        color: #fff;
        background-color: #6c757d;
        border-color: #6c757d;
    }

    /* -- screenshots ---------------------------------------------------------------------- */
    .img {
        border-collapse: collapse;
        max-width: 100%;
        max-height: 100%;
        object-fit: contain;
        position: absolute;
        top: 0;
        left: 0;
        bottom: 0;
        right: 0;
        margin: auto;
    }

    .img-card {
        height: 600px;
        width: 800px;
        background-color: #e7eaf0;
    }

    .img-circle {
        height: 20px;
        border-radius: 12px;
        background-color: red;
        padding-left: 13px;
        margin: 0 auto;
        position: relative;
        top: -40px;
        background-color: rgba(1, 150, 0, 0.3);
    }

    .img-circle font {
        border: 1px solid white;
        width: 11px;
        height: 11px;
        border-radius: 50%;
        margin-right: 9px;
        margin-top: 4px;
        display: block;
        float: left;
        background-color: white;
    }

    .close-shots {
        position: absolute;
        top: 5px;
        right: 5px;
        z-index: 99;
    }

    .footer {
        height: 50px;
        width: 100%;
        position: fixed;
        bottom: 0;
        background-color: white;
        padding-bottom: 0.75rem !important;
        padding-top: 0.75rem !important;
        border: 0 solid #e7eaf0;
    }

    #headContainer {
        margin-top: 20px;
        margin-bottom: 20px;
        padding-left: 30px;
        padding-right: 30px;
    }

    .head-list {
        padding-top: 5px;
        padding-bottom: 5px;
    }

    #resultContainer {
        margin-left: 30px;
        margin-right: 30px;
    }


</style>
</head>

<body style="background-color: #f5f9fc;">
<script language="javascript" type="text/javascript">
    function showLog(id) {
        document.body.style.overflow = 'hidden'
        document.body.style.height = '100%'
        document.getElementById(id).style.display = 'block'
    }

    function hideLog(id) {
        document.body.style.overflow = 'auto';
        document.getElementById(id).style.display = 'none'
    }

    function showImg(obj) {
        document.body.style.overflow = 'hidden'
        document.body.style.height = '100%'
        var nextObj = obj.nextElementSibling
        nextObj.style.display = 'block'
        var index = 0;   //每张图片的下标，
        var len = nextObj.getElementsByTagName('img').length;
        var imgCircle = nextObj.getElementsByClassName('img-circle')[0]
        nextObj.onmouseover = function () {  //当鼠标光标停在图片上，则停止轮播
            clearInterval(start);
        }
        nextObj.onmouseout = function () {  //当鼠标光标停在图片上，则开始轮播
            start = setInterval(autoPlay, 1000);
        }
        for (var i = 0; i < len; i++) {
            var fontTag = document.createElement('font')
            imgCircle.appendChild(fontTag)
        }
        var fontTagList = nextObj.getElementsByTagName('font');  //得到所有圆圈
        changeImg(0)
        var funny = function (i) {
            fontTagList[i].onmouseover = function () {
                index = i
                changeImg(i)
            }
        }
        for (var i = 0; i < fontTagList.length; i++) {
            funny(i);
        }

        function autoPlay() {
            if (index > len - 1) {
                index = 0;
                clearInterval(start); //运行一轮后停止
            }
            changeImg(index++);
        }

        imgCircle.style.width = 30 * len + "px";

        // 对应圆圈和图片同步
        function changeImg(index) {
            var imgTags = nextObj.getElementsByTagName('img');
            var fontTags = nextObj.getElementsByTagName('font');
            for (i = 0; i < fontTags.length; i++) {
                imgTags[i].style.display = 'none';
                fontTags[i].style.backgroundColor = 'white';
            }
            imgTags[index].style.display = 'block';
            if (fontTags.length > 0) {
                fontTags[index].style.backgroundColor = 'red';
            }
        }
    }

    function hideImg(obj) {
        document.body.style.overflow = 'auto';
        obj.parentElement.parentElement.parentElement.parentElement.parentElement.style.display = "none";
        obj.parentElement.parentElement.parentElement.getElementsByClassName('img-circle')[0].innerHTML = "";
    }

    output_list = Array();
    /* level
    - 0:Summary
    - 1:Failed
    - 2:Skip
    - 3:All
    */
    // function showCase(level, channel) {
    //     trs = document.getElementsByTagName("tr");
    //     for (var i=0; i < trs.length; i++) {
    //         tr = trs[i];
    //         id = tr.id;
    //         if (["ft", "pt", "et", "st"].indexOf(id.substr(0, 2)) != -1) {
    //             if (level == 0 && id.substr(2, 1) == channel) {
    //                 tr.className = 'hiddenRow';
    //             }
    //         }
    //         if (id.substr(0, 3) == 'pt' + channel) {
    //             if (level == 1) {
    //                 tr.className = '';
    //             }
    //             else if (level > 4 && id.substr(2, 1) == channel) {
    //                 tr.className = '';
    //             }
    //             else {
    //                 tr.className = 'hiddenRow';
    //             }
    //         }
    //         if (id.substr(0, 3) == 'ft' + channel) {
    //             if (level == 2) {
    //                 tr.className = '';
    //             }
    //             else if (level > 4 && id.substr(2, 1) == channel) {
    //                 tr.className = '';
    //             }
    //             else {
    //                 tr.className = 'hiddenRow';
    //             }
    //         }
    //         if (id.substr(0, 3) == 'et' + channel) {
    //             if (level == 3) {
    //                 tr.className = '';
    //             }
    //             else if (level > 4 && id.substr(2, 1) == channel) {
    //                 tr.className = '';
    //             }
    //             else {
    //                 tr.className = 'hiddenRow';
    //             }
    //         }
    //         if (id.substr(0, 3) == 'st' + channel) {
    //             if (level == 4) {
    //                 tr.className = '';
    //             }
    //             else if (level > 4 && id.substr(2, 1) == channel) {
    //                 tr.className = '';
    //             }
    //             else {
    //                 tr.className = 'hiddenRow';
    //             }
    //         }
    //     }
    // }
    // function showClassDetail(cid, count) {
    //     var id_list = Array(count);
    //     var toHide = 1;
    //     for (var i=0; i < count; i++) {
    //         tid0 = 't' + cid.substr(2) + '.' + (i + 1);
    //         tid = 'f' + tid0;
    //         tr = document.getElementById(tid);
    //         if (!tr) {
    //             tid = 'p' + tid0;
    //             tr = document.getElementById(tid);
    //         }
    //         if (!tr) {
    //             tid = 'e' + tid0;
    //             tr = document.getElementById(tid);
    //         }
    //         if (!tr) {
    //             tid = 's' + tid0;
    //             tr = document.getElementById(tid);
    //         }
    //         id_list[i] = tid;
    //         if (tr.className) {
    //             toHide = 0;
    //         }
    //     }
    //     for (var i = 0; i < count; i++) {
    //         tid = id_list[i];
    //         if (toHide) {
    //             document.getElementById(tid).className = 'hiddenRow';
    //         }
    //         else {
    //             document.getElementById(tid).className = '';
    //         }
    //     }
    // }

    function showStep(level, channel) {
        trs = document.getElementsByTagName("tr");
        for (var i = 0; i < trs.length; i++) {
            tr = trs[i];
            id = tr.id;
            // debugger
            console.log("id::", id);
            console.log("id截取::", id.substr(0, 3));
            console.log("idAther::", id.substr(2, 1));
            console.log("channel::", channel)
            if (["ft", "pt", "et", "st"].indexOf(id.substr(0, 2)) != -1) {
                if (level == 0 && id.substr(2, 1) == channel) {
                    tr.className = 'hiddenRow';
                }
            }
            if (id.substr(0, 2) == 'pt') {
                if (level == 1) {
                    tr.className = '';
                } else if (level > 4) {
                    tr.className = '';
                } else {
                    tr.className = 'hiddenRow';
                }
            }
            if (id.substr(0, 2) == 'ft') {
                if (level == 2) {
                    tr.className = '';
                } else if (level > 4) {
                    tr.className = '';
                } else {
                    tr.className = 'hiddenRow';
                }
            }
            if (id.substr(0, 2) == 'et') {

                if (level == 3) {
                    tr.className = '';
                } else if (level > 4) {
                    tr.className = '';
                } else {
                    tr.className = 'hiddenRow';
                }
            }
            if (id.substr(0, 2) == 'st') {
                if (level == 4) {
                    tr.className = '';
                } else if (level > 4) {
                    tr.className = '';
                } else {
                    tr.className = 'hiddenRow';
                }
            }
        }
    }

    function showClassDetail(cid, count) {
        var id_list = Array(count);
        var toHide = 1;
        for (var i = 0; i < count; i++) {
            tid0 = 't' + cid.substr(2) + '.' + (i + 1);
            tid = 'f' + tid0;
            tr = document.getElementById(tid);
            if (!tr) {
                tid = 'p' + tid0;
                tr = document.getElementById(tid);
            }
            if (!tr) {
                tid = 'e' + tid0;
                tr = document.getElementById(tid);
            }
            if (!tr) {
                tid = 's' + tid0;
                tr = document.getElementById(tid);
            }
            id_list[i] = tid;
            if (tr.className) {
                toHide = 0;
            }
        }
        for (var i = 0; i < count; i++) {
            tid = id_list[i];
            if (toHide) {
                document.getElementById(tid).className = 'hiddenRow';
            } else {
                document.getElementById(tid).className = '';
            }
        }
    }

    function showTestDetail(div_id) {
        var detailsDiv = document.getElementById(div_id)
        var displayState = detailsDiv.style.display

        if (displayState != 'block') {
            displayState = 'block'
            detailsDiv.style.display = 'block'
        } else {
            detailsDiv.style.display = 'none'
        }
    }

    function html_escape(s) {
        s = s.replace(/&/g, '&amp;');
        s = s.replace(/</g, '&lt;');
        s = s.replace(/>/g, '&gt;');
        return s;
    }
</script>

<nav class="navbar navbar-light position-lg-sticky top-lg-0 d-none d-lg-block overlap-10 flex-none bg-white border-bottom px-0 py-3" id="topbar">
    <div class="container-fluid">
        <div class="hstack gap-2">
            <a href="">
                <img src="https://img01.weeecdn.net/static/www/_next/static/media/logo.c5d10ee5.svg" style="height: 2.25rem;">
            </a>
        </div>
        <div class="navbar-user d-none d-sm-block">
            <div class="hstack gap-3 ms-4">
                <h3 style="float: right;"> ios ui automation </h3>
            </div>
        </div>
    </div>
</nav>

<div id="headContainer" class="container-fluid mm-active">
    <div class="row">
        <div class="col-12 col-lg-5 col-xl-4 d-flex" style="float:left">
            <div class='card flex-fill'>
                <div class="card-header">
                    <div class="d-flex align-items-center">
                        <h5 class="mb-0">Overview</h5>
                    </div>
                </div>
                <div class="card-body py-0 position-relative scrollable-y" style="max-height:300px">
                    <div class="list-group list-group-flush">
                        <div class="list-group-item px-0 position-relative hstack flex-wrap head-list">
                            <div class="flex-1">
                                <div class="d-flex align-items-center mb-1">Tester</div>
                                <div class="d-flex align-items-center">
                                    <div class="text-sm text-muted line-clamp-1 me-auto">weeeTest</div>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item px-0 position-relative hstack flex-wrap head-list">
                            <div class="flex-1">
                                <div class="d-flex align-items-center mb-1">Start time - End time</div>
                                <div class="d-flex align-items-center">
                                    <div class="text-sm text-muted line-clamp-1 me-auto">2025-06-05 18:56:57 - 2025-06-05 19:09:47</div>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item px-0 position-relative hstack flex-wrap head-list">
                            <div class="flex-1">
                                <div class="d-flex align-items-center mb-1">Duration</div>
                                <div class="d-flex align-items-center">
                                    <div class="text-sm text-muted line-clamp-1 me-auto">00:12:52</div>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item px-0 position-relative hstack flex-wrap head-list">
                            <div class="flex-1">
                                <div class="d-flex align-items-center mb-1">Status</div>
                                <div class="d-flex align-items-center">
                                    <div class="text-sm text-muted line-clamp-1 me-auto">
                                        <span class="badge badge-pill bg-soft-success text-success me-2">Passed:5</span>
                                        <span class="badge badge-pill bg-soft-warning text-warning me-2">Failed:5</span>
                                        <span class="badge badge-pill bg-soft-danger text-danger me-2">Errors:0</span>
                                        <span class="badge badge-pill bg-soft-secondary text-secondary me-2">Skipped:0</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="list-group-item px-0 position-relative hstack flex-wrap" style="padding-top: 5px;">
                            <div class="flex-1">
                                <div class="d-flex align-items-center mb-1">Description</div>
                                <div class="d-flex align-items-center">
                                    <div class="text-sm text-muted me-auto">weeeTest Test Report</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div style="width: 20%">
            <div class="card" style="height: 45%;">
                <div class="card-body">
                    <div class="row">
                        <div class="col"><span class="h6 font-semibold text-muted text-sm d-block mb-2">Passed</span>
                            <span class="h3 font-bold mb-0">5</span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-success text-white text-lg rounded-circle">P</div>
                        </div>
                    </div>
                    <div class="mt-2 mb-0 text-sm">
                        <span class="badge badge-pill bg-soft-success text-success me-2">50.0%</span>
                        <span class="text-nowrap text-xs text-muted">Pass rate</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="progress" style="width:70%; margin-top: 5px;">
                            <div class="progress-bar bg-success" role="progressbar" aria-valuenow="83" aria-valuemin="0"
                                aria-valuemax="100" style="width:50.0%"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card" style="height: 45%; top: 10%;">
                <div class="card-body">
                    <div class="row">
                        <div class="col"><span class="h6 font-semibold text-muted text-sm d-block mb-2">Failed</span>
                            <span class="h3 font-bold mb-0">5</span></div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-warning text-white text-lg rounded-circle">F</div>
                        </div>
                    </div>
                    <div class="mt-2 mb-0 text-sm">
                        <span class="badge badge-pill bg-soft-warning text-warning me-2">50.0%</span>
                        <span class="text-nowrap text-xs text-muted">Failure rate</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="progress" style="width:70%; margin-top: 5px;">
                            <div class="progress-bar bg-warning" role="progressbar" aria-valuenow="83" aria-valuemin="0"
                                aria-valuemax="100" style="width:50.0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="width: 20%">
            <div class="card" style="height: 45%;">
                <div class="card-body">
                    <div class="row">
                        <div class="col"><span class="h6 font-semibold text-muted text-sm d-block mb-2">Errors</span>
                            <span class="h3 font-bold mb-0">0</span></div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-danger text-white text-lg rounded-circle">E</div>
                        </div>
                    </div>
                    <div class="mt-2 mb-0 text-sm">
                        <span class="badge badge-pill bg-soft-danger text-danger me-2">0.0%</span>
                        <span class="text-nowrap text-xs text-muted">Error rate</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="progress" style="width:70%; margin-top: 5px;">
                            <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="83" aria-valuemin="0"
                                aria-valuemax="100" style="width:0.0%"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="card" style="height: 45%; top: 10%;">
                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <span class="h6 font-semibold text-muted text-sm d-block mb-2">Skipped</span>
                            <span class="h3 font-bold mb-0">0</span>
                        </div>
                        <div class="col-auto">
                            <div class="icon icon-shape bg-secondary text-white text-lg rounded-circle">S</div>
                        </div>
                    </div>
                    <div class="mt-2 mb-0 text-sm">
                        <span class="badge badge-pill bg-soft-secondary text-secondary me-2">0.0%</span>
                        <span class="text-nowrap text-xs text-muted">Skip rate</span>
                    </div>
                    <div class="d-flex align-items-center">
                        <div class="progress" style="width:70%; margin-top: 5px;">
                            <div class="progress-bar bg-secondary" role="progressbar" aria-valuenow="83" aria-valuemin="0"
                                aria-valuemax="100" style="width:0.0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div id="resultContainer" class="card">
    <div class="card-header border-bottom">
        <span style="float: left;">
            <h5 class="mb-0">Result</h5>
        </span>
        <span style="float: right;">
            <a href='javascript:showStep(0, 0)' class="btn btn-dark btn-sm">Summary</a>
            <a href='javascript:showStep(1, 0)' class="btn btn-success btn-sm">Pass</a>
            <a href='javascript:showStep(2, 0)' class="btn btn-warning btn-sm">Failed</a>
            <a href='javascript:showStep(3, 0)' class="btn btn-danger btn-sm">Error</a>
            <a href='javascript:showStep(4, 0)' class="btn btn-secondary btn-sm">Skip</a>
            <a href='javascript:showStep(5, 0)' class="btn btn-info btn-sm">All</a>
        </span>
    </div>
    <div class="table-responsive">
        <table class="table table-hover table-nowrap">
            <thead class="table-light">
            <tr>
                <th scope="col">Case Description</th>
                <th scope="col">Test Group/Test Case</th>
                <th scope="col">case_type</th>
                <th scope="col">Duration</th>
                <!--                    <th scope="col">Count(times)</th>-->
                <!--                    <th scope="col">Pass(times)</th>-->
                <!--                    <th scope="col">Fail(times)</th>-->
                <!--                    <th scope="col">Error(times)</th>-->
                <th scope="col">status</th>
                <th scope="col">View</th>
                <!--                <th scope="col">Screenshots</th>-->
            </tr>
            </thead>
            <tbody>
            
    <tr class='passClass'>
        <td>先登陆后加购</td>
        <td>src/EC/tests/onboarding/test_000_onboarding.py::TestOnboarding::test_000_onboarding_with_login[setup0]</td>
        <td>scene</td>
         <td>249.4398  s</td>
        <td>pass</td>
        <td><a href="javascript:showClassDetail('c.1',3)">detail</a></td>
    </tr>
    
<tr id='pt1.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>74.5622 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt1.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>74.5622 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt1.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>170.6566 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt1.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>170.6566 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt1.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>4.221 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt1.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>4.221 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='passClass'>
        <td>先加购，checkout时再登陆</td>
        <td>src/EC/tests/onboarding/test_000_onboarding.py::TestOnboarding::test_000_onboarding_without_login[setup0]</td>
        <td>scene</td>
         <td>170.2188  s</td>
        <td>pass</td>
        <td><a href="javascript:showClassDetail('c.2',3)">detail</a></td>
    </tr>
    
<tr id='pt2.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>52.4036 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt2.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>52.4036 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt2.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>114.8313 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt2.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>114.8313 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt2.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>2.9839 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt2.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>2.9839 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='passClass'>
        <td>
        测试步骤：
        1. 进入account页面
        2. 点击my orders,进入orders页面
        3. 依次点击"pending, unshipped, shipped, to review, cancelled"查看订单
        </td>
        <td>src/EC/tests/account/test_001_orders.py::TestOrders::test_001_check_order_tabs[setup0]</td>
        <td>scene</td>
         <td>36.5394  s</td>
        <td>pass</td>
        <td><a href="javascript:showClassDetail('c.3',3)">detail</a></td>
    </tr>
    
<tr id='pt3.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>8.0551 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt3.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>8.0551 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt3.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>28.0815 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt3.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>28.0815 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt3.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.4028 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt3.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.4028 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='passClass'>
        <td>
        测试步骤：
        1. 进入Gift card主页面，处理欢迎弹窗
        2. 填入收件人邮箱，验证输入成功
        3. 点击checkout，进入礼品卡checkout页面
        4. 点击支付方式下拉栏，选择PayPal
        5. 点击place order，跳转到PayPal第三方支付页面
        </td>
        <td>src/EC/tests/account/test_112237_gift_card_purchase_flow.py::TestGiftCardPurchaseFlow::test_112237_gift_card_purchase_flow_complete[setup0]</td>
        <td>scene</td>
         <td>51.3094  s</td>
        <td>pass</td>
        <td><a href="javascript:showClassDetail('c.4',3)">detail</a></td>
    </tr>
    
<tr id='pt4.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>4.2452 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt4.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>4.2452 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt4.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>46.2946 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt4.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>46.2946 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt4.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.7696 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt4.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.7696 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='failClass'>
        <td>
        测试步骤：
        1. 进入搜索页面
        2. 搜索指定用户名
        3. 验证默认在Posts栏
        4. 切换到Accounts栏
        5. 验证出现对应的用户
        </td>
        <td>src/EC/tests/social/test_112741_social_search_accounts_verify.py::TestSocialSearchAccountsVerify::test_112741_search_user_and_switch_accounts[setup0]</td>
        <td>scene</td>
         <td>15.3113  s</td>
        <td>fail</td>
        <td><a href="javascript:showClassDetail('c.5',3)">detail</a></td>
    </tr>
    
<tr id='pt5.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>5.0224 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt5.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>5.0224 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='ft5.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>10.2889 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft5.2')">fail</a>
        <div id='div_ft5.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft5.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Failed to find inspiration button!
assert None

error_message:self = <test_112741_social_search_accounts_verify.TestSocialSearchAccountsVerify object at 0x1085ca0c0>
get_platform = 'iOS'
get_driver = <appium.webdriver.webdriver.WebDriver (session="69f180a8-765a-4bf6-9510-6a7d0b7cf734")>
setup = None

    @allure.title("搜索对应用户名并切换到Accounts栏测试")
    @pytest.mark.parametrize('setup', [('test_search_user_and_switch_accounts',)], indirect=True)
    @pytest.mark.ios
    def test_112741_search_user_and_switch_accounts(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. 进入搜索页面
        2. 搜索指定用户名
        3. 验证默认在Posts栏
        4. 切换到Accounts栏
        5. 验证出现对应的用户
        """
        # 只能ios使用，安卓在输入用户名以后，发送回车键无效
        d = get_driver
        platform = get_platform
    
        # 初始化社交搜索页面对象
        search_page = SocialSearchPage(d, platform)
    
        # 导航到搜索页面，点击inspiration按钮
        inspiration = self.find_element(d, search_page.strategies.get(platform).get("inspiration"))
>       assert inspiration, "Failed to find inspiration button!"
E       AssertionError: Failed to find inspiration button!
E       assert None

src/EC/tests/social/test_112741_social_search_accounts_verify.py:52: AssertionError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='ft5.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>10.2889 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft5.2')">fail</a>
        <div id='div_ft5.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft5.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Failed to find inspiration button!
assert None

error_message:self = <test_112741_social_search_accounts_verify.TestSocialSearchAccountsVerify object at 0x1085ca0c0>
get_platform = 'iOS'
get_driver = <appium.webdriver.webdriver.WebDriver (session="69f180a8-765a-4bf6-9510-6a7d0b7cf734")>
setup = None

    @allure.title("搜索对应用户名并切换到Accounts栏测试")
    @pytest.mark.parametrize('setup', [('test_search_user_and_switch_accounts',)], indirect=True)
    @pytest.mark.ios
    def test_112741_search_user_and_switch_accounts(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. 进入搜索页面
        2. 搜索指定用户名
        3. 验证默认在Posts栏
        4. 切换到Accounts栏
        5. 验证出现对应的用户
        """
        # 只能ios使用，安卓在输入用户名以后，发送回车键无效
        d = get_driver
        platform = get_platform
    
        # 初始化社交搜索页面对象
        search_page = SocialSearchPage(d, platform)
    
        # 导航到搜索页面，点击inspiration按钮
        inspiration = self.find_element(d, search_page.strategies.get(platform).get("inspiration"))
>       assert inspiration, "Failed to find inspiration button!"
E       AssertionError: Failed to find inspiration button!
E       assert None

src/EC/tests/social/test_112741_social_search_accounts_verify.py:52: AssertionError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='pt5.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.1892 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt5.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.1892 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='passClass'>
        <td>
        测试步骤：
        1. 进入产品详情页
        2. 验证Reviews栏是否展示
        3. 验证总review数是否正确显示
        4. 验证Customers say栏是否展示
        5. 验证AI-generated文本样式
        </td>
        <td>src/EC/tests/product/test_112777_pdp_reviews_selling_points.py::TestPDPReviewsSellingPoints::test_112777_reviews_section_display[setup0]</td>
        <td>scene</td>
         <td>59.7555  s</td>
        <td>pass</td>
        <td><a href="javascript:showClassDetail('c.6',3)">detail</a></td>
    </tr>
    
<tr id='pt6.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>5.0143 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt6.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>5.0143 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt6.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>53.5585 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt6.2' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>53.5585 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt6.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>1.1827 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt6.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>1.1827 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='failClass'>
        <td>
        测试步骤：
        1. 进入产品详情页
        2. 点击See All
        3. 验证是否跳转到Explore页面并展示全部视频
        </td>
        <td>src/EC/tests/product/test_112812_pdp_video_features.py::TestPDPVideoFeatures::test_003_see_all_navigation[setup0]</td>
        <td>scene</td>
         <td>59.2337  s</td>
        <td>fail</td>
        <td><a href="javascript:showClassDetail('c.7',3)">detail</a></td>
    </tr>
    
<tr id='pt7.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>3.9424 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt7.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>3.9424 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='ft7.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>55.2913 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft7.2')">fail</a>
        <div id='div_ft7.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft7.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Failed to navigate to Explore page after clicking See All
assert None is not None

error_message:self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1085c91c0>
get_driver = <appium.webdriver.webdriver.WebDriver (session="69f180a8-765a-4bf6-9510-6a7d0b7cf734")>
get_platform = 'iOS', setup = None

    @allure.title("点击See All跳转测试")
    @pytest.mark.parametrize('setup', [('test_003_see_all_navigation', )], indirect=True)
    def test_003_see_all_navigation(self, get_driver, get_platform, setup):
        """
        测试步骤：
        1. 进入产品详情页
        2. 点击See All
        3. 验证是否跳转到Explore页面并展示全部视频
        """
        d = get_driver
        platform = get_platform
    
        # 准备测试环境
    
        # 导航到产品详情页
        navigation_success = self.navigate_to_product_detail(d, platform)
        assert navigation_success, "Failed to navigate to product detail page"
    
        # 初始化产品详情页对象
        pdp_page = ProductDetailPage(d, platform)
    
        # 验证视频栏是否存在
        assert pdp_page.check_videos_section_exists(), "Videos section is not displayed on PDP"
    
        # 点击See All按钮
        assert pdp_page.click_see_all_videos(), "Failed to click See All button"
    
        # 验证是否跳转到Explore页面
        # 这里需要根据实际情况调整验证逻辑
        if platform == 'Android':
            explore_title = self.find_element(_driver=d, ele=(AppiumBy.XPATH, '//android.widget.TextView[@text="Explore"]'))
        else:  # iOS
            explore_title = self.find_element(_driver=d, ele=(AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeNavigationBar[`name == "Explore"`]'))
    
>       assert explore_title is not None, "Failed to navigate to Explore page after clicking See All"
E       AssertionError: Failed to navigate to Explore page after clicking See All
E       assert None is not None

src/EC/tests/product/test_112812_pdp_video_features.py:154: AssertionError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='ft7.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>55.2913 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft7.2')">fail</a>
        <div id='div_ft7.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft7.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Failed to navigate to Explore page after clicking See All
assert None is not None

error_message:self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1085c91c0>
get_driver = <appium.webdriver.webdriver.WebDriver (session="69f180a8-765a-4bf6-9510-6a7d0b7cf734")>
get_platform = 'iOS', setup = None

    @allure.title("点击See All跳转测试")
    @pytest.mark.parametrize('setup', [('test_003_see_all_navigation', )], indirect=True)
    def test_003_see_all_navigation(self, get_driver, get_platform, setup):
        """
        测试步骤：
        1. 进入产品详情页
        2. 点击See All
        3. 验证是否跳转到Explore页面并展示全部视频
        """
        d = get_driver
        platform = get_platform
    
        # 准备测试环境
    
        # 导航到产品详情页
        navigation_success = self.navigate_to_product_detail(d, platform)
        assert navigation_success, "Failed to navigate to product detail page"
    
        # 初始化产品详情页对象
        pdp_page = ProductDetailPage(d, platform)
    
        # 验证视频栏是否存在
        assert pdp_page.check_videos_section_exists(), "Videos section is not displayed on PDP"
    
        # 点击See All按钮
        assert pdp_page.click_see_all_videos(), "Failed to click See All button"
    
        # 验证是否跳转到Explore页面
        # 这里需要根据实际情况调整验证逻辑
        if platform == 'Android':
            explore_title = self.find_element(_driver=d, ele=(AppiumBy.XPATH, '//android.widget.TextView[@text="Explore"]'))
        else:  # iOS
            explore_title = self.find_element(_driver=d, ele=(AppiumBy.IOS_CLASS_CHAIN, '**/XCUIElementTypeNavigationBar[`name == "Explore"`]'))
    
>       assert explore_title is not None, "Failed to navigate to Explore page after clicking See All"
E       AssertionError: Failed to navigate to Explore page after clicking See All
E       assert None is not None

src/EC/tests/product/test_112812_pdp_video_features.py:154: AssertionError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='pt7.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.8833 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt7.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.8833 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='failClass'>
        <td>
        测试步骤：
        1. 进入产品详情页
        2. 验证PDP下方是否展示发布视频tip词
        3. 验证发布视频按钮是否可点击
        </td>
        <td>src/EC/tests/product/test_112812_pdp_video_features.py::TestPDPVideoFeatures::test_004_publish_video_entry[setup0]</td>
        <td>scene</td>
         <td>42.6109  s</td>
        <td>fail</td>
        <td><a href="javascript:showClassDetail('c.8',3)">detail</a></td>
    </tr>
    
<tr id='pt8.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>4.2455 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt8.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>4.2455 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='ft8.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>38.3654 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft8.2')">fail</a>
        <div id='div_ft8.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft8.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Failed to navigate to product detail page
assert False

error_message:self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1085c94f0>
get_driver = <appium.webdriver.webdriver.WebDriver (session="69f180a8-765a-4bf6-9510-6a7d0b7cf734")>
get_platform = 'iOS', setup = None

    @allure.title("PDP页面发布视频入口测试")
    @pytest.mark.parametrize('setup', [('test_004_publish_video_entry', )], indirect=True)
    def test_004_publish_video_entry(self, get_driver, get_platform, setup):
        """
        测试步骤：
        1. 进入产品详情页
        2. 验证PDP下方是否展示发布视频tip词
        3. 验证发布视频按钮是否可点击
        """
        d = get_driver
        platform = get_platform
    
        # 准备测试环境
    
        # 导航到产品详情页
        navigation_success = self.navigate_to_product_detail(d, platform)
>       assert navigation_success, "Failed to navigate to product detail page"
E       AssertionError: Failed to navigate to product detail page
E       assert False

src/EC/tests/product/test_112812_pdp_video_features.py:175: AssertionError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='ft8.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>38.3654 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft8.2')">fail</a>
        <div id='div_ft8.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft8.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Failed to navigate to product detail page
assert False

error_message:self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1085c94f0>
get_driver = <appium.webdriver.webdriver.WebDriver (session="69f180a8-765a-4bf6-9510-6a7d0b7cf734")>
get_platform = 'iOS', setup = None

    @allure.title("PDP页面发布视频入口测试")
    @pytest.mark.parametrize('setup', [('test_004_publish_video_entry', )], indirect=True)
    def test_004_publish_video_entry(self, get_driver, get_platform, setup):
        """
        测试步骤：
        1. 进入产品详情页
        2. 验证PDP下方是否展示发布视频tip词
        3. 验证发布视频按钮是否可点击
        """
        d = get_driver
        platform = get_platform
    
        # 准备测试环境
    
        # 导航到产品详情页
        navigation_success = self.navigate_to_product_detail(d, platform)
>       assert navigation_success, "Failed to navigate to product detail page"
E       AssertionError: Failed to navigate to product detail page
E       assert False

src/EC/tests/product/test_112812_pdp_video_features.py:175: AssertionError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='pt8.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.671 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt8.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.671 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='failClass'>
        <td>
        测试步骤：
        1. 进入产品详情页
        2. 验证视频栏展示及视频个数显示
        </td>
        <td>src/EC/tests/product/test_112812_pdp_video_features.py::TestPDPVideoFeatures::test_112812_pdp_video_ui_display[setup0]</td>
        <td>scene</td>
         <td>41.3019  s</td>
        <td>fail</td>
        <td><a href="javascript:showClassDetail('c.9',3)">detail</a></td>
    </tr>
    
<tr id='pt9.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>3.9257 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt9.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>3.9257 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='ft9.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>37.3762 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft9.2')">fail</a>
        <div id='div_ft9.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft9.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Failed to navigate to product detail page
assert False

error_message:self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1085c90a0>
get_platform = 'iOS'
get_driver = <appium.webdriver.webdriver.WebDriver (session="69f180a8-765a-4bf6-9510-6a7d0b7cf734")>
setup = None

    @allure.title("iOS端PDP视频UI展示测试")
    @pytest.mark.parametrize('setup', [('test_112812_pdp_video_ui_display', )], indirect=True)
    def test_112812_pdp_video_ui_display(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. 进入产品详情页
        2. 验证视频栏展示及视频个数显示
        """
        d = get_driver
        platform = get_platform
    
        # 准备测试环境
    
        # 导航到产品详情页
        navigation_success = self.navigate_to_product_detail(d, platform)
>       assert navigation_success, "Failed to navigate to product detail page"
E       AssertionError: Failed to navigate to product detail page
E       assert False

src/EC/tests/product/test_112812_pdp_video_features.py:80: AssertionError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='ft9.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>37.3762 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft9.2')">fail</a>
        <div id='div_ft9.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft9.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Failed to navigate to product detail page
assert False

error_message:self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x1085c90a0>
get_platform = 'iOS'
get_driver = <appium.webdriver.webdriver.WebDriver (session="69f180a8-765a-4bf6-9510-6a7d0b7cf734")>
setup = None

    @allure.title("iOS端PDP视频UI展示测试")
    @pytest.mark.parametrize('setup', [('test_112812_pdp_video_ui_display', )], indirect=True)
    def test_112812_pdp_video_ui_display(self, get_platform, get_driver, setup):
        """
        测试步骤：
        1. 进入产品详情页
        2. 验证视频栏展示及视频个数显示
        """
        d = get_driver
        platform = get_platform
    
        # 准备测试环境
    
        # 导航到产品详情页
        navigation_success = self.navigate_to_product_detail(d, platform)
>       assert navigation_success, "Failed to navigate to product detail page"
E       AssertionError: Failed to navigate to product detail page
E       assert False

src/EC/tests/product/test_112812_pdp_video_features.py:80: AssertionError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='pt9.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.6334 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt9.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>0.6334 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

    <tr class='failClass'>
        <td>
        测试步骤：
        1. 进入产品详情页
        2. 验证视频栏分类排序是否为 review > recipes > unboxing
        </td>
        <td>src/EC/tests/product/test_112812_pdp_video_features.py::TestPDPVideoFeatures::test_112812_video_categories_order[setup0]</td>
        <td>scene</td>
         <td>38.8252  s</td>
        <td>fail</td>
        <td><a href="javascript:showClassDetail('c.10',3)">detail</a></td>
    </tr>
    
<tr id='pt10.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>3.929 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt10.1' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>SetUp</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>3.929 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='ft10.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>34.8962 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft10.2')">fail</a>
        <div id='div_ft10.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft10.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Video categories are not in the expected order (review > recipes > unboxing)
assert False
 +  where False = <bound method ProductDetailPage.check_video_categories_order of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x108643c50>>()
 +    where <bound method ProductDetailPage.check_video_categories_order of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x108643c50>> = <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x108643c50>.check_video_categories_order

error_message:self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x108597290>
get_driver = <appium.webdriver.webdriver.WebDriver (session="69f180a8-765a-4bf6-9510-6a7d0b7cf734")>
get_platform = 'iOS', setup = None

    @allure.title("视频栏分类排序测试")
    @pytest.mark.parametrize('setup', [('test_002_video_categories_order', )], indirect=True)
    def test_112812_video_categories_order(self, get_driver, get_platform, setup):
        """
        测试步骤：
        1. 进入产品详情页
        2. 验证视频栏分类排序是否为 review > recipes > unboxing
        """
        # 此用例仅适用于iOS端,安卓滑这三个标签
        d = get_driver
        platform = get_platform
    
    
        # 初始化产品详情页对象
        pdp_page = ProductDetailPage(d, platform)
    
        # 验证视频分类排序
>       assert pdp_page.check_video_categories_order(), "Video categories are not in the expected order (review > recipes > unboxing)"
E       AssertionError: Video categories are not in the expected order (review > recipes > unboxing)
E       assert False
E        +  where False = <bound method ProductDetailPage.check_video_categories_order of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x108643c50>>()
E        +    where <bound method ProductDetailPage.check_video_categories_order of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x108643c50>> = <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x108643c50>.check_video_categories_order

src/EC/tests/product/test_112812_pdp_video_features.py:115: AssertionError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='ft10.2' class='hiddenRow'>
    <td class='failCase'>
        <div class='testcase'>Call</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>34.8962 s</div>
    </td>
    <td colspan='5' align='center' class='caseStatistics'>
        <!--css div popup start-->
        <a class="popup_link" href="javascript:void(0)" onclick="showLog('div_ft10.2')">fail</a>
        <div id='div_ft10.2' class="modal show" style="display: none; background-color: #000000c7;">
            <div class="modal-dialog modal-dialog-centered log_window">
                <div class="modal-content shadow-3">
                    <div class="modal-header">
                        <div>
                            <h5 class="mb-1">Call</h5>
                        </div>
                        <div>
                            <h5 class="mb-1">detailed log</h5>
                        </div>
                        <div>
                            <button type="button" class="btn btn-sm btn-square bg-tertiary bg-opacity-20 bg-opacity-100-hover text-tertiary text-white-hover" data-bs-dismiss="modal" onclick="hideLog('div_ft10.2')">X</button>
                        </div>
                    </div>
                    <div class="modal-body">
                        <div>
                            <pre>out_message:Video categories are not in the expected order (review > recipes > unboxing)
assert False
 +  where False = <bound method ProductDetailPage.check_video_categories_order of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x108643c50>>()
 +    where <bound method ProductDetailPage.check_video_categories_order of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x108643c50>> = <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x108643c50>.check_video_categories_order

error_message:self = <src.EC.tests.product.test_112812_pdp_video_features.TestPDPVideoFeatures object at 0x108597290>
get_driver = <appium.webdriver.webdriver.WebDriver (session="69f180a8-765a-4bf6-9510-6a7d0b7cf734")>
get_platform = 'iOS', setup = None

    @allure.title("视频栏分类排序测试")
    @pytest.mark.parametrize('setup', [('test_002_video_categories_order', )], indirect=True)
    def test_112812_video_categories_order(self, get_driver, get_platform, setup):
        """
        测试步骤：
        1. 进入产品详情页
        2. 验证视频栏分类排序是否为 review > recipes > unboxing
        """
        # 此用例仅适用于iOS端,安卓滑这三个标签
        d = get_driver
        platform = get_platform
    
    
        # 初始化产品详情页对象
        pdp_page = ProductDetailPage(d, platform)
    
        # 验证视频分类排序
>       assert pdp_page.check_video_categories_order(), "Video categories are not in the expected order (review > recipes > unboxing)"
E       AssertionError: Video categories are not in the expected order (review > recipes > unboxing)
E       assert False
E        +  where False = <bound method ProductDetailPage.check_video_categories_order of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x108643c50>>()
E        +    where <bound method ProductDetailPage.check_video_categories_order of <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x108643c50>> = <src.EC.page_objects.product.product_detail_page.ProductDetailPage object at 0x108643c50>.check_video_categories_order

src/EC/tests/product/test_112812_pdp_video_features.py:115: AssertionError</pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--css div popup end-->
    </td>
    <td></td>
</tr>

<tr id='pt10.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>3.1339 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

<tr id='pt10.3' class='hiddenRow'>
    <td class='passCase'>
        <div class='testcase'>TearDown</div>
    </td>
    <td style="color: #495057">
        <div></div>
    </td>
    <td style="color: #495057">
        <div>3.1339 s</div>
    </td>
    <td colspan='5' align='center'>pass</td>
    <td></td>
</tr>

            </tbody>
        </table>
    </div>
    <div class="card-footer border-0 py-5">
        <span class="text-muted text-sm">Cases Total:
            <button type="button"
                    class="btn btn-sm bg-dark bg-opacity-20 bg-opacity-100-hover text-dark text-white-hover">10</button> =
            <button type="button"
                    class="btn btn-sm bg-success bg-opacity-20 bg-opacity-100-hover text-success text-white-hover">5</button> +
            <button type="button"
                    class="btn btn-sm bg-warning bg-opacity-20 bg-opacity-100-hover text-warning text-white-hover">5</button> +
            <button type="button"
                    class="btn btn-sm bg-danger bg-opacity-20 bg-opacity-100-hover text-danger text-white-hover">0</button> +
            <button type="button"
                    class="btn btn-sm bg-secondary bg-opacity-20 bg-opacity-100-hover text-secondary text-white-hover">0</button>
        </span>
    </div>
</div>
<div style="height:120px"></div>



<footer class="footer" style="height: 50px; position: fixed; width: 100%">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-6">
                weeeTest 0.2.10.4c; 2023 © Weee! 版权所有
            </div>
        </div>
    </div>
</footer>

</body>

</html>